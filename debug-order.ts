#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

async function debugOrder(orderId: number) {
  try {
    console.log(`Debugging order ${orderId}...`);

    // Check if order exists
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        OrderItem: {
          include: {
            Product: true,
          },
        },
      },
    });

    if (!order) {
      console.log(`❌ Order ${orderId} not found`);
      return;
    }

    console.log(`✅ Found order ${orderId}:`);
    console.log(`  - Shipstation Order Number: ${order.shipstation_order_number}`);
    console.log(`  - Status: ${order.status}`);
    console.log(`  - Created: ${order.created_at}`);
    console.log(`  - Order Items: ${order.OrderItem.length}`);

    if (order.OrderItem.length === 0) {
      console.log(`❌ No order items found for order ${orderId}`);

      // Let's check if there are any order items in the database at all
      const allOrderItems = await prisma.orderItem.findMany({
        where: { orderId: orderId },
      });
      console.log(`  - Direct query found ${allOrderItems.length} order items`);

      if (allOrderItems.length > 0) {
        console.log(`  - Order items found with direct query:`);
        allOrderItems.forEach((item, index) => {
          console.log(
            `    ${index + 1}. ID: ${item.id}, Product ID: ${item.productId}, Quantity: ${item.quantity}`
          );
        });
      }

      // Check what order items exist in general
      const recentOrderItems = await prisma.orderItem.findMany({
        take: 10,
        orderBy: { id: 'desc' },
        include: { Product: true },
      });
      console.log(`\n📊 Recent order items in database (last 10):`);
      recentOrderItems.forEach((item, index) => {
        console.log(
          `  ${index + 1}. ID: ${item.id}, Order: ${item.orderId}, Product: ${item.Product?.name || 'Unknown'}`
        );
      });
    } else {
      console.log(`✅ Order items:`);
      order.OrderItem.forEach((item, index) => {
        console.log(
          `  ${index + 1}. ID: ${item.id}, Product: ${item.Product?.name || 'Unknown'}, Quantity: ${item.quantity}`
        );
        console.log(`     - Shipstation Line Item Key: ${item.shipstationLineItemKey}`);
      });
    }

    // Check existing print tasks
    const existingTasks = await prisma.printOrderTask.findMany({
      where: { orderId: orderId },
    });
    console.log(`\n📋 Existing print tasks: ${existingTasks.length}`);

    if (existingTasks.length > 0) {
      existingTasks.forEach((task, index) => {
        console.log(
          `  ${index + 1}. ID: ${task.id}, Text: ${task.custom_text}, Status: ${task.status}`
        );
      });
    }
  } catch (error) {
    console.error('Error debugging order:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Get order ID from command line
const orderId = parseInt(process.argv[2]);
if (!orderId) {
  console.error('Please provide an order ID: tsx debug-order.ts 32242');
  process.exit(1);
}

debugOrder(orderId);
