graph LR
    subgraph "src/scripts/"
        A[populate-print-queue.ts<br/>~1,500 lines<br/>Main orchestration]
        B[populate-print-queue.types.ts<br/>Type definitions]
        
        subgraph "database/"
            C[task-operations.ts<br/>~1,200 lines<br/>Database operations]
        end
    end
    
    subgraph "src/lib/"
        D[order-processing/index.ts<br/>Order utilities]
        E[shipstation/api.ts<br/>ShipStation API]
        F[constants/index.ts<br/>App constants]
        G[orders/amazon/customization.ts<br/>Amazon data extraction]
        H[product-mapping/index.ts<br/>Product mapping]
        I[ai/schemas.ts<br/>AI response schemas]
    end
    
    subgraph "External Services"
        J[OpenAI API<br/>GPT-4 Processing]
        K[ShipStation API<br/>Order management]
        L[MySQL Database<br/>Prisma ORM]
    end
    
    subgraph "File System"
        M[Prompt Files<br/>.txt templates]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J
    A --> K
    A --> L
    A --> M
    
    C --> B
    C --> D
    C --> E
    C --> L
    
    E --> K
    G --> J
    
    classDef mainScript fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef extracted fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef types fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef lib fill:#e8f5e8,stroke:#388e3c,stroke-width:1px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class A mainScript
    class C extracted
    class B types
    class D,E,F,G,H,I lib
    class J,K,L,M external