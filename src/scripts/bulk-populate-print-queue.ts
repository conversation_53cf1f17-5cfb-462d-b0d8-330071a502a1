#!/usr/bin/env ts-node

// Node built-in modules first
import fs from 'node:fs/promises';
import path from 'node:path';

// External dependencies
import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import { config } from 'dotenv';
import pino from 'pino';

// Project imports - using the same pattern as populate-print-queue.ts
import { productNameMappings, simplifyProductName } from '@/lib/product-mapping';
import { getOrdersToProcess, type OrderWithItemsAndProducts } from '@/lib/order-processing';
import { fetchAndProcessAmazonCustomization } from '@/lib/orders/amazon/customization';
import { fetchShipStationOrderDetails } from '@/lib/shipstation/api';
import {
  DEFAULT_OPENAI_MODEL,
  DEFAULT_SYSTEM_PROMPT_PATH,
  DEFAULT_USER_PROMPT_PATH,
  MAX_AI_TOKENS,
  OPENAI_API_URL,
  PRISMA_TRANSACTION_MAX_WAIT,
  PRISMA_TRANSACTION_TIMEOUT,
  SCRIPT_LOG_DIR,
} from '../lib/constants';

// Load environment variables
config();

// Setup logger
const logDir = path.resolve(process.cwd(), SCRIPT_LOG_DIR);
await fs.mkdir(logDir, { recursive: true });

const logger = pino({
  level: process.env.DEBUG ? 'debug' : 'info',
  transport: {
    targets: [
      {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
        },
        level: 'info',
      },
      {
        target: 'pino/file',
        options: {
          destination: path.join(logDir, 'bulk-populate-print-queue.log'),
        },
        level: 'debug',
      },
    ],
  },
});

const prisma = new PrismaClient({
  log: process.env.DEBUG ? ['query', 'info', 'warn', 'error'] : ['warn', 'error'],
});

// Configuration
const BATCH_SIZE = 25; // Smaller batches for better control
const DELAY_BETWEEN_BATCHES = 1000; // 1 second delay between batches

async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function processItemBatch(items: OrderItemWithDetails[], batchNumber: number): Promise<void> {
  logger.info(`Processing batch ${batchNumber} with ${items.length} items`);
  
  for (const item of items) {
    try {
      // Check if print task already exists
      const existingTask = await prisma.printOrderTask.findFirst({
        where: {
          orderItemId: item.id
        }
      });

      if (existingTask) {
        logger.debug(`Print task already exists for order item ${item.id}`);
        continue;
      }

      // Map SKU to product
      const product = mapSkuToProduct(item.sku);
      if (!product) {
        logger.warn(`No product mapping found for SKU: ${item.sku}`);
        continue;
      }

      // Extract personalization data
      let customText = '';
      let color1 = '';
      let color2 = '';
      let needsReview = false;
      let reviewReason = '';

      // For bulk orders, try to extract from Amazon customization first
      if (item.order.amazonCustomizationFile?.extracted_text) {
        const extractedData = item.order.amazonCustomizationFile.extracted_text;
        try {
          const parsed = JSON.parse(extractedData);
          customText = parsed.name || parsed.text || '';
          color1 = parsed.color || parsed.color1 || '';
          color2 = parsed.color2 || '';
        } catch (e) {
          // If not JSON, use as plain text
          customText = extractedData;
        }
      }

      // If no customization data, try AI extraction with smaller context
      if (!customText && (item.order.order_notes || item.orderItemOptions.length > 0)) {
        try {
          // Create a smaller, focused context for AI
          const contextData = {
            orderNotes: item.order.order_notes?.slice(0, 500) || '', // Limit notes length
            itemOptions: item.orderItemOptions.slice(0, 5), // Limit options
            itemName: item.name,
            sku: item.sku
          };

          const aiResult = await extractPersonalizationWithAI(
            contextData,
            item.order.id,
            'bulk-populate-print-queue'
          );

          if (aiResult.success) {
            customText = aiResult.data.name || aiResult.data.text || '';
            color1 = aiResult.data.color || aiResult.data.color1 || '';
            color2 = aiResult.data.color2 || '';
            
            if (aiResult.data.confidence && aiResult.data.confidence < 0.8) {
              needsReview = true;
              reviewReason = 'AI confidence below threshold';
            }
          } else {
            needsReview = true;
            reviewReason = 'AI extraction failed';
          }
        } catch (error) {
          logger.error(`AI extraction failed for item ${item.id}:`, error);
          needsReview = true;
          reviewReason = 'AI extraction error';
        }
      }

      // Create print task
      const printTask = await prisma.printOrderTask.create({
        data: {
          orderId: item.order.id,
          orderItemId: item.id,
          productId: product.id,
          status: needsReview ? 'pending_review' : 'pending',
          custom_text: customText,
          color_1: color1,
          color_2: color2,
          quantity: item.quantity,
          needs_review: needsReview,
          review_reason: reviewReason,
          stl_render_state: 'pending'
        }
      });

      logger.info(`Created print task ${printTask.id} for order item ${item.id} (${customText || 'no text'})`);

    } catch (error) {
      logger.error(`Error processing item ${item.id}:`, error);
      // Continue with next item rather than failing entire batch
    }
  }

  logger.info(`Completed batch ${batchNumber}`);
}

async function bulkPopulatePrintQueue(orderId?: number): Promise<void> {
  try {
    logger.info('Starting bulk populate print queue process...');

    // Build query conditions
    const whereConditions: any = {
      order: {
        order_status: {
          in: ['awaiting_shipment', 'on_hold', 'pending_fulfillment']
        }
      },
      printOrderTasks: {
        none: {} // No existing print tasks
      }
    };

    if (orderId) {
      whereConditions.order.id = orderId;
      logger.info(`Processing specific order: ${orderId}`);
    } else {
      // For bulk processing, limit to recent orders to avoid overwhelming
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7); // Last 7 days
      whereConditions.order.created_at = {
        gte: cutoffDate
      };
      logger.info('Processing orders from last 7 days');
    }

    // Get total count first
    const totalCount = await prisma.orderItem.count({
      where: whereConditions
    });

    logger.info(`Found ${totalCount} order items to process`);

    if (totalCount === 0) {
      logger.info('No items to process');
      return;
    }

    // Process in batches
    let processed = 0;
    let batchNumber = 1;

    while (processed < totalCount) {
      const items = await prisma.orderItem.findMany({
        where: whereConditions,
        include: {
          order: {
            include: {
              amazonCustomizationFile: true
            }
          },
          orderItemOptions: true
        },
        skip: processed,
        take: BATCH_SIZE,
        orderBy: {
          id: 'asc'
        }
      });

      if (items.length === 0) {
        break;
      }

      await processItemBatch(items, batchNumber);
      processed += items.length;
      batchNumber++;

      // Progress update
      const percentage = Math.round((processed / totalCount) * 100);
      logger.info(`Progress: ${processed}/${totalCount} (${percentage}%)`);

      // Delay between batches to avoid overwhelming APIs
      if (processed < totalCount) {
        logger.debug(`Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
        await sleep(DELAY_BETWEEN_BATCHES);
      }
    }

    logger.info(`Bulk populate print queue completed. Processed ${processed} items.`);

  } catch (error) {
    logger.error('Bulk populate print queue failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// CLI setup
const argv = yargs(hideBin(process.argv))
  .option('order-id', {
    type: 'number',
    description: 'Process specific order by ID'
  })
  .option('batch-size', {
    type: 'number',
    description: 'Number of items to process per batch',
    default: BATCH_SIZE
  })
  .help()
  .parseSync();

// Override batch size if provided
const batchSize = argv['batch-size'] || BATCH_SIZE;

// Run the script
bulkPopulatePrintQueue(argv['order-id'])
  .then(() => {
    logger.info('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Script failed:', error);
    process.exit(1);
  });
