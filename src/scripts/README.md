# Order Processing Cleanup Scripts

This directory contains scripts for cleaning up and managing orders in various states.

## cleanup-unconfirmed-orders.ts

This script identifies and processes orders that have been in "awaiting_payment" status for too long (considered "never confirmed") and:

1. Marks the order as "cancelled"
2. Updates all associated pending print tasks to "cancelled" status
3. Adds a note to the order indicating it was auto-cancelled

### Usage

```bash
# Run with default settings (7 days threshold)
npx tsx src/scripts/cleanup-unconfirmed-orders.ts

# Run with custom days threshold (e.g., 10 days)
npx tsx src/scripts/cleanup-unconfirmed-orders.ts --days=10

# Run in dry-run mode (no changes made)
npx tsx src/scripts/cleanup-unconfirmed-orders.ts --dry-run

# Combine options
npx tsx src/scripts/cleanup-unconfirmed-orders.ts --days=14 --dry-run
```

### Automation

This script is configured to run daily at 3 AM via cron job. See `docs/operations/crontab-updated.md` for details.

## Other Order Management Scripts

- **populate-print-queue.ts**: Processes orders and creates print tasks
- **sync-orders.ts**: Synchronizes orders from ShipStation to the local database
