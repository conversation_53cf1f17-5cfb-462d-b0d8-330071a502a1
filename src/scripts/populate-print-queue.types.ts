'use strict';

// External dependencies
import { PrintOrderTask_status } from '@prisma/client';
import { z } from 'zod';

// --- Zod Schemas ---
export const PersonalizationDetailSchema = z.object({
  customText: z.string().nullable(),
  color1: z.string().nullable(),
  color2: z.string().nullable().optional(),
  quantity: z.number().int().positive(),
  needsReview: z.boolean().optional().default(false),
  reviewReason: z.string().nullable(),
  annotation: z.string().nullable().optional(),
});

export const ItemPersonalizationResultSchema = z.object({
  personalizations: z.array(PersonalizationDetailSchema),
  overallNeedsReview: z.boolean(),
  overallReviewReason: z.string().nullable(),
});

export const AiOrderResponseSchema = z.object({
  itemPersonalizations: z.record(z.string(), ItemPersonalizationResultSchema),
});

// --- Types ---
export interface OrderDebugInfo {
  orderId: number;
  orderNumber: string;
  marketplace: string | null;
  overallStatus: string;
  promptSent: string | null;
  rawResponseReceived: string | null;
  parsedResponse: z.infer<typeof AiOrderResponseSchema> | null;
  processingError: string | null;
  aiProvider: string | null;
  modelUsed: string | null;
  items: Array<{
    itemId: number;
    status: string;
    error?: string;
    createdTaskIds: string[];
  }>;
  forceRecreate?: boolean;
  preserveText?: boolean;
  skipAi?: boolean;
}

export interface ProcessingOptions {
  orderId?: string;
  limit?: number;
  days?: number;
  openaiApiKey: string | null;
  openaiModel: string;
  systemPrompt: string;
  userPromptTemplate: string;
  systemPromptFile?: string;
  userPromptFile?: string;
  debug: boolean;
  verbose: boolean;
  logLevel: string;
  debugFile: string | undefined;
  forceRecreate?: boolean;
  createPlaceholder: boolean;
  confirm?: boolean;
  clearAll?: boolean;
  dryRun?: boolean;
  preserveText?: boolean;
  skipAi?: boolean;
  syncToShipstation?: boolean;
  shipstationSyncOnly?: boolean;
}

// Define local interfaces for AI prompt data structure
export interface AiOrderItemOption {
  name: string;
  value: string;
}

export interface AiOrderItemData {
  id: string; // This will be lineItemKey
  sku?: string | null;
  name?: string; // Simplified name
  quantity: number;
  options?: AiOrderItemOption[] | null;
  productName?: string | null; // Original product name
  productId?: number | null;
  // Properties to hold directly extracted Amazon data
  _amazonDataProcessed?: boolean;
  _amazonCustomText?: string | null;
  _amazonColor1?: string | null;
  _amazonColor2?: string | null;
  _amazonDataSource?: 'AmazonURL';
}

export interface AiPromptShippingAddress {
  name?: string | null;
  street1?: string | null;
  street2?: string | null;
  city?: string | null;
  state?: string | null;
  postalCode?: string | null;
  country?: string | null;
  phone?: string | null;
}

export interface AiPromptData {
  orderId: number;
  orderNumber: string;
  orderDate: string;
  marketplace: string;
  customerNotes?: string | null;
  internalNotes?: string | null;
  items: AiOrderItemData[];
  shippingAddress: AiPromptShippingAddress;
}

// Define a simpler type for what taskDetailsToCreate will actually hold
export interface TaskPersonalizationData {
  custom_text: string | null;
  color_1: string | null;
  color_2?: string | null | undefined;
  quantity: number;
  needs_review?: boolean;
  review_reason?: string | null;
  status: PrintOrderTask_status;
  annotation?: string | null | undefined;
}

export interface DirectExtractionResult {
  customText: string | null;
  color1: string | null;
  color2: string | null;
  dataSource: 'AmazonURL' | 'CustomerNotes' | null; // 'CustomerNotes' might be added later
  annotation: string | null;
  needsReview?: boolean;
  reviewReason?: string | null;
}

// Define more specific types for print_settings
export interface PrintSettingOption {
  name: string;
  value: string;
}

export type PrintSettings =
  | PrintSettingOption
  | PrintSettingOption[]
  | Record<string, string>
  | null;

// API-related interfaces
export interface ApiMessage {
  role: 'system' | 'user';
  content: string;
}

export interface ResponseFormat {
  type: 'json_object';
}

export interface ApiPayload {
  model: string;
  messages: ApiMessage[];
  temperature: number;
  max_tokens: number;
  response_format: ResponseFormat;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

// Database operation result types
export interface TaskCreationResult {
  tasksCreatedCount: number;
  tasksSkippedCount: number;
  itemsNeedReviewCount: number;
}

export interface ShipStationSyncResult {
  updatedCount: number;
  failedCount: number;
}
