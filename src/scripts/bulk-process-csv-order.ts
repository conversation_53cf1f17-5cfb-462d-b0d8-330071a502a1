#!/usr/bin/env tsx

/**
 * Specialized script for processing large CSV bulk orders
 * This script is optimized for handling the 400+ name CSV bulk order
 * that was causing token limit issues in the regular populate-print-queue script
 */

// External dependencies
import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

// Simple console logger to avoid pino transport issues
const logger = {
  info: (msg: string, ...args: unknown[]) => console.log(`[INFO] ${msg}`, ...args),
  warn: (msg: string, ...args: unknown[]) => console.warn(`[WARN] ${msg}`, ...args),
  error: (msg: string, ...args: unknown[]) => console.error(`[ERROR] ${msg}`, ...args),
  debug: (msg: string, ...args: unknown[]) => {
    if (process.env.DEBUG) console.log(`[DEBUG] ${msg}`, ...args);
  }
};

const prisma = new PrismaClient({
  log: process.env.DEBUG ? ['query', 'info', 'warn', 'error'] : ['warn', 'error'],
});

// Configuration
const BATCH_SIZE = 20; // Small batches to avoid overwhelming
const DELAY_BETWEEN_BATCHES = 1500; // 1.5 seconds between batches

interface OrderItemData {
  id: number;
  orderId: number;
  productId: number;
  quantity: number;
  order: {
    id: number;
    shipstation_order_number: string;
    amazonCustomizationFile?: {
      extracted_text?: string;
    } | null;
  };
}

async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Parse CSV data from Amazon customization file
 * Expected format: Name,Color1,Color2 (or similar)
 */
function parseCsvData(csvText: string): Array<{ name: string; color1?: string; color2?: string }> {
  try {
    const lines = csvText.split('\n').filter(line => line.trim());
    const results: Array<{ name: string; color1?: string; color2?: string }> = [];
    
    // Skip header row if it exists
    const dataLines = lines[0].toLowerCase().includes('name') ? lines.slice(1) : lines;
    
    for (const line of dataLines) {
      const parts = line.split(',').map(part => part.trim().replace(/"/g, ''));
      if (parts.length >= 1 && parts[0]) {
        results.push({
          name: parts[0],
          color1: parts[1] || '',
          color2: parts[2] || ''
        });
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error parsing CSV data:', error);
    return [];
  }
}

/**
 * Process a single order item and create print tasks based on CSV data
 */
async function processOrderItem(item: OrderItemData): Promise<number> {
  try {
    logger.info(`Processing order item ${item.id} from order ${item.order.shipstation_order_number}`);

    // Check if print tasks already exist
    const existingTasks = await prisma.printOrderTask.findMany({
      where: { orderItemId: item.id }
    });

    if (existingTasks.length > 0) {
      logger.info(`Skipping item ${item.id} - ${existingTasks.length} print tasks already exist`);
      return 0;
    }

    // Get CSV data from Amazon customization file
    if (!item.order.amazonCustomizationFile?.extracted_text) {
      logger.warn(`No CSV data found for order item ${item.id}`);
      return 0;
    }

    const csvData = parseCsvData(item.order.amazonCustomizationFile.extracted_text);
    if (csvData.length === 0) {
      logger.warn(`Could not parse CSV data for order item ${item.id}`);
      return 0;
    }

    logger.info(`Found ${csvData.length} personalization entries for order item ${item.id}`);

    // Create print tasks for each CSV entry
    const tasksToCreate = csvData.map((entry, index) => ({
      orderId: item.orderId,
      orderItemId: item.id,
      productId: item.productId,
      status: 'pending' as const,
      custom_text: entry.name,
      color_1: entry.color1 || '',
      color_2: entry.color2 || '',
      quantity: 1, // Each personalized item is quantity 1
      needs_review: false,
      stl_render_state: 'pending' as const,
      annotation: `Bulk CSV processing - Entry ${index + 1} of ${csvData.length}`
    }));

    // Create tasks in smaller batches to avoid overwhelming the database
    let tasksCreated = 0;
    const taskBatchSize = 10;
    
    for (let i = 0; i < tasksToCreate.length; i += taskBatchSize) {
      const batch = tasksToCreate.slice(i, i + taskBatchSize);
      
      const createdTasks = await prisma.printOrderTask.createMany({
        data: batch,
        skipDuplicates: true
      });
      
      tasksCreated += createdTasks.count;
      
      if (i + taskBatchSize < tasksToCreate.length) {
        await sleep(100); // Small delay between task batches
      }
    }

    logger.info(`Created ${tasksCreated} print tasks for order item ${item.id}`);
    return tasksCreated;

  } catch (error) {
    logger.error(`Error processing order item ${item.id}:`, error);
    return 0;
  }
}

/**
 * Main function to process bulk CSV orders
 */
async function processBulkCsvOrder(orderId?: number): Promise<void> {
  try {
    logger.info('Starting bulk CSV order processing...');

    // Query for order items that need processing
    const whereCondition = orderId ? { orderId } : {
      order: {
        order_status: {
          in: ['awaiting_shipment', 'on_hold', 'pending_fulfillment']
        },
        amazonCustomizationFile: {
          isNot: null
        }
      }
    };

    // Get total count
    const totalCount = await prisma.orderItem.count({
      where: {
        ...whereCondition,
        printOrderTasks: {
          none: {} // No existing print tasks
        }
      }
    });

    logger.info(`Found ${totalCount} order items to process`);

    if (totalCount === 0) {
      logger.info('No items to process');
      return;
    }

    // Process in batches
    let processed = 0;
    let totalTasksCreated = 0;
    let batchNumber = 1;

    while (processed < totalCount) {
      const items = await prisma.orderItem.findMany({
        where: {
          ...whereCondition,
          printOrderTasks: {
            none: {}
          }
        },
        include: {
          order: {
            include: {
              amazonCustomizationFile: true
            }
          }
        },
        skip: processed,
        take: BATCH_SIZE,
        orderBy: {
          id: 'asc'
        }
      });

      if (items.length === 0) {
        break;
      }

      logger.info(`Processing batch ${batchNumber} with ${items.length} items`);

      // Process each item in the batch
      for (const item of items) {
        const tasksCreated = await processOrderItem(item);
        totalTasksCreated += tasksCreated;
      }

      processed += items.length;
      batchNumber++;

      const percentage = Math.round((processed / totalCount) * 100);
      logger.info(`Progress: ${processed}/${totalCount} (${percentage}%) - ${totalTasksCreated} tasks created total`);

      // Delay between batches
      if (processed < totalCount) {
        logger.debug(`Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
        await sleep(DELAY_BETWEEN_BATCHES);
      }
    }

    logger.info(`Bulk CSV processing completed. Processed ${processed} items, created ${totalTasksCreated} print tasks.`);

  } catch (error) {
    logger.error('Bulk CSV processing failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// CLI setup
const args = process.argv.slice(2);
const orderId = args.includes('--order-id') ? 
  parseInt(args[args.indexOf('--order-id') + 1], 10) : undefined;

if (orderId && isNaN(orderId)) {
  logger.error('Invalid order ID provided');
  process.exit(1);
}

// Run the script
processBulkCsvOrder(orderId)
  .then(() => {
    logger.info('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Script failed:', error);
    process.exit(1);
  });
