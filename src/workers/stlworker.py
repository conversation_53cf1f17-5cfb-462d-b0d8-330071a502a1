#!/usr/bin/env python3
"""STL Render Worker (Python)

This script continuously monitors the database for print‑order tasks that
require STL generation.  For each pending task it invokes OpenSCAD with a
parameterised SCAD template, stores the resulting STL on disk, and updates the
record in the database.  The behaviour mirrors the original TypeScript worker
but uses the (async) Python Prisma client.

Prerequisites
-------------
* Python 3.10+
* prisma-client-py (https://github.com/RobertCraigie/prisma-client-py)
* OpenSCAD installed at the configured path (default: /usr/bin/openscad)
* The database schema must match that used by the original application.

Configuration can be supplied via environment variables – see the
`Settings` dataclass below.
"""

from __future__ import annotations

import asyncio
import os
import re
import shlex
import subprocess
import sys
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional

from prisma import Prisma  # type: ignore[import-not-found]
from prisma.models import PrintOrderTask  # generated by prisma-client-py
from prisma.enums import PrintTaskStatus, StlRenderStatus  # generated enums

# ---------------------------------------------------------------------------
# Configuration
# ---------------------------------------------------------------------------

@dataclass(slots=True, frozen=True)
class Settings:
    """Run‑time configuration for the worker.

    Values are read from the environment with sensible fallbacks so that the
    worker can run locally without additional configuration.
    """

    target_sku: str = os.getenv("TARGET_SKU", "PER-KEY3D-STY3-Y3D")
    max_retries: int = int(os.getenv("MAX_RETRIES", "3"))

    # Paths
    openscad_path: Path = Path(os.getenv("OPENSCAD_PATH", "/usr/bin/openscad"))
    scad_template_path: Path = Path(
        os.getenv("SCAD_TEMPLATE_PATH", Path(__file__).with_name("../../openscad/DualColour.scad").resolve()),
    )
    stl_output_dir_abs: Path = Path(
        os.getenv("STL_OUTPUT_DIR_ABS", Path(__file__).with_name("../../public/stl").resolve()),
    )
    # Path stored in the database (relative to the project root / public dir)
    stl_output_dir_relative: str = os.getenv("STL_OUTPUT_DIR_RELATIVE", "public/stl")


SETTINGS = Settings()

# ---------------------------------------------------------------------------
# Helper utilities
# ---------------------------------------------------------------------------

_slug_pattern = re.compile(r"[^a-z0-9-_]+")


def slug(value: str) -> str:
    """Return a filesystem‑safe, lower‑case slug version of *value*."""
    text = value.lower().strip()
    text = re.sub(r"\s+", "_", text)  # replace whitespace with underscores
    text = _slug_pattern.sub("", text)   # remove disallowed chars
    # Collapse repeated underscores / hyphens and strip affixes
    text = re.sub(r"[_-]{2,}", "_", text).strip("-_")
    return text or "untitled"


async def ensure_output_dir(path: Path) -> None:
    """Create *path* (and parents) if it does not already exist."""
    path.mkdir(parents=True, exist_ok=True)


# ---------------------------------------------------------------------------
# Database operations
# ---------------------------------------------------------------------------

prisma = Prisma()


async def reserve_task() -> Optional[PrintOrderTask]:
    """Reserve the oldest pending STL task for the configured SKU (atomic).

    The task's status is set to *running* so that other worker instances will
    ignore it.  Returns the reserved task instance or *None* if nothing is
    available.
    """
    async with prisma.tx() as tx:  # atomic section
        task = await tx.printorder_task.find_first(
            where={
                "status": PrintTaskStatus.pending,
                "stl_render_state": StlRenderStatus.pending,
                "product": {"sku": SETTINGS.target_sku},
            },
            order={"created_at": "asc"},
        )
        if not task:
            return None

        await tx.printorder_task.update(
            where={"id": task.id},
            data={
                "status": PrintTaskStatus.in_progress,
                "stl_render_state": StlRenderStatus.running,
            },
        )
        return task


# ---------------------------------------------------------------------------
# Worker logic
# ---------------------------------------------------------------------------

async def process_task(task: PrintOrderTask) -> None:
    """Generate an STL for *task* and update the database to reflect the outcome."""

    task_id = task.id
    started_at = datetime.now(timezone.utc).isoformat()
    print(f"[{started_at}] Processing task {task_id}…")

    # ------------------------------------------------------------------
    # 1. Prepare file system & input values
    # ------------------------------------------------------------------
    await ensure_output_dir(SETTINGS.stl_output_dir_abs)

    # Normalise the custom text: split by newline / slash / backslash and trim
    raw_lines = (task.custom_text or "").splitlines()
    # also break on “/” and “\” which often sneak through on form inputs
    expanded: list[str] = []
    for part in raw_lines:
        expanded.extend(re.split(r"[/\\]", part))
    lines = [ln.strip() for ln in expanded if ln.strip()][:3]  # max 3 lines
    line1, line2, line3 = (lines + ["", "", ""])[:3]

    colour1 = task.color_1 or "Black"
    colour2 = task.color_2 or "White"

    safe_name = slug(line1 or f"task_{task_id}")
    output_filename = f"task_{task_id}_{safe_name}.stl"

    output_path_abs = SETTINGS.stl_output_dir_abs / output_filename
    output_path_rel = f"{SETTINGS.stl_output_dir_relative}/{output_filename}"

    print(
        f"[" + datetime.now(timezone.utc).isoformat() + "] "
        f"Prepared data L1='{line1}' L2='{line2}' L3='{line3}' "
        f"Colours='{colour1}/{colour2}' → {output_path_abs}"
    )

    # ------------------------------------------------------------------
    # 2. Build and run the OpenSCAD command
    # ------------------------------------------------------------------
    def _esc(value: str) -> str:
        """Escape double quotes for the shell command."""
        return value.replace("\"", r"\\\"")

    cmd_parts = [
        str(SETTINGS.openscad_path),
        "-o",
        str(output_path_abs),
        str(SETTINGS.scad_template_path),
        "-D",
        f"text_line1=\"{_esc(line1)}\"",
        "-D",
        f"text_line2=\"{_esc(line2)}\"",
        "-D",
        f"text_line3=\"{_esc(line3)}\"",
        "-D",
        f"color1=\"{_esc(colour1)}\"",
        "-D",
        f"color2=\"{_esc(colour2)}\"",
    ]
    command = " ".join(shlex.quote(part) for part in cmd_parts)

    print(f"[" + datetime.now(timezone.utc).isoformat() + f"] Executing: {command}")

    try:
        proc = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout_b, stderr_b = await proc.communicate()
        stdout, stderr = stdout_b.decode(), stderr_b.decode()

        if stderr.strip():
            print(f"[" + datetime.now(timezone.utc).isoformat() + f"] OpenSCAD stderr:\n{stderr}")
        if stdout.strip():
            print(f"[" + datetime.now(timezone.utc).isoformat() + f"] OpenSCAD stdout:\n{stdout}")

        if proc.returncode != 0:
            raise RuntimeError(
                f"OpenSCAD exited with code {proc.returncode}\n{stderr}\n{stdout}"
            )

        # ------------------------------------------------------------------
        # 3. Update DB on success
        # ------------------------------------------------------------------
        await prisma.printorder_task.update(
            where={"id": task_id},
            data={
                "stl_path": output_path_rel,
                "stl_render_state": StlRenderStatus.completed,
                "annotation": None,
                "render_retries": 0,
                "status": PrintTaskStatus.completed,
            },
        )
        print(
            f"✓ [{datetime.now(timezone.utc).isoformat()}] "
            f"STL rendered for task {task_id} → {output_path_rel}"
        )

    except Exception as exc:
        # ------------------------------------------------------------------
        # 4. Failure handling & retry bookkeeping
        # ------------------------------------------------------------------
        next_retry = task.render_retries + 1
        out_of_retries = next_retry >= SETTINGS.max_retries

        err_text = str(exc)
        if len(err_text) > 1000:
            err_text = err_text[:1000] + "…"

        await prisma.printorder_task.update(
            where={"id": task_id},
            data={
                "render_retries": next_retry,
                "stl_render_state": (
                    StlRenderStatus.failed if out_of_retries else StlRenderStatus.pending
                ),
                "status": (
                    PrintTaskStatus.completed if out_of_retries else PrintTaskStatus.pending
                ),
                "annotation": f"STL render error ({next_retry}/{SETTINGS.max_retries}): {err_text}",
            },
        )

        print(
            f"✗ [{datetime.now(timezone.utc).isoformat()}] Failed task {task_id} "
            f"retry {next_retry}/{SETTINGS.max_retries} ({'fatal' if out_of_retries else 'will retry'})"
        )


# ---------------------------------------------------------------------------
# Main event‑loop
# ---------------------------------------------------------------------------


async def worker_loop() -> None:
    print(
        f"[{datetime.now(timezone.utc).isoformat()}] STL Render Worker started – "
        f"watching SKU ‘{SETTINGS.target_sku}’"
    )

    await prisma.connect()
    try:
        while True:
            print(f"[{datetime.now(timezone.utc).isoformat()}] Checking for tasks…")
            task: Optional[PrintOrderTask] = await reserve_task()

            if task:
                await process_task(task)
                # short respite when work has been done
                await asyncio.sleep(1)
            else:
                # nothing to do – nap for a bit
                await asyncio.sleep(15)

    finally:
        await prisma.disconnect()


# ---------------------------------------------------------------------------
# Entrypoint
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    try:
        asyncio.run(worker_loop())
    except KeyboardInterrupt:
        print("Interrupted by user – shutting down…", file=sys.stderr)
        sys.exit(0)
