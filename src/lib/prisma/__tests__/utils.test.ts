import { describe, it, expect } from 'vitest';

import { extractNumberValue } from '../utils';

import type { Prisma } from '@prisma/client';

describe('extractNumberValue', () => {
  it('returns number directly', () => {
    expect(extractNumberValue(5)).toBe(5);
  });

  it('handles set update', () => {
    const update: Prisma.IntFieldUpdateOperationsInput = { set: 10 };
    expect(extractNumberValue(update)).toBe(10);
  });

  it('handles increment update', () => {
    const update: Prisma.IntFieldUpdateOperationsInput = { increment: 3 };
    expect(extractNumberValue(update)).toBe(3);
  });

  it('falls back to default for invalid increment', () => {
    const update = { increment: 'bad' } as unknown as Prisma.IntFieldUpdateOperationsInput;
    expect(extractNumberValue(update, 2)).toBe(2);
  });
});
