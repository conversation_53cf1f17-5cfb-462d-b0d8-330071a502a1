import { PrismaClient, Prisma } from '@prisma/client';

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
//
// Learn more: https://pris.ly/d/help/next-js-best-practices

// Get a valid database URL, handling various edge cases in Vercel and other environments
function getValidDatabaseUrl(): string {
  // Set of known valid database URLs in order of preference
  const possibleUrls = [
    process.env.DATABASE_URL,
    process.env.DATABASE_URL_FALLBACK,
    process.env.MYSQL_URL,
    process.env.DB_URL,
  ];

  // Try each possible URL
  for (const url of possibleUrls) {
    if (!url) continue;

    let dbUrl = url.trim();

    // Skip URLs that contain unresolved variables
    if (dbUrl.includes('${') || dbUrl.includes('$DATABASE_URL')) {
      console.warn(`Skipping database URL with unresolved variable: ${dbUrl.substring(0, 10)}...`);
      continue;
    }

    // Add mysql:// prefix if missing
    if (!dbUrl.startsWith('mysql://')) {
      console.info('Adding mysql:// prefix to database URL');
      dbUrl = 'mysql://' + dbUrl;
    }

    // Verify the URL is properly formatted
    try {
      // Basic validation - URL should now have mysql:// prefix and contain @
      if (dbUrl.includes('@')) {
        // Log safely, e.g., up to the '@' or a fixed length if user/pass might be before it
        const atIndex = dbUrl.indexOf('@');
        const loggableUrl = atIndex !== -1 ? dbUrl.substring(0, atIndex) : dbUrl.substring(0, 15);
        console.log(`Using database URL starting with: ${loggableUrl}...`);
        // Update environment variable with valid URL
        process.env.DATABASE_URL = dbUrl;
        return dbUrl;
      }
    } catch (e) {
      console.error('Error validating database URL component:', e);
      // Continue to next URL if current one causes error during validation logic
    }
  }

  // If we get here, no valid URL was found
  throw new Error(
    'No valid database URL found. Please check .env, .env.local, or environment variables.'
  );
}

// Set DATABASE_URL to a valid value before Prisma initialization
// This will throw if no valid URL is found, preventing PrismaClient instantiation with bad config.
// Skip validation during build time to allow Next.js builds without database connection
const isBuildTime =
  process.env.NEXT_PHASE === 'phase-production-build' ||
  process.env.NODE_ENV === 'test' ||
  process.env.SKIP_DATABASE_VALIDATION === 'true' ||
  process.env.OFFLINE_MODE === 'true';

if (!isBuildTime) {
  try {
    getValidDatabaseUrl();
  } catch (error) {
    console.warn(
      'Database URL validation failed - continuing for offline/build mode',
      error instanceof Error ? error.message : error
    );
    // Don't throw during build or offline mode - just log the warning
    if (process.env.NODE_ENV === 'production' && !process.env.OFFLINE_MODE) {
      throw error;
    }
  }
}

declare global {
  // allow global `var` declarations
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

const prismaOptions: Prisma.PrismaClientOptions = {
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  transactionOptions: {
    maxWait: 120000, // 2 minutes in milliseconds
    timeout: 300000, // 5 minutes in milliseconds
  },
};

// Use globalThis for universal runtime compatibility
const g = globalThis as unknown as { prisma?: PrismaClient };

const prismaInstance = g.prisma ?? new PrismaClient(prismaOptions);

const logPrefix = '[lib/prisma]';

if (g.prisma) {
  console.info(`${logPrefix} Re-using existing PrismaClient instance`);
} else {
  console.info(`${logPrefix} Creating NEW PrismaClient instance`);
}

try {
  prismaInstance
    .$connect()
    .then(() => {
      console.info(`${logPrefix} Database connection established (eager)`);
    })
    .catch(err => {
      console.error(`${logPrefix} Initial eager database connection failed`, err);
      // Do **not** throw here – allow import to succeed so the default export is defined.
    });
} catch (err) {
  console.error(`${logPrefix} Unexpected error during eager connection`, err);
}

if (process.env.NODE_ENV !== 'production') {
  g.prisma = prismaInstance; // keep a single instance in dev
}

export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    throw new Error('[lib/prisma] PrismaClient is undefined – init failed');
  }
  return prismaInstance;
}

// Provide a named export so callers can `import { prisma }` consistently
export const prisma = prismaInstance;

export default prismaInstance;
