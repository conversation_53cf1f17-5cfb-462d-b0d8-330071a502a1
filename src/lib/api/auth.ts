import { NextApiRequest, NextApiResponse } from 'next';
import { NextRequest, NextResponse } from 'next/server';

type _NextApiHandler = (_req: NextApiRequest, _res: NextApiResponse) => Promise<void> | void;

// Type for App Router API handlers
type AppRouterApiHandler = (
  _req: NextRequest,
  _params?: { params: Record<string, string | string[]> }
) => Promise<NextResponse> | NextResponse;

const DEFAULT_API_KEY = 'your_default_static_api_key_here_change_me'; // CHANGE THIS IN YOUR ACTUAL CODE

export function with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(handler: AppRouterApiHandler): AppRouterApiHandler {
  return async (req: NextRequest, context?: { params: Record<string, string | string[]> }) => {
    const apiKeyHeader = req.headers.get('Authorization');
    const apiKey = apiKeyHeader?.split('ApiKey ')[1];
    let serverApiKey = process.env.EXTERNAL_PULL_API_KEY;

    if (!serverApiKey) {
      console.warn(
        '**************************************************************************************\n' +
          'WARNING: EXTERNAL_PULL_API_KEY is not set in environment variables.           \n' +
          `Using insecure default API key: ${DEFAULT_API_KEY}                               \n` +
          'Please set EXTERNAL_PULL_API_KEY in your .env file for production.             \n' +
          '**************************************************************************************'
      );
      serverApiKey = DEFAULT_API_KEY;
    }

    if (!apiKey || apiKey !== serverApiKey) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized: Invalid API Key.' },
        { status: 401 }
      );
    }

    // Pass context (which includes params) to the handler if it exists
    return context ? handler(req, context) : handler(req);
  };
}
