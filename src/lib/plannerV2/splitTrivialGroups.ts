import { PlannerJob } from './groupJobsBySku';

export interface SplitResult {
  readyTasks: PlannerJob[][];
  aiGroups: Planner<PERSON>ob[][];
}

const SINGLE_CAPACITY = 15;
const DUAL_CAPACITY = 6;
const MAX_COLORS = 4;
const MAX_COMBOS = 6;

export function splitTrivialGroups(groups: Record<string, PlannerJob[]>): SplitResult {
  const readyTasks: PlannerJob[][] = [];
  const aiGroups: PlannerJob[][] = [];

  for (const sku in groups) {
    const jobs = groups[sku];
    let total = 0;
    const colors = new Set<string>();
    const combos = new Set<string>();
    let dual = false;
    for (const j of jobs) {
      total += j.quantity;
      if (j.color1) colors.add(j.color1);
      if (j.color2) colors.add(j.color2);
      if (j.color2) dual = true;
      combos.add(`${j.color1 ?? 'null'}>${j.color2 ?? 'null'}`);
    }
    const within = !dual
      ? total <= SINGLE_CAPACITY && colors.size <= MAX_COLORS
      : total <= DUAL_CAPACITY && colors.size <= MAX_COLORS && combos.size <= MAX_COMBOS;
    if (within) {
      readyTasks.push(jobs);
    } else {
      aiGroups.push(jobs);
    }
  }

  return { readyTasks, aiGroups };
}
