import fs from 'fs/promises';
import path from 'path';

// eslint-disable-next-line import/no-named-as-default
import OpenA<PERSON> from 'openai';

import { TaskSequence } from './greedyBuild';
import { PlannerJob } from './groupJobsBySku';

const model = process.env.PLANNER_V2_MODEL || 'gpt-3.5-turbo-1106';
let concurrency = parseInt(process.env.PLANNER_V2_CONCURRENCY || '5', 10);
if (isNaN(concurrency) || concurrency <= 0) {
  concurrency = 5; // Fallback to a safe default
}

const systemPromptPath = path.join(
  process.cwd(),
  'src',
  'lib',
  'ai',
  'prompts-v2',
  'planner-system.txt'
);
const schemaPath = path.join(process.cwd(), 'src', 'lib', 'ai', 'prompts-v2', 'plan-schema.json');

let cachedSystem: string | null = null;
let cachedSchema: Record<string, unknown> | null = null;

async function loadPrompts() {
  if (!cachedSystem) {
    cachedSystem = await fs.readFile(systemPromptPath, 'utf8');
  }
  if (!cachedSchema) {
    const txt = await fs.readFile(schemaPath, 'utf8');
    cachedSchema = JSON.parse(txt);
  }
}

// Function removed as it was unused

async function callOpenAi(jobs: PlannerJob[]): Promise<TaskSequence | null> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) return null;
  await loadPrompts();
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  let delay = 500;
  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      const completion = await openai.chat.completions.create(
        {
          model,
          messages: [
            { role: 'system', content: cachedSystem as string },
            { role: 'user', content: JSON.stringify({ jobList: jobs }) },
          ],
          temperature: 0.2,
          response_format: { type: 'json_object' },
        },
        {
          timeout: 30000, // Move timeout to request options
        }
      );

      const content = completion.choices[0]?.message?.content;
      if (!content) throw new Error('No content in response');

      // Parse and validate the response
      const result = JSON.parse(content);
      const sequence = result.sequence as TaskSequence;
      return sequence;
    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed:`, error);
      if (attempt === 2) throw error; // Rethrow on last attempt
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }
  return null;
}

export async function planWithAi(jobs: PlannerJob[]): Promise<TaskSequence | null> {
  return callOpenAi(jobs);
}

export { concurrency as aiConcurrency };
