import { PlannerJob, groupJobsBySku } from './groupJobsBySku';

export interface AssignedJob {
  id: string;
  sku: string | null;
  quantity: number;
  color1: string | null;
  color2: string | null;
  customText: string | null;
}

export interface Task {
  taskNumber: number;
  colorsLoaded: string[];
  estimatedItemsOnPlate: number;
  assignedJobs: AssignedJob[];
}

export interface TaskSequence {
  metadata: { totalJobs: number; totalTasks: number };
  taskSequence: Task[];
}

const SINGLE_CAPACITY = 15;
const DUAL_CAPACITY = 6;
const MAX_COLORS = 4;
const MAX_COMBOS = 6;

function shouldStart(current: PlannerJob[], next: PlannerJob): boolean {
  if (current.length === 0) return false;
  const total = current.reduce((s, j) => s + j.quantity, 0) + next.quantity;
  const colors = new Set<string>();
  const combos = new Set<string>();
  let dual = false;
  for (const j of [...current, next]) {
    if (j.color1) colors.add(j.color1);
    if (j.color2) colors.add(j.color2);
    if (j.color2) dual = true;
    combos.add(`${j.color1 ?? 'null'}>${j.color2 ?? 'null'}`);
  }
  if (!dual) return total > SINGLE_CAPACITY || colors.size > MAX_COLORS;
  return total > DUAL_CAPACITY || colors.size > MAX_COLORS || combos.size > MAX_COMBOS;
}

function buildTask(jobs: PlannerJob[], num: number): Task {
  const colors = new Set<string>();
  jobs.forEach(j => {
    if (j.color1) colors.add(j.color1);
    if (j.color2) colors.add(j.color2);
  });
  const assigned: AssignedJob[] = jobs.map(j => ({
    id: j.id,
    sku: j.sku,
    quantity: j.quantity,
    color1: j.color1 ?? null,
    color2: j.color2 ?? null,
    customText: j.customText ?? null,
  }));
  return {
    taskNumber: num,
    colorsLoaded: Array.from(colors),
    estimatedItemsOnPlate: jobs.reduce((s, j) => s + j.quantity, 0),
    assignedJobs: assigned,
  };
}

export function greedyBuild(allJobs: PlannerJob[]): TaskSequence {
  const groups = groupJobsBySku(allJobs);
  const tasks: Task[] = [];
  let num = 1;
  for (const sku of Object.keys(groups)) {
    const jobs = groups[sku];
    let current: PlannerJob[] = [];
    for (const job of jobs) {
      if (shouldStart(current, job)) {
        tasks.push(buildTask(current, num++));
        current = [job];
      } else {
        current.push(job);
      }
    }
    if (current.length) tasks.push(buildTask(current, num++));
  }
  return { metadata: { totalJobs: allJobs.length, totalTasks: tasks.length }, taskSequence: tasks };
}
