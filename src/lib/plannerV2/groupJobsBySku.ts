export interface PlannerJob {
  id: string;
  sku: string | null;
  groupingSku: string;
  order?: {
    marketplace?: string | null;
    marketplace_order_number?: string | null;
    requested_shipping_service?: string | null;
  } | null;
  color1?: string | null;
  color2?: string | null;
  customText?: string | null;
  quantity: number;
  shipByDate?: string | null;
}

const WI_STYLE1_PREFIX = 'wi_395107128418';
const PER_KEY3D_STYLE1 = 'PER-KEY3D-STY1-Y3D';

export function canonicalSku(rawSku: string | null): string {
  if (!rawSku) return 'UNKNOWN_SKU';
  if (rawSku.startsWith(WI_STYLE1_PREFIX) || rawSku === PER_KEY3D_STYLE1) {
    return 'WI_STYLE1_KEYRING';
  }
  return rawSku;
}

export function groupJobsBySku(jobs: PlannerJob[]): Record<string, PlannerJob[]> {
  const grouped: Record<string, PlannerJob[]> = {};
  for (const job of jobs) {
    const key = canonicalSku(job.groupingSku ?? job.sku);
    const clonedJob = { ...job, groupingSku: key };
    if (!grouped[key]) grouped[key] = [];
    grouped[key].push(clonedJob);
  }
  return grouped;
}
