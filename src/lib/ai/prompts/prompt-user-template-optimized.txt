## Instructions

Process only the JSON data provided below. Ignore any previous messages or external information.

## Multi-Personalization Extraction

If the customer notes contain multiple names grouped by color pairings (e.g., "Primary Pink & Secondary Purple"), extract one personalization for each name, using the matching color group. Do NOT skip any names. Each name should result in a separate personalization entry.

## Example

If the notes contain:

🩷 Primary Pink & Secondary Purple
Josephine
Frankie
Liz

💙 Primary Blue & Secondary Green
Jayden
Bryson

You must return a personalization for each name, e.g.:

{
  "itemPersonalizations": {
    "<lineItemKey>": {
      "personalizations": [
        { "customText": "Josephine", "color1": "Pink", "color2": "Purple", ... },
        { "customText": "<PERSON>", "color1": "Pink", "color2": "Purple", ... },
        { "customText": "Liz", "color1": "Pink", "color2": "Purple", ... },
        { "customText": "Jayden", "color1": "Blue", "color2": "Green", ... },
        { "customText": "<PERSON><PERSON><PERSON>", "color1": "<PERSON>", "color2": "Green", ... }
      ]
    }
  }
}

## Actual Order Data

{INPUT_DATA_JSON}

## Output Format

Return ONLY a valid JSON object with the key "itemPersonalizations" as specified in the system instructions.

## Important

- Do not include any additional text, comments, or explanations in your response.
- Return only the JSON object without any markdown formatting.
- Ensure all required keys are present in each object, even if set to null.
- When a color is explicitly chosen in the item's options, use that exact color with no gender-based overrides or review warnings.
- Only apply gender-based color guidelines when the requested color is random or not specified at all.
