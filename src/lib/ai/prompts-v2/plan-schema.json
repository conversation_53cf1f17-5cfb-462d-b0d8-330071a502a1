{"name": "plan_task_sequence", "description": "Return optimized task sequence", "parameters": {"type": "object", "properties": {"taskSequence": {"type": "object", "properties": {"metadata": {"type": "object", "properties": {"totalJobs": {"type": "integer"}, "totalTasks": {"type": "integer"}}, "required": ["totalJobs", "totalTasks"]}, "taskSequence": {"type": "array", "items": {"type": "object", "properties": {"taskNumber": {"type": "integer"}, "colorsLoaded": {"type": "array", "items": {"type": "string"}, "maxItems": 4}, "estimatedItemsOnPlate": {"type": "integer"}, "assignedJobs": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "sku": {"type": ["string", "null"]}, "quantity": {"type": "integer"}, "color1": {"type": ["string", "null"]}, "color2": {"type": ["string", "null"]}, "customText": {"type": ["string", "null"]}}, "required": ["id", "quantity"]}}}, "required": ["taskNumber", "colorsLoaded", "estimatedItemsOnPlate", "<PERSON><PERSON><PERSON><PERSON>"]}}}, "required": ["metadata", "taskSequence"]}}, "required": ["taskSequence"]}}