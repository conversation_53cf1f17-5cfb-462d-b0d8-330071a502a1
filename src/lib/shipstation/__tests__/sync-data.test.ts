import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the shipstation client
vi.mock('../client', () => {
  const mockShipstationApi = {
    get: vi.fn(),
  };
  return {
    shipstationApi: mockShipstationApi,
  };
});

vi.mock('../db-sync', () => ({
  upsertOrderWithItems: vi.fn(),
}));

vi.mock('../../email/system-notifications', () => ({
  sendSystemNotification: vi.fn().mockResolvedValue(true),
  ErrorSeverity: { ERROR: 'ERROR', CRITICAL: 'CRITICAL', WARNING: 'WARNING' },
  ErrorCategory: { SYNC: 'SYNC', GENERAL: 'GENERAL' },
}));

import { sendSystemNotification } from '../../email/system-notifications';
import * as api from '../api';
import { shipstationApi } from '../client';
import { upsertOrderWithItems } from '../db-sync';

const mockOrder = {
  orderId: 1,
  orderNumber: '123',
  orderKey: null,
  orderDate: new Date().toISOString(),
  paymentDate: null,
  shipByDate: null,
  orderStatus: 'awaiting_shipment',
  customerId: null,
  customerUsername: null,
  customerEmail: null,
  billTo: {
    name: null,
    company: null,
    street1: null,
    street2: null,
    street3: null,
    city: null,
    state: null,
    postalCode: null,
    country: null,
    phone: null,
    residential: null,
    addressVerified: null,
  },
  shipTo: {
    name: null,
    company: null,
    street1: null,
    street2: null,
    street3: null,
    city: null,
    state: null,
    postalCode: null,
    country: null,
    phone: null,
    residential: null,
    addressVerified: null,
  },
  items: [],
  orderTotal: 0,
  taxAmount: null,
  shippingAmount: null,
  customerNotes: null,
  internalNotes: null,
};

const mockResponse = {
  orders: [mockOrder],
  total: 1,
  page: 1,
  pages: 1,
};

describe('syncShipstationData error handling', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    (shipstationApi.get as jest.Mock).mockResolvedValue({ data: mockResponse });
  });

  it('sets syncFailed when order upsert fails', async () => {
    (upsertOrderWithItems as jest.Mock).mockRejectedValue(new Error('fail'));

    const summary = await api.syncAllPaginatedOrders({ pageLimit: 1 });

    expect(summary.success).toBe(false);
    expect(summary.message).toContain('COMPLETED WITH ERRORS');
    expect(sendSystemNotification).toHaveBeenCalled();
  }, 10000);

  it('handles API errors', async () => {
    (shipstationApi.get as jest.Mock).mockRejectedValue(new Error('api fail'));

    const summary = await api.syncAllPaginatedOrders({ pageLimit: 1 });

    expect(summary.success).toBe(false);
    expect(summary.message).toContain('ShipStation sync FAILED');
    expect(sendSystemNotification).toHaveBeenCalled();
  }, 10000);
});
