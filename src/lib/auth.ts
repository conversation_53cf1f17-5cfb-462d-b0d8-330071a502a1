import { PrismaAdapter } from '@next-auth/prisma-adapter';
import bcrypt from 'bcryptjs';
import NextAuth, {
  Account,
  DefaultSession,
  DefaultUser,
  NextAuthOptions,
  User as NextAuthUserType,
  Profile,
} from 'next-auth';
import { AdapterUser } from 'next-auth/adapters';
import { JWT } from 'next-auth/jwt';
// eslint-disable-next-line import/no-named-as-default
import CredentialsProvider from 'next-auth/providers/credentials';

// eslint-disable-next-line import/no-named-as-default
import prisma from '@/lib/prisma';

// Helper function to safely access environment variables
const getEnvVariable = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    if (process.env.NODE_ENV === 'production' && key !== 'NEXTAUTH_SECRET') {
      console.error(`FATAL ERROR: Environment variable ${key} is not set.`);
      throw new Error(`FATAL ERROR: Environment variable ${key} is not set.`);
    }
    return '';
  }
  return value || defaultValue || '';
};

// Define custom user and session types
interface CustomNextAuthUser extends DefaultUser {
  id: string; // Corresponds to token.sub, always a string
  db_id?: number; // To store the original database integer ID
  // Add other custom properties you might extract from dbUser and put on the token/session
  // role?: string;
}

interface CustomNextAuthSession extends DefaultSession {
  user: CustomNextAuthUser;
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) {
          return null;
        }
        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        });

        if (user && user.password && bcrypt.compareSync(credentials.password, user.password)) {
          return {
            id: user.id.toString(), // Important: ID must be a string for CredentialsProvider
            name: user.name,
            email: user.email,
            image: user.image,
            emailVerified: user.emailVerified || null,
          };
        }
        return null;
      },
    }),
  ],
  session: {
    strategy: 'jwt' as const,
  },
  callbacks: {
    async jwt({
      token,
      user,
      account: _account,
      profile: _profile,
      trigger: _trigger,
      isNewUser: _isNewUser,
    }: {
      token: JWT;
      user?: AdapterUser | NextAuthUserType; // user is from authorize (now string ID) or AdapterUser from OAuth
      account?: Account | null;
      profile?: Profile | null;
      trigger?: 'signIn' | 'signUp' | 'update';
      isNewUser?: boolean;
    }) {
      // On initial sign-in, 'user' is populated.
      if (user) {
        // token.sub is automatically set to user.id (string) by NextAuth.
        // Store the numeric database ID if available.
        // The `user` object from `authorize` now has `id` as string.
        token.db_id = parseInt(user.id, 10);
        token.email = user.email;
        token.name = user.name;
        token.picture = user.image;
      }

      // On subsequent calls, we might want to refresh data if critical, or rely on initial token population.
      // For instance, if db_id is missing for some reason and token.sub exists:
      if (token.sub && typeof token.db_id === 'undefined') {
        const dbUser = await prisma.user.findUnique({
          where: { id: parseInt(token.sub, 10) }, // Prisma User.id is Int
        });
        if (dbUser) {
          token.db_id = dbUser.id;
          // Re-populate if necessary, though typically handled by initial sign-in.
          token.email = dbUser.email ?? token.email;
          token.name = dbUser.name ?? token.name;
          token.picture = dbUser.image ?? token.picture;
        }
      }
      return token;
    },
    async session({
      session,
      token,
    }: {
      session: CustomNextAuthSession;
      token: JWT;
    }): Promise<CustomNextAuthSession> {
      if (session.user) {
        session.user.id = token.sub as string; // Standard way: session.user.id is token.sub (string)
        session.user.db_id = token.db_id as number | undefined;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.image = token.picture;
        // If you had roles/agencyId in token, you would set them here:
        // session.user.role = token.role as string | undefined;
        // session.user.agencyId = token.agencyId as string | undefined;
      }
      return session;
    },
  },
  secret: getEnvVariable('NEXTAUTH_SECRET'),
  pages: {
    signIn: '/login',
  },
  debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions);
