// Interface for the render settings file structure
export interface RenderSettings {
  parameterSets: Record<string, Record<string, string | number | boolean | Array<string | number>>>;
  fileFormatVersion: string;
  // OpenSCAD rendering quality settings
  fa?: number; // Fragment angle
  fs?: number; // Fragment size
  fn?: number; // Fragment number
}

// OpenSCAD rendering options
export interface OpenSCADRenderOptions {
  /** Variables to pass to OpenSCAD using -D name=value */
  variables?: Record<string, string | number>;
  /** Directory to write the STL into (absolute or relative). Defaults to ./public/stl */
  outputDir?: string;
  /** Custom file name (without path). If omitted, one is autogenerated */
  fileName?: string;
}
