'use client';

import { PrintOrderTask_status } from '@prisma/client';
import {
  addDays,
  endOfDay,
  endOfToday,
  endOfWeek,
  endOfYesterday,
  format,
  startOfDay,
  startOfToday,
  startOfWeek,
  startOfYesterday,
} from 'date-fns';
import debounce from 'lodash.debounce';
import { CalendarIcon, RotateCcw, X } from 'lucide-react'; // Import icons
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState, useTransition } from 'react';
import { DateRange } from 'react-day-picker';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

const colorOptions = [
  'Black',
  'Grey',
  'Light Blue',
  'Blue',
  'Dark Blue',
  'Brown',
  'Orange',
  'Matt Orange',
  'Silk Orange',
  'Red',
  'Fire Engine Red',
  'Rose Gold',
  'Magenta',
  'White',
  'Yellow',
  'Silver',
  'Silk Silver',
  'Purple',
  'Pink',
  'Gold',
  'Skin',
  'Peak Green',
  'Green',
  'Olive Green',
  'Pine Green',
  'Cold White',
  'Matt Pink',
  'Silk Pink',
  'Glow in the Dark',
  'Bronze',
  'Beige',
  'Turquoise',
];

interface PrintQueueFiltersProps {
  currentFilters: {
    status?: string | string[];
    needsReview?: string | string[];
    query?: string | string[];
    shipByDateStart?: string | string[];
    shipByDateEnd?: string | string[];
    color1?: string | string[];
    color2?: string | string[];
    color?: string | string[];
    shippingMethod?: string | string[];
  };
  availableProductNames?: string[];
  availableShippingMethods?: string[];
}

// Helper to get the first value if it's an array
const getFilterParam = (value: string | string[] | undefined): string | undefined => {
  return Array.isArray(value) ? value[0] : value;
};

// Type guard for PrintOrderTask_status
function isPrintOrderTask_status(value: string): value is PrintOrderTask_status {
  // Check if value is one of the enum keys
  return Object.values(PrintOrderTask_status).includes(value as PrintOrderTask_status);
}

// Type guard for extended status including 'active'
function isExtendedPrintOrderTask_status(
  value: string
): value is PrintOrderTask_status | 'active' | 'all' {
  return isPrintOrderTask_status(value) || value === 'all' || value === 'active';
}

// Type guard for Review Option
function isReviewOption(value: string): value is 'yes' | 'no' | 'all' {
  return ['yes', 'no', 'all'].includes(value);
}

export function PrintQueueFilters({
  currentFilters,
  availableProductNames: _availableProductNames = [],
  availableShippingMethods = [],
}: PrintQueueFiltersProps) {
  const shippingMethods = availableShippingMethods; // Use the passed prop
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  // Local state for controlled inputs
  const initialQueryParam = getFilterParam(currentFilters.query) || '';
  const [query, setQuery] = useState(initialQueryParam);

  const initialStatusParam = getFilterParam(currentFilters.status) || 'active';
  const initialValidatedStatus = isExtendedPrintOrderTask_status(initialStatusParam)
    ? initialStatusParam
    : 'active';
  const [status, setStatus] = useState<PrintOrderTask_status | 'all' | 'active'>(
    initialValidatedStatus as PrintOrderTask_status | 'all' | 'active'
  );

  // Initialize needsReview state from currentFilters, defaulting to 'all' only if currentFilters.needsReview is undefined
  // Initialize needsReview state from currentFilters, defaulting to 'no' if currentFilters.needsReview is undefined
  // Initialize needsReview state from currentFilters, defaulting to 'no' if currentFilters.needsReview is undefined
  // Initialize needsReview state from currentFilters, defaulting to 'no' if currentFilters.needsReview is undefined
  // Initialize needsReview state from currentFilters, defaulting to 'no' if currentFilters.needsReview is undefined
  const initialNeedsReviewParam = getFilterParam(currentFilters.needsReview);
  const initialValidatedNeedsReview =
    initialNeedsReviewParam && isReviewOption(initialNeedsReviewParam)
      ? initialNeedsReviewParam
      : 'no'; // Default to 'no' if param is missing or invalid
  const [needsReview, setNeedsReview] = useState<'yes' | 'no' | 'all'>(initialValidatedNeedsReview);

  // Color filters
  const initialColor1Param = getFilterParam(currentFilters.color1) || '';
  const [color1, setColor1] = useState<string>(initialColor1Param); // Explicitly type as string

  const initialColor2Param = getFilterParam(currentFilters.color2) || '';
  const [color2, setColor2] = useState<string>(initialColor2Param); // Explicitly type as string

  const initialColorParam = getFilterParam(currentFilters.color) || '';
  const [color, setColor] = useState(initialColorParam);

  // Shipping Method filter
  const initialShippingMethodParam = getFilterParam(currentFilters.shippingMethod) || '';
  const [shippingMethod, setShippingMethod] = useState(initialShippingMethodParam);

  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    const startStr = getFilterParam(currentFilters.shipByDateStart);
    const endStr = getFilterParam(currentFilters.shipByDateEnd);
    // Ensure dates are valid before setting
    const start = startStr && !isNaN(new Date(startStr).getTime()) ? new Date(startStr) : undefined;
    const end = endStr && !isNaN(new Date(endStr).getTime()) ? new Date(endStr) : undefined;
    if (start || end) {
      return { from: start, to: end };
    }
    return undefined;
  });

  // Check if any filter is currently active
  const isAnyFilterActive = useMemo(() => {
    return (
      query !== '' ||
      status !== 'all' ||
      needsReview !== 'all' ||
      dateRange?.from !== undefined ||
      dateRange?.to !== undefined ||
      color1 !== '' || // Check if color1 is not an empty string (includes 'none')
      color2 !== '' || // Check if color2 is not an empty string (includes 'none')
      color !== '' ||
      shippingMethod !== ''
    );
  }, [query, status, needsReview, dateRange, color1, color2, color, shippingMethod]);

  // Update URL search params function
  const updateSearchParams = useCallback(
    (newParams: Record<string, string | undefined>, clearAll = false) => {
      console.log('PrintQueueFilters: Updating search params:', {
        newParams,
        clearAll,
        currentSearchParams: Object.fromEntries(searchParams.entries()),
      });

      let current: URLSearchParams;
      if (clearAll) {
        // Start fresh if clearing all, keep only non-filter params like limit
        current = new URLSearchParams();
        const limit = searchParams.get('limit');
        if (limit) current.set('limit', limit);
      } else {
        current = new URLSearchParams(Array.from(searchParams.entries()));
      }

      Object.entries(newParams).forEach(([key, value]) => {
        // Trim the query parameter if it exists
        const processedValue = key === 'query' && value ? value.trim() : value;

        if (processedValue) {
          // Always set the parameter value, even if it's 'all'
          current.set(key, processedValue);
        } else {
          // Only delete if the value is null/undefined/empty string
          if (!clearAll) {
            current.delete(key);
          }
        }
      });

      // Always delete page when filters change or are cleared
      current.delete('page');

      const search = current.toString();
      const query = search ? `?${search}` : '';

      console.log('PrintQueueFilters: Final URL will be:', `${pathname}${query}`);

      startTransition(() => {
        router.push(`${pathname}${query}`, { scroll: false }); // Prevent scroll jump
      });
    },
    [pathname, router, searchParams]
  );

  // Debounced search input handler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedUpdateSearch = useCallback(
    debounce((value: string) => {
      updateSearchParams({ query: value });
    }, 500),
    [updateSearchParams] // Dependency array is correct based on usage
  );

  // Handler for search input change
  const handleQueryChange = (event: { target: { value: string } }) => {
    const value = event.target.value;
    setQuery(value);
    // Pass the value as-is to state for UI, but trim for the actual search
    debouncedUpdateSearch(value);
  };

  // Handler for status select change
  const handleStatusChange = (value: string) => {
    const newStatus = isExtendedPrintOrderTask_status(value) ? value : 'active';
    console.log('PrintQueueFilters: Status changed to:', {
      rawValue: value,
      validatedStatus: newStatus,
      isExtendedPrintOrderTask_status: isExtendedPrintOrderTask_status(value),
    });
    setStatus(newStatus as PrintOrderTask_status | 'all' | 'active');

    // Always include the status parameter in the URL, even for 'all'
    updateSearchParams({ status: newStatus });
    console.log(`PrintQueueFilters: Setting status parameter to "${newStatus}"`);
  };

  // Handler for needs review select change
  const handleNeedsReviewChange = (value: string) => {
    const newReview = value === 'yes' || value === 'no' ? value : 'all';
    setNeedsReview(newReview);
    updateSearchParams({ needsReview: newReview });
  };

  // Handler for Date Range Select
  const handleDateRangeSelect = (range: DateRange | undefined) => {
    setDateRange(range);
    const start = range?.from ? format(range.from, 'yyyy-MM-dd') : undefined;
    const end = range?.to ? format(range.to, 'yyyy-MM-dd') : undefined;
    updateSearchParams({ shipByDateStart: start, shipByDateEnd: end });
  };

  // Quick presets for Ship By Date
  const handlePresetClick = (preset: 'today' | 'yesterday' | 'tomorrow' | 'nextWeek') => {
    let range: DateRange | undefined;
    switch (preset) {
      case 'today':
        range = { from: startOfToday(), to: endOfToday() };
        break;
      case 'yesterday':
        range = { from: startOfYesterday(), to: endOfYesterday() };
        break;
      case 'tomorrow': {
        const baseTomorrow = addDays(new Date(), 1);
        range = { from: startOfDay(baseTomorrow), to: endOfDay(baseTomorrow) };
        break;
      }
      case 'nextWeek': {
        const base = addDays(new Date(), 7);
        range = { from: startOfWeek(base), to: endOfWeek(base) };
        break;
      }
      default:
        range = undefined;
    }
    handleDateRangeSelect(range);
  };

  // Handler for shipping method select change
  const handleShippingMethodChange = (value: string): void => {
    setShippingMethod(value);
    updateSearchParams({ shippingMethod: value || undefined });
  };

  // Clear search input
  const clearSearch = (): void => {
    setQuery('');
    updateSearchParams({ query: undefined }); // Pass undefined to clear
  };

  // Clear date range
  const clearDateRange = (): void => {
    handleDateRangeSelect(undefined); // Reuse existing handler
  };

  // Clear All Filters
  const handleClearAllFilters = (): void => {
    setQuery('');
    setStatus('all');
    setNeedsReview('all');
    setDateRange(undefined);
    setColor1(''); // Reset color1 state to empty string
    setColor2(''); // Reset color2 state to empty string
    setColor('');
    setShippingMethod('');
    // Call update with all filters explicitly set to undefined/default
    updateSearchParams(
      {
        query: undefined,
        status: undefined, // Will be deleted as it maps to 'all'
        needsReview: undefined, // Will be deleted
        shipByDateStart: undefined,
        shipByDateEnd: undefined,
        color1: undefined,
        color2: undefined,
        color: undefined,
        shippingMethod: undefined,
      },
      true
    ); // Pass true to indicate clearing all
  };

  // Sync local state
  // Sync local state with search params when search params change
  useEffect(() => {
    // Only update state if the search param is different from the current state
    const currentQueryParam = searchParams.get('query') || '';
    if (query !== currentQueryParam) {
      setQuery(currentQueryParam);
    } // Added missing closing curly brace

    const statusParam = searchParams.get('status') || 'active'; // Default to 'active'
    const validatedStatusParam = isExtendedPrintOrderTask_status(statusParam)
      ? statusParam
      : 'active';
    if (status !== validatedStatusParam) {
      setStatus(validatedStatusParam as PrintOrderTask_status | 'all' | 'active');
    }

    // needsReview state is initialized from props, no need to sync from searchParams here

    const start = searchParams.get('shipByDateStart');
    const end = searchParams.get('shipByDateEnd');
    const startDate = start && !isNaN(new Date(start).getTime()) ? new Date(start) : undefined;
    const endDate = end && !isNaN(new Date(end).getTime()) ? new Date(end) : undefined;

    // Check if dateRange needs updating to avoid unnecessary re-renders
    const currentStartDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined;
    const currentEndDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined;

    if (currentStartDate !== start || currentEndDate !== end) {
      setDateRange({ from: startDate, to: endDate });
    }

    const color1Param = searchParams.get('color1') || '';
    if (color1 !== color1Param) {
      setColor1(color1Param);
    }

    const color2Param = searchParams.get('color2') || '';
    if (color2 !== color2Param) {
      setColor2(color2Param);
    }

    const colorParam = searchParams.get('color') || '';
    if (color !== colorParam) {
      setColor(colorParam);
    }

    const shippingMethodParam = searchParams.get('shippingMethod') || '';
    if (shippingMethod !== shippingMethodParam) {
      setShippingMethod(shippingMethodParam);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]); // Only run effect when searchParams change
  const statusOptions = [
    { value: 'active', label: 'Active Tasks' },
    { value: 'all', label: 'All Statuses' },
    ...Object.values(PrintOrderTask_status).map(s => ({
      value: s,
      label: s.charAt(0).toUpperCase() + s.slice(1).replace('_', ' '),
    })),
  ];

  const reviewOptions = [
    { value: 'all', label: 'Review: Any' },
    { value: 'yes', label: 'Review: Yes' },
    { value: 'no', label: 'Review: No' },
  ];

  return (
    <div className="flex flex-wrap items-start gap-3 mb-4 p-3 border rounded-md bg-muted/40 relative">
      {/* Clear All Button - positioned top-right */}
      {isAnyFilterActive && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearAllFilters}
          className="absolute top-2 right-2 text-xs text-muted-foreground hover:text-foreground"
          title="Clear all filters"
        >
          <RotateCcw className="mr-1 h-3 w-3" />
          Clear All
        </Button>
      )}

      {/* Search Input */}
      <div className="flex-grow min-w-[200px] max-w-xs relative">
        <Label htmlFor="search-query" className="mb-1 block text-xs font-medium">
          Search (Product, SKU, Order#)
        </Label>
        <div>
          <Input
            id="search-query"
            placeholder="Enter search term..."
            value={query}
            onChange={handleQueryChange}
            className="pr-8 h-9" // Add padding for clear button and match height
            // Add onBlur handler to trim spaces
            onBlur={() => {
              if (query.trim() !== query) {
                setQuery(query.trim());
                debouncedUpdateSearch(query.trim());
              }
            }}
          />
          <p className="text-[10px] text-muted-foreground mt-1 whitespace-nowrap">
            Search by ID, text, color, or marketplace order number
          </p>
        </div>
        {query && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-[29px] h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
            onClick={clearSearch}
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Status Select */}
      <div>
        <Label htmlFor="status-filter" className="mb-1 block text-xs font-medium">
          Status
        </Label>
        <Select value={status} onValueChange={handleStatusChange}>
          <SelectTrigger id="status-filter" className="w-[160px] h-9">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            {statusOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Needs Review Select */}
      <div>
        <Label htmlFor="review-filter" className="mb-1 block text-xs font-medium">
          Needs Review
        </Label>
        <Select value={needsReview} onValueChange={handleNeedsReviewChange}>
          <SelectTrigger id="review-filter" className="w-[160px] h-9">
            <SelectValue placeholder="Filter by review" />
          </SelectTrigger>
          <SelectContent>
            {reviewOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Date Range Picker with Clear Button */}
      <div className="flex items-end gap-1">
        <div>
          <Label htmlFor="date-range" className="mb-1 block text-xs font-medium">
            Ship By Date
          </Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date-range"
                variant={'outline'}
                className={cn(
                  'w-[240px] h-9 justify-start text-left font-normal text-sm', // Increased width to fit end date
                  !dateRange?.from && !dateRange?.to && 'text-muted-foreground' // Adjust condition
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, 'LLL dd, y')} - {format(dateRange.to, 'LLL dd, y')}
                    </>
                  ) : (
                    format(dateRange.from, 'LLL dd, y')
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-2 flex space-x-2">
                <Button variant="ghost" size="sm" onClick={() => handlePresetClick('today')}>
                  Today
                </Button>
                <Button variant="ghost" size="sm" onClick={() => handlePresetClick('tomorrow')}>
                  Tomorrow
                </Button>
                <Button variant="ghost" size="sm" onClick={() => handlePresetClick('yesterday')}>
                  Yesterday
                </Button>
                <Button variant="ghost" size="sm" onClick={() => handlePresetClick('nextWeek')}>
                  Next Week
                </Button>
              </div>
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={handleDateRangeSelect}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>
        {/* Clear Date Range Button */}
        {(dateRange?.from || dateRange?.to) && (
          <Button
            variant="ghost"
            size="icon"
            onClick={clearDateRange}
            className="h-9 w-9 p-0 text-muted-foreground hover:text-foreground"
            aria-label="Clear date range"
            title="Clear date range"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Color 1 Filter */}
      <div>
        <Label htmlFor="color1-filter" className="mb-1 block text-xs font-medium">
          Color 1
        </Label>
        <Select
          value={color1 || 'all'} // Use 'all' as the default display value
          onValueChange={value => {
            const newValue = value === 'all' ? '' : value; // Map 'all' to empty string for internal state/param
            setColor1(newValue);
            // If value is 'none', set param to 'none'. If 'all', set param to undefined. Otherwise, set param to value.
            updateSearchParams({ color1: value === 'all' ? undefined : value });
          }}
        >
          <SelectTrigger id="color1-filter" className="w-[160px] h-9">
            <SelectValue placeholder="All Colors" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Colors</SelectItem>
            <SelectItem value="none">None</SelectItem> {/* Added None option */}
            {colorOptions.map(colorOption => (
              <SelectItem key={colorOption} value={colorOption}>
                {colorOption}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Color 2 Filter */}
      <div>
        <Label htmlFor="color2-filter" className="mb-1 block text-xs font-medium">
          Color 2
        </Label>
        <Select
          value={color2 || 'all'} // Use 'all' as the default display value
          onValueChange={value => {
            const newValue = value === 'all' ? '' : value; // Map 'all' to empty string for internal state/param
            setColor2(newValue);
            // If value is 'none', set param to 'none'. If 'all', set param to undefined. Otherwise, set param to value.
            updateSearchParams({ color2: value === 'all' ? undefined : value });
          }}
        >
          <SelectTrigger id="color2-filter" className="w-[160px] h-9">
            <SelectValue placeholder="All Colors" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Colors</SelectItem>
            <SelectItem value="none">None</SelectItem> {/* Added None option */}
            {colorOptions.map(colorOption => (
              <SelectItem key={colorOption} value={colorOption}>
                {colorOption}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Shipping Method Filter */}
      <div>
        <Label htmlFor="shipping-method-filter" className="mb-1 block text-xs font-medium">
          Shipping Method
        </Label>
        <Select value={shippingMethod} onValueChange={handleShippingMethodChange}>
          <SelectTrigger className="w-[180px] h-9 text-xs">
            <SelectValue placeholder="All Shipping Methods" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Shipping Methods</SelectItem>
            {shippingMethods.map(method => (
              <SelectItem key={method} value={method}>
                {method}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Loading Indicator */}
      {isPending && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <p>Loading...</p>
        </div>
      )}
    </div>
  );
}
