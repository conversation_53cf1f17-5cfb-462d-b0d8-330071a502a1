'use client';

import { PrintOrderTask, PrintOrderTask_status, Product as PrismaProduct } from '@prisma/client';
import {
  ColumnDef,
  ColumnFiltersState,
  Row,
  RowSelectionState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { format, isToday, isTomorrow, isYesterday } from 'date-fns';
import {
  AlertTriangle,
  ArrowUpDown,
  Check,
  CheckCircle2,
  ClipboardEdit,
  ClipboardEdit as ClipboardEditIcon,
  Copy,
  Loader2,
  MoreHorizontal,
  PlayCircle,
  ShieldCheck,
  Undo2,
  XCircle,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState, useTransition } from 'react';
// eslint-disable-next-line import/no-named-as-default
import toast from 'react-hot-toast';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { usePrintQueueModal } from '@/contexts/PrintQueueModalContext';
import {
  getColorInfo,
  getMarketplaceAlias,
  getShippingAlias,
  marketplaceStyles,
} from '@/lib/mapping-utils';
import { cn } from '@/lib/utils';
import { ClientPrintTaskData } from '@/types/print-tasks';

import { BulkUpdateProductNameModal } from './bulk-update-product-name-modal';
import { PrintTaskDetailModal } from './print-task-detail-modal';

// Type definition for Table type
type TTable<T> = ReturnType<typeof useReactTable<T>>;

interface SerializableProduct extends Omit<PrismaProduct, 'weight' | 'item_weight_value'> {
  weight: string | null;
  item_weight_value: string | null;
}

export interface PrintTaskData extends PrintOrderTask {
  product: SerializableProduct;
  orderLink?: string;
  order?: {
    requested_shipping_service: string | null;
    marketplace?: string | null;
    marketplace_order_number?: string | null;
  };
}

async function updateTaskStatus(
  taskId: string,
  status: PrintOrderTask_status
): Promise<{ message?: string; count?: number }> {
  const response = await fetch(`/api/print-tasks/${taskId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status }),
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ error: 'Failed to parse error response' }));
    throw new Error(errorData.error || `Failed to update task ${taskId}`);
  }

  return response.json();
}

async function bulkUpdateTaskStatus(
  taskIds: string[],
  status: PrintOrderTask_status
): Promise<{ message?: string; count?: number }> {
  const response = await fetch(`/api/print-tasks/bulk-status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ taskIds, status }),
    credentials: 'include',
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ error: 'Failed to parse error response' }));
    throw new Error(errorData.error || 'Bulk status update failed');
  }

  return response.json();
}

async function handleBulkStatusUpdateOutside(
  newStatus: PrintOrderTask_status,
  numSelected: number,
  selectedRowIds: string[],
  setIsBulkUpdating: React.Dispatch<React.SetStateAction<boolean>>,
  router: ReturnType<typeof useRouter>,
  bulkUpdateHelper: typeof bulkUpdateTaskStatus
): Promise<void> {
  if (numSelected === 0) {
    toast('No tasks selected.', { icon: '⚠️' }); // Using standard toast with warning icon
    return;
  }
  const idsToUpdate = selectedRowIds;
  setIsBulkUpdating(true);
  try {
    const result = await bulkUpdateHelper(idsToUpdate, newStatus);
    toast.success(
      `${result.message || `Successfully marked ${result.count} tasks as ${newStatus}.`}`
    );
    router.refresh();
  } catch (error: unknown) {
    console.error('Bulk status update failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown bulk update error';
    toast.error(`Bulk update failed: ${errorMessage}`);
  } finally {
    setIsBulkUpdating(false);
  }
}

const formatRelativeDate = (date: Date | string | null): string => {
  if (!date) return 'N/A';
  const dateObj = date instanceof Date ? date : new Date(date);
  if (isToday(dateObj)) return 'Today';
  if (isTomorrow(dateObj)) return 'Tomorrow';
  if (isYesterday(dateObj)) return 'Yesterday';
  return format(dateObj, 'dd/MM/yyyy');
};

interface TableMeta {
  openModal: (_task: ClientPrintTaskData) => void;
}
interface ExtendedTableMeta extends TableMeta {
  router: ReturnType<typeof useRouter>;
}

async function toggleTaskVerification(taskId: string, newVerificationStatus: boolean) {
  const response = await fetch(`/api/print-tasks/${taskId}/verify`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ isVerified: newVerificationStatus }),
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ message: 'Unknown error updating verification status.' })); // Provide a fallback message
    console.error('Failed to update verification status:', errorData);
    throw new Error(
      errorData.message ||
        `Failed to update verification status. Server responded with ${response.status}`
    );
  }
  const updatedTaskData = await response.json();
  return updatedTaskData;
}

const ActionCellComponent: React.FC<{
  row: Row<ClientPrintTaskData>;
  table: TTable<ClientPrintTaskData>;
}> = ({ row, table }) => {
  const [isVerifying, setIsVerifying] = useState(false); // For Verify/Unverify task
  const task = row.original;
  const meta = table.options.meta as ExtendedTableMeta;
  const openModal =
    meta?.openModal ||
    ((_task: ClientPrintTaskData) => {
      console.warn('No openModal function provided');
    });
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleStatusUpdate = (newStatus: PrintOrderTask_status): void => {
    startTransition(async () => {
      try {
        await updateTaskStatus(task.id, newStatus);
        toast.success(`Task marked as ${newStatus}`);
        router.refresh();
      } catch (error: unknown) {
        console.error('Status update failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        toast.error(`Failed to update task: ${errorMessage}`);
      }
    });
  };

  const handleCopyId = (): void => {
    try {
      // Check if clipboard API is available
      if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
        navigator.clipboard
          .writeText(task.id.toString())
          .then(() => toast.success(`Task ID ${task.id} copied!`))
          .catch(() => toast.error('Failed to copy ID'));
      } else {
        // Fallback for browsers without clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = task.id.toString();
        textArea.style.position = 'fixed';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
          toast.success(`Task ID ${task.id} copied!`);
        } catch (err) {
          toast.error('Failed to copy ID');
          console.error('Fallback: Could not copy ID: ', err);
        }
        document.body.removeChild(textArea);
      }
    } catch (error) {
      toast.error('Failed to copy ID');
      console.error('Copy operation failed:', error);
    }
  };

  const renderQuickActionButton = (): React.ReactNode => {
    if (task.status === PrintOrderTask_status.pending) {
      return (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full"
          onClick={e => {
            e.stopPropagation();
            handleStatusUpdate(PrintOrderTask_status.in_progress);
          }}
          disabled={isPending}
          title="Mark as In Progress"
          data-no-row-click
        >
          <PlayCircle className="h-5 w-5" />
        </Button>
      );
    } else if (task.status === PrintOrderTask_status.in_progress) {
      return (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-green-600 hover:text-green-800 hover:bg-green-100 rounded-full"
          onClick={e => {
            e.stopPropagation();
            handleStatusUpdate(PrintOrderTask_status.completed);
          }}
          disabled={isPending}
          title="Mark as Completed"
          data-no-row-click
        >
          <CheckCircle2 className="h-5 w-5" />
        </Button>
      );
    } else if (task.status === PrintOrderTask_status.completed) {
      return (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-full"
          onClick={e => {
            e.stopPropagation();
            handleStatusUpdate(PrintOrderTask_status.pending);
          }}
          disabled={isPending}
          title="Mark as Pending"
          data-no-row-click
        >
          <Undo2 className="h-5 w-5" />
        </Button>
      );
    }
    return null;
  };

  return (
    <div className="flex justify-end space-x-2">
      {renderQuickActionButton()}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0" data-no-row-click>
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          {task.orderLink && (
            <DropdownMenuItem asChild>
              <Link href={task.orderLink}>View Order</Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            data-no-row-click
            onClick={e => {
              e.stopPropagation();
              handleCopyId();
            }}
          >
            Copy Task ID
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              if (typeof openModal === 'function') {
                openModal(task);
              }
            }}
          >
            View Details
          </DropdownMenuItem>
          {task.status !== PrintOrderTask_status.cancelled && (
            <DropdownMenuItem
              data-no-row-click // Prevent row click from firing
              onClick={async event => {
                event.stopPropagation(); // Prevent row click and close menu
                setIsVerifying(true);
                try {
                  await toggleTaskVerification(task.id, !task.verifiedAt);
                  toast.success(
                    `Task ${task.verifiedAt ? 'unverified' : 'verified'} successfully.`
                  );
                  router.refresh();
                } catch (error: Error | unknown) {
                  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                  toast.error(
                    `Failed to ${task.verifiedAt ? 'unverify' : 'verify'} task: ${errorMessage}`
                  );
                } finally {
                  setIsVerifying(false);
                }
              }}
              disabled={isVerifying}
            >
              {isVerifying ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : task.verifiedAt ? (
                <XCircle className="mr-2 h-4 w-4 text-red-500" />
              ) : (
                <Check className="mr-2 h-4 w-4 text-green-500" />
              )}
              {task.verifiedAt ? 'Unverify Task' : 'Verify Task'}
            </DropdownMenuItem>
          )}
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="text-xs text-muted-foreground">
            Status Actions
          </DropdownMenuLabel>

          {task.status !== PrintOrderTask_status.pending && (
            <DropdownMenuItem
              data-no-row-click
              onClick={e => {
                e.stopPropagation();
                handleStatusUpdate(PrintOrderTask_status.pending);
              }}
              disabled={isPending}
              className="text-slate-600 hover:text-slate-800 hover:bg-slate-100"
            >
              {isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Undo2 className="mr-2 h-4 w-4" />
              )}
              Mark Pending
            </DropdownMenuItem>
          )}
          {task.status !== PrintOrderTask_status.in_progress && (
            <DropdownMenuItem
              data-no-row-click
              onClick={e => {
                e.stopPropagation();
                handleStatusUpdate(PrintOrderTask_status.in_progress);
              }}
              disabled={isPending}
              className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
            >
              {isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <PlayCircle className="mr-2 h-4 w-4" />
              )}
              Mark In Progress
            </DropdownMenuItem>
          )}
          {task.status !== PrintOrderTask_status.completed && (
            <DropdownMenuItem
              data-no-row-click
              onClick={e => {
                e.stopPropagation();
                handleStatusUpdate(PrintOrderTask_status.completed);
              }}
              disabled={isPending}
              className="text-green-600 hover:text-green-800 hover:bg-green-100"
            >
              {isPending ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle2 className="mr-2 h-4 w-4" />
              )}
              Mark Completed
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export const columns: ColumnDef<ClientPrintTaskData>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value: boolean | 'indeterminate') =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="border-gray-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: boolean | 'indeterminate') => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="border-gray-400 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white"
      />
    ),
    meta: {
      headerClassName: 'sticky top-0 left-0 z-20 w-[40px] px-2 bg-background',
      cellClassName: 'sticky left-0 z-10 w-[40px] px-2 bg-background',
    },
    enableSorting: false,
    enableHiding: false,
  },
  // ID column removed to prevent confusion with new CUID format IDs
  {
    accessorKey: 'product.sku',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          SKU
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    meta: {
      headerClassName: 'sticky top-0 left-[40px] z-20 w-[100px] pl-2 pr-1 bg-background',
      cellClassName:
        'sticky left-[40px] z-10 w-[100px] pl-2 pr-1 bg-background whitespace-nowrap overflow-hidden text-ellipsis',
    },
    cell: ({ row }) => {
      const product = row.original.product;
      const sku = product.sku as string;
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="truncate font-mono text-xs max-w-[80px] cursor-default">{sku}</div>
            </TooltipTrigger>
            <TooltipContent side="bottom" align="start">
              <p className="text-sm font-semibold">{product.name}</p>
              <p className="text-xs text-muted-foreground">SKU: {sku}</p>
              {product.weight && <p className="text-xs">Weight: {product.weight}g</p>}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
    enableSorting: true,
  },

  {
    accessorKey: 'product.name',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Product Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const name = row.original.product.name as string;
      const productName = name || 'N/A';
      return (
        <div title={productName} className="truncate max-w-[200px] whitespace-normal text-sm">
          {productName}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'quantity',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          className="px-0 mx-0 w-full"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Qty
          <ArrowUpDown className="ml-1 h-2 w-2" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const quantity = row.getValue('quantity') as number;
      // Use different styling based on quantity value
      const badgeVariant = quantity > 1 ? 'default' : 'outline';
      const badgeStyle =
        quantity > 1
          ? 'bg-blue-500 text-white border border-blue-600 shadow-sm px-2 py-1 font-medium'
          : 'bg-slate-50 text-slate-700 border border-gray-300 shadow-sm px-2 py-1 font-medium';

      return (
        <div className="text-center w-full">
          <Badge variant={badgeVariant} className={badgeStyle}>
            {quantity}
          </Badge>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'color_1',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Color 1
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const color = row.original.color_1;
      if (!color) return <div className="text-center">-</div>;

      const { bgClass, textClass } = getColorInfo(color);
      return (
        <div className="flex justify-center">
          <Badge
            className={`${bgClass} ${textClass} border border-gray-300 shadow-sm`}
            variant="outline"
          >
            {color}
          </Badge>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'color_2',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Color 2
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const color = row.original.color_2;
      if (!color) return <div className="text-center">-</div>;

      const { bgClass, textClass } = getColorInfo(color);
      return (
        <div className="flex justify-center">
          <Badge
            className={`${bgClass} ${textClass} border border-gray-300 shadow-sm`}
            variant="outline"
          >
            {color}
          </Badge>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'custom_text',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Personalisation
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }: { row: Row<ClientPrintTaskData> }) => {
      const customText = row.getValue('custom_text') as string | null;
      const fullText = customText || '';
      const truncatedText = fullText.length > 50 ? `${fullText.substring(0, 50)}...` : fullText;

      const handleCopyCustomText = () => {
        if (fullText) {
          try {
            // Check if clipboard API is available
            if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
              navigator.clipboard.writeText(fullText);
              toast.success('Custom text copied!');
            } else {
              // Fallback for browsers without clipboard API
              const textArea = document.createElement('textarea');
              textArea.value = fullText;
              textArea.style.position = 'fixed';
              document.body.appendChild(textArea);
              textArea.focus();
              textArea.select();
              try {
                document.execCommand('copy');
                toast.success('Custom text copied!');
              } catch (err) {
                toast.error('Failed to copy text');
                console.error('Fallback: Could not copy text: ', err);
              }
              document.body.removeChild(textArea);
            }
          } catch (error) {
            toast.error('Failed to copy text');
            console.error('Copy operation failed:', error);
          }
        }
      };

      return fullText ? (
        <div className="flex items-center justify-between">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-left font-medium truncate max-w-[180px] hover:whitespace-normal hover:overflow-visible">
                  {truncatedText}
                </div>
              </TooltipTrigger>
              {fullText.length > 50 && <TooltipContent>{fullText}</TooltipContent>}
            </Tooltip>
          </TooltipProvider>
          <Button
            variant="ghost"
            size="icon"
            data-no-row-click
            onClick={e => {
              e.stopPropagation();
              handleCopyCustomText();
            }}
            className="h-6 w-6 p-1 text-gray-500 hover:text-gray-700 ml-1 flex-shrink-0"
            title="Copy Custom Text"
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      ) : (
        <span className="text-gray-400">-</span>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const status = row.original.status;
      let indicator;

      switch (status) {
        case PrintOrderTask_status.pending:
          indicator = (
            <Badge
              variant="outline"
              className="bg-gray-100 text-gray-800 border-gray-300 font-medium"
            >
              Pending
            </Badge>
          );
          break;
        case PrintOrderTask_status.in_progress:
          indicator = (
            <Badge
              variant="outline"
              className="bg-blue-100 text-blue-800 border-blue-300 font-medium"
            >
              In Progress
            </Badge>
          );
          break;
        case PrintOrderTask_status.completed:
          indicator = (
            <Badge
              variant="outline"
              className="bg-green-100 text-green-800 border-green-300 font-medium"
            >
              Completed
            </Badge>
          );
          break;
        case PrintOrderTask_status.cancelled:
          indicator = (
            <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300 font-medium">
              Cancelled
            </Badge>
          );
          break;
        default:
          indicator = (
            <Badge variant="outline" className="bg-gray-100 text-gray-800">
              {status}
            </Badge>
          );
      }

      return <div className="flex justify-center">{indicator}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: 'personalisation_status',
    header: () => (
      <div className="flex justify-center items-center w-full h-full">
        <ClipboardEdit className="h-5 w-5" />
      </div>
    ),
    cell: ({ row }) => {
      const task = row.original;
      let pStatusIconElement: JSX.Element;
      const pStatusTooltipLines: string[] = [];
      let pStatusIconColorClass = '';

      if (task.needs_review) {
        pStatusIconElement = <AlertTriangle className="h-5 w-5" />;
        pStatusIconColorClass = 'text-yellow-500';
        pStatusTooltipLines.push('Status: Needs Personalisation Review');
        if (task.review_reason) {
          pStatusTooltipLines.push(`Reason: ${task.review_reason}`);
        }
      } else {
        pStatusIconElement = <CheckCircle2 className="h-5 w-5" />;
        pStatusIconColorClass = 'text-green-600';
        pStatusTooltipLines.push('Status: Personalisation Reviewed/OK');
      }

      return (
        <div className="flex justify-center items-center">
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className={cn('cursor-default', pStatusIconColorClass)}>
                  {pStatusIconElement}
                </span>
              </TooltipTrigger>
              {pStatusTooltipLines.length > 0 && (
                <TooltipContent side="bottom" align="center">
                  {pStatusTooltipLines.map((line, index) => (
                    <p key={index} className="text-xs">
                      {line}
                    </p>
                  ))}
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
    meta: {
      headerClassName: 'text-center w-[60px] px-1',
      cellClassName: 'text-center px-1',
    },
    enableSorting: true,
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.original.created_at;
      return (
        <div className="text-center text-xs">
          <div>{formatRelativeDate(date)}</div>
          <div className="text-gray-500">{date ? format(new Date(date), 'HH:mm') : 'N/A'}</div>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    id: 'verifiedAt',
    header: ({ column }) => (
      <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
        Verified At
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const task = row.original;
      if (!task.verifiedAt) {
        return <div className="text-center">-</div>;
      }
      const date = new Date(task.verifiedAt);
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="text-center text-xs cursor-default">
                <div>{format(date, 'dd/MM/yy')}</div>
                <div className="text-gray-500">{format(date, 'HH:mm')}</div>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{format(date, 'PPPPpppp')}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    },
    enableSorting: true,
  },
  {
    id: 'verifiedByUserName',
    header: 'Verified By',
    cell: ({ row }) => {
      const task = row.original;
      if (!task.verifiedAt || !task.verifiedByUser) {
        return <div className="text-center">-</div>;
      }
      // Display name if available, otherwise show user ID
      const displayName = task.verifiedByUser.name || `User #${task.verifiedByUser.id}`;
      return <div className="text-center text-xs">{displayName}</div>;
    },
    enableSorting: true,
  },
  {
    id: 'verification',
    header: () => (
      <div className="flex justify-center items-center w-full h-full">
        <ShieldCheck className="h-5 w-5" />
      </div>
    ),
    cell: ({ row }) => {
      const task = row.original;
      let iconElement: JSX.Element;
      const tooltipLines: string[] = [];
      let iconColorClass = '';

      if (task.verifiedAt) {
        iconElement = <CheckCircle2 className="h-5 w-5" />;
        iconColorClass = 'text-green-600';
        tooltipLines.push('Status: Verified');
        if (task.verifiedAt) {
          tooltipLines.push(`At: ${format(new Date(task.verifiedAt), 'dd/MM/yy HH:mm')}`);
        }
        if (task.verifiedByUser?.name) {
          tooltipLines.push(`By: ${task.verifiedByUser.name}`);
        }
      } else if (task.needs_review) {
        iconElement = <AlertTriangle className="h-5 w-5" />;
        iconColorClass = 'text-yellow-500';
        tooltipLines.push('Status: Needs Review');
        if (task.review_reason) {
          tooltipLines.push(`Reason: ${task.review_reason}`);
        }
      } else {
        iconElement = <XCircle className="h-5 w-5" />;
        iconColorClass = 'text-slate-400';
        tooltipLines.push('Status: Not Verified');
      }

      return (
        <div className="flex justify-center items-center">
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className={cn('cursor-default', iconColorClass)}>{iconElement}</span>
              </TooltipTrigger>
              {tooltipLines.length > 0 && (
                <TooltipContent side="bottom" align="center">
                  {tooltipLines.map((line, index) => (
                    <p key={index} className="text-xs">
                      {line}
                    </p>
                  ))}
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
    enableSorting: false, // Or sort by isVerified, needs_review, verifiedAt etc.
  },
  {
    accessorKey: 'order.requested_shipping_service',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Shipping
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const shippingService = row.original.order?.requested_shipping_service;
      const shippingAlias = getShippingAlias(shippingService);

      let icon = null;
      if (shippingAlias === 'Special Delivery') {
        icon = (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
              </TooltipTrigger>
              <TooltipContent>
                <p>Special Delivery - Priority</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      } else if (shippingAlias === 'Tracked24') {
        icon = (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
              </TooltipTrigger>
              <TooltipContent>
                <p>Tracked 24 - Priority</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      }

      return (
        <div className="flex items-center justify-center text-xs shipping-cell">
          {icon}
          <span>{shippingAlias}</span>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'order.marketplace',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Marketplace
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const marketplace = row.original.order?.marketplace;
      const marketplaceAlias = getMarketplaceAlias(marketplace);

      const style = marketplaceStyles[marketplaceAlias] || marketplaceStyles['N/A'];

      return (
        <div className="flex justify-center marketplace-id-cell">
          <Badge
            className={`${style?.color || 'bg-gray-400'} ${style?.text || 'text-black'}`}
            variant="default"
          >
            {marketplaceAlias}
          </Badge>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: 'marketplace_order_number',
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Order ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const orderNum = row.original.order?.marketplace_order_number;
      const orderLink = row.original.orderLink;

      if (orderLink) {
        return (
          <div>
            <Link
              href={orderLink}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700 hover:underline text-xs"
              data-no-row-click
            >
              {orderNum || 'View Order'}
            </Link>
          </div>
        );
      }
      return <div>{orderNum || 'N/A'}</div>;
    },
    enableSorting: true,
  },
  {
    id: 'actions',
    cell: ActionCellComponent,
    enableSorting: false,
    enableHiding: false,
  },
];

export interface PrintQueueTableProps {
  data: ClientPrintTaskData[];
  onSelectTask?: (_task: ClientPrintTaskData) => void;
}

export function PrintQueueTable({ data, onSelectTask }: PrintQueueTableProps): JSX.Element {
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [isBulkUpdating, setIsBulkUpdating] = useState(false);
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false); // Added state for new modal
  const { setSelectedTask, setIsModalOpen } = usePrintQueueModal();

  // Load saved sorting preferences from localStorage on first mount
  useEffect(() => {
    try {
      const saved = window.localStorage.getItem('printQueueTableSorting');
      if (saved) {
        const parsed: SortingState = JSON.parse(saved);
        if (Array.isArray(parsed)) {
          setSorting(parsed);
        }
      }
    } catch (err: unknown) {
      console.warn('[PrintQueueTable] Failed to load saved sorting from localStorage:', err);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Persist sorting changes to localStorage
  useEffect(() => {
    try {
      window.localStorage.setItem('printQueueTableSorting', JSON.stringify(sorting));
    } catch (err: unknown) {
      console.warn('[PrintQueueTable] Failed to save sorting to localStorage:', err);
    }
  }, [sorting]);

  const handleOpenModal = (task: ClientPrintTaskData) => {
    if (onSelectTask) {
      onSelectTask(task);
    }
    setSelectedTask(task);
    setIsModalOpen(true);
  };

  const table = useReactTable<ClientPrintTaskData>({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    initialState: {
      pagination: { pageSize: 100, pageIndex: 0 },
      columnFilters: [],
      sorting: [{ id: 'created_at', desc: true }],
      columnVisibility: {
        custom_text: true,
        marketplace_order_number: true,
        'product.sku': true,
        'product.name': true,
        verification: true,
        verifiedAt: true,
        verifiedByUserName: true,
      },
    },
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: { sorting, columnFilters, columnVisibility, rowSelection },
    enableRowSelection: true,
    meta: { openModal: handleOpenModal, router } as ExtendedTableMeta,
  });

  const selectedRowIds = Object.keys(rowSelection)
    .map(index => table.getRowModel().rowsById[index]?.original?.id)
    .filter((id): id is string => typeof id === 'string');
  const numSelected = selectedRowIds.length;

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-1 text-sm">
            <span className="text-muted-foreground">
              {numSelected > 0 ? `${numSelected} task(s) selected. ` : ''}
            </span>
            <span className="font-medium">{table.getFilteredRowModel().rows.length} tasks</span>
            <span className="text-muted-foreground"> displayed</span>
          </div>
        </div>
        <div className="flex items-center space-x-2 ml-auto">
          {numSelected > 0 && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  handleBulkStatusUpdateOutside(
                    PrintOrderTask_status.in_progress,
                    numSelected,
                    selectedRowIds,
                    setIsBulkUpdating,
                    router,
                    bulkUpdateTaskStatus
                  )
                }
                disabled={isBulkUpdating}
                className="border-blue-500 text-blue-500 hover:bg-blue-500/10 hover:text-blue-600"
              >
                {isBulkUpdating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <PlayCircle className="mr-2 h-4 w-4" />
                )}{' '}
                Mark Selected In Progress ({numSelected})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  handleBulkStatusUpdateOutside(
                    PrintOrderTask_status.completed,
                    numSelected,
                    selectedRowIds,
                    setIsBulkUpdating,
                    router,
                    bulkUpdateTaskStatus
                  )
                }
                disabled={isBulkUpdating}
                className="border-green-500 text-green-500 hover:bg-green-500/10 hover:text-green-600"
              >
                {isBulkUpdating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Check className="mr-2 h-4 w-4" />
                )}{' '}
                Mark Selected Completed ({numSelected})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  handleBulkStatusUpdateOutside(
                    PrintOrderTask_status.pending,
                    numSelected,
                    selectedRowIds,
                    setIsBulkUpdating,
                    router,
                    bulkUpdateTaskStatus
                  )
                }
                disabled={isBulkUpdating}
              >
                {isBulkUpdating ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Undo2 className="mr-2 h-4 w-4" />
                )}{' '}
                Mark Selected Pending ({numSelected})
              </Button>
              {/* New "Update Product Name" button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsBulkUpdateModalOpen(true)}
                disabled={isBulkUpdating}
                className="border-purple-500 text-purple-500 hover:bg-purple-500/10 hover:text-purple-600"
              >
                <ClipboardEditIcon className="mr-2 h-4 w-4" />
                Update Product Name
              </Button>
            </>
          )}
        </div>
      </div>
      <BulkUpdateProductNameModal
        isOpen={isBulkUpdateModalOpen}
        onOpenChange={setIsBulkUpdateModalOpen}
        selectedTaskIds={selectedRowIds}
        onBulkUpdateStart={() => setIsBulkUpdating(true)}
        onBulkUpdateEnd={() => setIsBulkUpdating(false)}
      />
      <div className="rounded-md border relative">
        <div
          className="overflow-x-auto print-queue-table"
          style={{ width: '100%', overflowY: 'visible' }}
        >
          <style jsx global>{`
            .print-queue-table td {
              padding: 0.25rem 0.25rem;
              min-width: auto;
              text-overflow: clip !important;
              overflow: visible !important;
              white-space: nowrap !important;
            }
            .print-queue-table th {
              padding: 0.25rem 0.25rem;
              min-width: auto;
              text-overflow: clip !important;
              overflow: visible !important;
              white-space: nowrap !important;
            }

            /* Reduce space in header buttons */
            .print-queue-table button {
              padding-left: 0.5rem;
              padding-right: 0.5rem;
            }

            /* Tighten up badge spacing */
            .print-queue-table .badge {
              padding: 0.15rem 0.3rem;
              font-size: 0.7rem;
            }

            /* Make specific columns wrap text if needed */
            .print-queue-table .product-name-cell {
              white-space: normal !important;
            }
            .print-queue-table .custom-text-cell {
              white-space: normal !important;
            }

            /* Sticky columns */
            .select-cell {
              position: sticky;
              left: 0;
              z-index: 30; /* Highest z-index */
              background: var(--background); /* Make sticky cell opaque */
              width: 20px;
              padding-left: 0px;
              padding-right: 0px;
              text-align: center;
            }
            .status-cell {
              position: sticky;
              left: 20px; /* new select-cell width */
              z-index: 25; /* Adjusted z-index for left stacking */
              background: var(--background); /* Make sticky cell opaque */
              width: 46px;
              padding-left: 1px;
              padding-right: 1px;
              text-align: center;
            }
            .actions-cell {
              position: sticky;
              left: 66px; /* select-cell (20) + status-cell (46) */
              z-index: 20; /* Adjusted z-index for left stacking */
              background: var(--background); /* Make sticky cell opaque */
              width: 24px;
              padding-left: 1px;
              padding-right: 1px;
            }

            /* Ensuring table cell defaults */
            /* RESTORED CELL-SPECIFIC STYLES (TD level) */
            .quantity-cell {
              width: 25px;
              text-align: center;
              padding-left: 1px;
              padding-right: 1px;
            }
            .sku-cell {
              /* TD style */
              width: 110px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .color-cell {
              width: 70px;
              padding-left: 2px;
              padding-right: 2px;
              text-align: center;
            }
            .personalization-cell {
              /* TD style */
              width: 80px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .created-cell {
              width: 60px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .product-name-cell {
              /* TD style */
              min-width: 150px;
              max-width: 200px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .custom-text-cell {
              min-width: 100px;
              max-width: 150px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .task-id-cell {
              /* TD style */
              width: 40px;
              padding-left: 2px;
              padding-right: 2px;
              text-align: right;
              overflow: hidden;
            }
            .order-id-cell {
              /* TD style */
              width: 60px;
              max-width: 60px;
              padding-left: 2px;
              padding-right: 2px;
              overflow: hidden;
              position: relative;
            }
            .printer-cell {
              width: 70px;
              padding-left: 2px;
              padding-right: 2px;
            }
            .priority-cell {
              width: 60px;
              padding-left: 2px;
              padding-right: 2px;
              text-align: center;
            }
            .shipping-cell {
              width: 70px;
              white-space: nowrap !important;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 2px;
              padding-right: 2px;
            }
            .marketplace-id-cell {
              width: 80px;
              white-space: nowrap !important;
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 2px;
              padding-right: 2px;
            }

            /* RESTORED INNER DIV/A STYLES (for wrapping/truncation) */
            .sku-cell > div,
            .product-name-cell > div {
              max-width: 100%;
              word-break: break-word; /* Ensure long words break and wrap */
              white-space: normal; /* Allow normal text wrapping */
              line-height: 1.4; /* Adjusted for tighter packing with sm font */
              /* Padding is handled by the cell itself or global td/th styles */
            }
            /* Restore overflow, nowrap, and ellipsis styles to .shipping-cell and .marketplace-id-cell */
            .shipping-cell {
              width: 70px; /* Reduced width */
              white-space: nowrap !important; /* Ensure nowrap */
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 2px;
              padding-right: 2px;
            }
            .marketplace-id-cell {
              width: 80px;
              white-space: nowrap !important; /* Ensure nowrap */
              overflow: hidden;
              text-overflow: ellipsis;
              padding-left: 2px;
              padding-right: 2px;
            }
            /* Truncation styles for inner divs of specific cells */
            .task-id-cell > div {
              display: block; /* Ensure block behavior for overflow */
              width: 100%; /* Ensure div fills td for proper truncation */
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-align: right; /* Inherited from old .task-id-cell */
            }
            .order-id-cell > div,
            .sku-cell > div, /* SKU now truncates instead of wrapping */
            .personalization-cell > div {
              display: block; /* Ensure block behavior for overflow */
              width: 100%; /* Ensure div fills td for proper truncation */
              white-space: nowrap; /* Apply nowrap here */
              overflow: hidden; /* This div will clip the <a> tag */
              text-overflow: ellipsis; /* Apply ellipsis here */
            }

            /* Style for the link within the Order ID cell to ensure truncation */
            .order-id-cell > div > a {
              display: block; /* Revert to block for more reliable truncation */
              width: 100%; /* Take full width of parent div */
              /* white-space: nowrap; --- Removed */
              /* overflow: hidden; --- Removed */
              /* text-overflow: ellipsis; --- Removed */
            }
          `}</style>
          <Table className="min-w-full table-fixed" style={{ minWidth: '1200px' }}>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      className={cn(header.column.id === 'select' ? 'text-center' : '')}
                    >
                      {header.isPlaceholder ? null : (
                        <div>{flexRender(header.column.columnDef.header, header.getContext())}</div>
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => {
                  const task = row.original;
                  const shippingService = task.order?.requested_shipping_service;
                  const isSpecialDelivery =
                    shippingService === 'Special Delivery' ||
                    shippingService === 'NextDay' ||
                    shippingService === 'Next Day' ||
                    shippingService === 'NextDay UK Next';
                  const isTracked24 =
                    shippingService === 'Tracked24' ||
                    shippingService === '2-Day Shipping – 1-2 Working Days Delivery' ||
                    shippingService === 'SecondDay UK Second';

                  const rowClassName = cn(
                    'h-12 cursor-pointer transition-colors duration-150 ease-in-out hover:bg-muted/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2', // Base styles
                    task.status === PrintOrderTask_status.in_progress &&
                      'in-progress-row bg-gray-100 dark:bg-gray-800/30 text-gray-500 dark:text-gray-400 line-through',
                    isSpecialDelivery &&
                      'font-medium priority-special-delivery bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-600',
                    isTracked24 &&
                      'font-medium priority-tracked24 bg-purple-50 dark:bg-purple-900/20 border-l-4 border-purple-400 dark:border-purple-600',
                    task.needs_review &&
                      'needs-review-row border-l-6 border-amber-500 dark:border-red-500 bg-transparent hover:bg-slate-100 dark:hover:bg-slate-800/40',
                    task.status === PrintOrderTask_status.completed &&
                      'bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-800/40 text-green-700 dark:text-green-300',
                    selectedRowIds.includes(task.id) &&
                      'selected-row bg-sky-100 dark:bg-sky-700/60 hover:bg-sky-200 dark:hover:bg-sky-700/80' // Selection style should have high precedence
                  );

                  return (
                    <TableRow
                      key={task.id}
                      data-state={row.getIsSelected() && 'selected'}
                      onClick={(e: React.MouseEvent<HTMLTableRowElement, MouseEvent>) => {
                        if (e.defaultPrevented) return;
                        let el = e.target as EventTarget & HTMLElement;
                        while (el && el !== e.currentTarget) {
                          if (el.getAttribute && el.getAttribute('data-no-row-click') !== null) {
                            return;
                          }
                          el = el.parentNode as EventTarget & HTMLElement;
                        }
                        handleOpenModal(task);
                      }} // Use task directly
                      className={rowClassName} // Use the consolidated rowClassName
                    >
                      {row.getVisibleCells().map(cell => (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            'px-2 py-1.5',
                            cell.column.id === 'status' ? 'status-cell' : '',
                            cell.column.id === 'actions' ? 'actions-cell' : '',
                            cell.column.id === 'select' ? 'select-cell' : '',
                            cell.column.id === 'product.sku' ? 'sku-cell' : '',
                            cell.column.id === 'product.name'
                              ? 'product-name-cell whitespace-normal'
                              : '',
                            cell.column.id === 'quantity' ? 'quantity-cell' : '',
                            cell.column.id === 'order.marketplace_order_number' ||
                              cell.column.id === 'marketplace_order_number'
                              ? 'order-id-cell'
                              : '',
                            cell.column.id === 'order.requested_shipping_service' ||
                              cell.column.id === 'shipping'
                              ? 'shipping-cell'
                              : '',
                            cell.column.id === 'custom_text' ? 'custom-text-cell' : '',
                            cell.column.id === 'color_1' || cell.column.id === 'color_2'
                              ? 'color-cell'
                              : '',
                            cell.column.id === 'created_at' ? 'created-cell' : '',
                            cell.column.id === 'personalization_data' ? 'personalization-cell' : '',
                            cell.column.id === 'printer_name' ? 'printer-cell' : '',
                            // ID column removed
                            cell.column.id === 'priority_score'
                              ? 'priority-cell' /* Apply new class here */
                              : ''
                          )}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      <PrintTaskDetailModal />
    </div>
  );
}
