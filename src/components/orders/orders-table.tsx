'use client';

import { Prisma, type Tag } from '@prisma/client';
import {
  type ColumnDef,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {
  Archive,
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Copy,
  Loader2,
  MoreHorizontal,
  Pause,
  Play,
  Printer,
  RefreshCw,
  Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label'; // Added
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'; // Added
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { orderSelectedFields } from '@/lib/orders/selected-fields';
import { formatDateForTable } from '@/lib/shared/date-utils';
import { cn, formatPrice } from '@/lib/utils';

import { PackingSlipBatchControls } from './packing-slip-batch-controls';

// Type for a single order, consistent with OrdersPage
// Uses shared select fields from '@/lib/orders/selected-fields'

// Original Prisma-generated type
type PrismaSelectedOrder = Prisma.OrderGetPayload<{ select: typeof orderSelectedFields }>;

// Type for serializable order data passed from server components to client components
export type SerializableOrder = Omit<
  PrismaSelectedOrder,
  'total_price' | 'order_date' | 'shipped_date' | 'ship_by_date' | 'lastPackingSlipAt'
> & {
  total_price: number | null;
  order_date: string | null;
  shipped_date: string | null;
  ship_by_date: string | null;
  lastPackingSlipAt: string | null;
  // tag_ids is Prisma.JsonValue which is serializable
  // _count and printTasks are also expected to be serializable
};

interface OrdersTableProps {
  orders: SerializableOrder[];
  allTags: Tag[];
  currentLimit: number;
}

// Adopted from print-queue-table.tsx for consistency
const marketplaceMapping: Record<string, string> = {
  ebay: 'eBay',
  ebay_v2: 'eBay',
  amazon: 'Amazon',
  amazon_com: 'Amazon',
  amazon_ca: 'Amazon',
  amazon_uk: 'Amazon',
  etsy: 'Etsy',
  shopify: 'Shopify',
  web: 'Shopify', // 'web' is often used for Shopify in ShipStation
  woocommerce: 'WooCommerce',
  manual_order: 'Manual Order',
  // Add other raw values from ShipStation if they need mapping
};

// Updated marketplaceStyles to align with print-queue
const marketplaceStyles: Record<string, { bg: string; text: string }> = {
  Shopify: { bg: 'bg-green-600', text: 'text-white' },
  Amazon: { bg: 'bg-yellow-600', text: 'text-white' },
  eBay: { bg: 'bg-red-600', text: 'text-white' },
  Etsy: { bg: 'bg-orange-600', text: 'text-white' },
  WooCommerce: { bg: 'bg-purple-500', text: 'text-white' }, // Kept from original OrdersTable
  'Manual Order': { bg: 'bg-gray-600', text: 'text-white' }, // Kept from original OrdersTable
  'N/A': { bg: 'bg-gray-500', text: 'text-white' },
};

// Adopted from print-queue-table.tsx for consistency
function getMarketplaceAlias(originalName?: string | null): string {
  if (!originalName) {
    return 'N/A';
  }
  const lowerCaseName = originalName.toLowerCase().trim();
  // Return mapped name or the original name if not in map (it will default to N/A style then)
  return marketplaceMapping[lowerCaseName] || originalName;
}

function TrackingNumberCell({ trackingNumber }: { trackingNumber: string | null }) {
  if (!trackingNumber) {
    return <span className="text-xs text-muted-foreground">-</span>;
  }
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard
      .writeText(trackingNumber)
      .then(() => toast.success('Tracking # copied!'))
      .catch(() => toast.error('Failed to copy tracking #'));
  };
  const trackingUrl =
    trackingNumber.length > 10
      ? `https://www.google.com/search?q=${encodeURIComponent(trackingNumber)}`
      : null;

  const content = (
    <div className="flex items-center gap-1 max-w-xs group">
      <span className="truncate font-mono text-xs flex-grow">{trackingNumber}</span>
      <Button
        variant="ghost"
        size="icon"
        className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
        onClick={handleCopy}
        aria-label="Copy tracking number"
      >
        <Copy className="h-3 w-3" />
      </Button>
    </div>
  );

  if (trackingUrl) {
    return (
      <a
        href={trackingUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="text-foreground/90 hover:text-foreground hover:underline"
        title="Track package (opens in new tab)"
      >
        {content}
      </a>
    );
  }
  return content;
}

export function OrdersTable({ orders, allTags, currentLimit }: OrdersTableProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [printingOrderId, setPrintingOrderId] = useState<number | null>(null);

  const handlePrintPackingSlip = useCallback((orderId: number) => {
    setPrintingOrderId(orderId);
    try {
      const apiUrl = `/api/generate-pdf/packing-slips?ids=${orderId}`;
      const newWindow = window.open(apiUrl, '_blank');

      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        throw new Error(
          'Failed to open packing slip. Please disable your popup blocker for this site.'
        );
      }
    } catch (err) {
      let message = 'Failed to display packing slip. See console for details.';
      if (err instanceof Error) {
        message = err.message;
      }
      console.error('Error displaying packing slip:', err);
      alert(message);
    } finally {
      setPrintingOrderId(null);
    }
  }, []);
  const handleResyncOrder = useCallback((orderId: number) => {
    console.log('TODO: Implement handleResyncOrder for orderId:', orderId);
  }, []);

  const handleUpdateOrderStatus = useCallback((orderId: number, status: string) => {
    console.log(
      `TODO: Implement handleUpdateOrderStatus for orderId: ${orderId} to status: ${status}`
    );
  }, []);

  const handleReprocessOrderForAI = useCallback((orderId: number) => {
    console.log('TODO: Implement handleReprocessOrderForAI for orderId:', orderId);
  }, []);

  const handleArchiveOrder = useCallback((orderId: number) => {
    console.log('TODO: Implement handleArchiveOrder for orderId:', orderId);
  }, []);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [_columnFilters, _setColumnFilters] = useState<Record<string, unknown>[]>([]);
  const [rowSelection, setRowSelection] = useState({});

  const handlePageSizeChange = useCallback(
    (newPageSize: string) => {
      const current = new URLSearchParams(Array.from(searchParams.entries()));
      current.set('limit', newPageSize);
      current.delete('page'); // Reset to page 1 when limit changes
      router.push(`/orders?${current.toString()}`, { scroll: false });
    },
    [router, searchParams]
  );

  const columns: ColumnDef<SerializableOrder, unknown>[] = useMemo(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
            // className="translate-y-[2px]" // Already centered by meta
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={value => row.toggleSelected(!!value)}
            aria-label="Select row"
            // className="translate-y-[2px]" // Already centered by meta
          />
        ),
        enableSorting: false,
        enableHiding: false,
        meta: {
          cellClassName:
            'w-[60px] sticky left-0 z-10 bg-background flex items-center justify-center',
          headerClassName:
            'w-[60px] sticky left-0 z-10 bg-background flex items-center justify-center',
        },
      },
      {
        accessorKey: 'id',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            ID
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => <div className="font-medium text-xs">{row.getValue('id')}</div>,
        meta: { headerClassName: 'text-left w-[80px]', cellClassName: 'text-left' },
      },
      {
        accessorKey: 'shipstation_order_number',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Order #
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const order = row.original;
          const shipstationOrderNumber = order.shipstation_order_number;

          return (
            <Link
              href={`/orders/${order.id}`}
              className="font-medium hover:underline text-blue-500 dark:text-blue-400"
            >
              {shipstationOrderNumber}
            </Link>
          );
        },
        meta: { headerClassName: 'w-[250px] min-w-[220px]' },
      },
      {
        accessorKey: 'customer_name',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Customer
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => (
          <div className="whitespace-nowrap truncate">{row.original.customer_name || 'N/A'}</div>
        ),
        meta: { headerClassName: 'w-[220px] min-w-[200px]' },
      },
      {
        accessorKey: 'marketplace',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Marketplace
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const order = row.original;
          const marketplaceAlias = getMarketplaceAlias(order.marketplace);
          // marketplaceStyles should be accessible from component scope
          const style = marketplaceStyles[marketplaceAlias] ||
            marketplaceStyles['N/A'] || { bg: 'bg-gray-500', text: 'text-white' };
          return (
            <div className="text-center whitespace-nowrap truncate">
              <Badge
                variant="default"
                className={cn(
                  'px-2 py-1 text-xs font-medium rounded-md border border-white/10 shadow-sm',
                  style?.bg || 'bg-gray-500',
                  style?.text || 'text-white'
                )}
              >
                {marketplaceAlias || 'N/A'}
              </Badge>
            </div>
          );
        },
        meta: { headerClassName: 'w-[120px]' },
      },
      {
        id: 'order_tags',
        header: 'Tags',
        cell: ({ row }) => {
          const order = row.original;
          const tagsArray = (order.tag_ids as number[]) || [];
          const isPrime =
            Array.isArray(tagsArray) &&
            tagsArray.some(
              (tagId: number) =>
                allTags.find(t => t?.shipstation_tag_id === tagId)?.name === 'Amazon Prime Order'
            );
          const isPremium =
            Array.isArray(tagsArray) &&
            tagsArray.some(
              (tagId: number) =>
                allTags.find(t => t?.shipstation_tag_id === tagId)?.name ===
                '*** PREMIUM DELIVERY ***'
            );
          const isRush =
            Array.isArray(tagsArray) &&
            tagsArray.some(
              (tagId: number) =>
                allTags.find(t => t?.shipstation_tag_id === tagId)?.name === 'RUSH ORDER'
            );

          if (!isPrime && !isPremium && !isRush) {
            return <div className="text-xs text-center">-</div>;
          }

          return (
            <div className="flex items-center justify-center gap-1">
              {isPrime && (
                <Badge className="bg-blue-500 text-white hover:bg-blue-600 text-xs px-1.5 py-0.5">
                  Prime
                </Badge>
              )}
              {isPremium && (
                <Badge className="bg-purple-500 text-white hover:bg-purple-600 text-xs px-1.5 py-0.5">
                  Premium
                </Badge>
              )}
              {isRush && (
                <Badge className="bg-red-500 text-white hover:bg-red-600 text-xs px-1.5 py-0.5">
                  Rush
                </Badge>
              )}
            </div>
          );
        },
        meta: { headerClassName: 'w-[180px] text-center', cellClassName: 'text-center' },
        enableSorting: false,
      },
      {
        accessorKey: 'order_status',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Status
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const orderStatus = row.original.order_status;
          return (
            <div className="whitespace-nowrap truncate">
              <Badge
                variant="default"
                className={cn(
                  'font-medium text-xs px-2 py-1',
                  orderStatus === 'shipped' && 'bg-green-600 dark:bg-green-700 text-white',
                  orderStatus === 'awaiting_shipment' && 'bg-blue-600 dark:bg-blue-700 text-white',
                  orderStatus === 'on_hold' && 'bg-yellow-600 dark:bg-yellow-700 text-white',
                  orderStatus === 'cancelled' && 'bg-red-600 dark:bg-red-700 text-white'
                )}
              >
                {orderStatus.replace(/_/g, ' ')}
              </Badge>
            </div>
          );
        },
        meta: { headerClassName: 'w-[150px]' },
      },
      {
        accessorKey: 'total_price',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto justify-end w-full"
          >
            Total
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => <div className="text-xs">{formatPrice(row.original.total_price)}</div>,
        meta: { headerClassName: 'w-[100px] text-right', cellClassName: 'text-right' },
      },
      {
        accessorKey: 'order_date',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Order Date
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const date = row.original.order_date;
          return (
            <div className="text-xs whitespace-nowrap">
              {date ? formatDateForTable(date) : 'N/A'}
            </div>
          );
        },
        meta: { headerClassName: 'w-[120px]' },
      },
      {
        accessorKey: 'ship_by_date',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Ship By
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const shipByDate = row.original.ship_by_date;
          const orderStatus = row.original.order_status;
          const isShippedOrCancelled = orderStatus === 'shipped' || orderStatus === 'cancelled';
          const isOverdue =
            shipByDate && new Date(shipByDate) < new Date() && !isShippedOrCancelled;
          return (
            <div
              className={cn('text-xs whitespace-nowrap', isOverdue && 'text-red-500 font-semibold')}
            >
              {shipByDate ? formatDateForTable(shipByDate) : 'N/A'}
            </div>
          );
        },
        meta: { headerClassName: 'w-[120px]' },
      },
      {
        accessorKey: 'lastPackingSlipAt',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Packing Slip Printed
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) =>
          row.getValue('lastPackingSlipAt')
            ? formatDateForTable(row.getValue('lastPackingSlipAt'))
            : 'Never',
        meta: { headerClassName: 'w-[150px]' },
      },
      {
        accessorKey: 'shipped_date',
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="px-0 py-1 text-xs h-auto"
          >
            Shipped Date
            {column.getIsSorted() === 'asc' ? (
              <ArrowUp className="ml-1 h-3 w-3" />
            ) : column.getIsSorted() === 'desc' ? (
              <ArrowDown className="ml-1 h-3 w-3" />
            ) : (
              <ArrowUpDown className="ml-1 h-3 w-3" />
            )}
          </Button>
        ),
        cell: ({ row }) => {
          const date = row.original.shipped_date;
          return (
            <div className="text-xs whitespace-nowrap">
              {date ? formatDateForTable(date) : 'N/A'}
            </div>
          );
        },
        meta: { headerClassName: 'w-[120px]' },
      },
      {
        accessorKey: 'tracking_number',
        header: () => <div className="text-xs text-left px-1">Tracking #</div>, // No sorting for this one
        cell: ({ row }) => <TrackingNumberCell trackingNumber={row.original.tracking_number} />,
        enableSorting: false,
        meta: { headerClassName: 'w-[180px] min-w-[160px] text-left', cellClassName: 'text-left' },
      },
      {
        id: 'actions',
        header: () => <div className="text-xs text-center">Actions</div>,
        cell: ({ row }) => {
          const order = row.original;
          return (
            <div className="flex items-center justify-center space-x-1 whitespace-nowrap">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePrintPackingSlip(order.id)}
                disabled={printingOrderId === order.id}
                className="h-7 px-2 text-xs"
              >
                {printingOrderId === order.id ? (
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                ) : (
                  <Printer className="mr-1 h-3 w-3" />
                )}{' '}
                Print Slip
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-7 w-7 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="text-xs">
                  <DropdownMenuItem onClick={() => handleResyncOrder(order.id)}>
                    <RefreshCw className="mr-2 h-3 w-3" /> Resync Order
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      handleUpdateOrderStatus(
                        order.id,
                        order.order_status === 'on_hold' ? 'awaiting_shipment' : 'on_hold'
                      )
                    }
                  >
                    {order.order_status === 'on_hold' ? (
                      <Play className="mr-2 h-3 w-3" />
                    ) : (
                      <Pause className="mr-2 h-3 w-3" />
                    )}{' '}
                    {order.order_status === 'on_hold' ? 'Resume Order' : 'Hold Order'}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleReprocessOrderForAI(order.id)}>
                    <Zap className="mr-2 h-3 w-3" /> Reprocess for AI
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => navigator.clipboard.writeText(order.id.toString())}
                  >
                    Copy Order ID
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleArchiveOrder(order.id)}
                    className="text-red-600 hover:!text-red-700 focus:!text-red-700"
                  >
                    <Archive className="mr-2 h-3 w-3" /> Archive Order
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
        enableSorting: false,
        meta: {
          headerClassName: 'w-[150px] sticky right-0 z-10 bg-background text-center',
          cellClassName: 'sticky right-0 z-10 bg-background',
        },
      },
    ],
    [
      allTags,
      printingOrderId,
      handlePrintPackingSlip,
      handleResyncOrder,
      handleUpdateOrderStatus,
      handleReprocessOrderForAI,
      handleArchiveOrder,
    ]
  );

  const table = useReactTable({
    data: orders,
    columns,
    state: {
      sorting,
      rowSelection,
    },
    enableRowSelection: true, // Enable row selection for all rows
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: currentLimit, // Set page size from prop
      },
    },
    // manualPagination: true, // If you handle pagination server-side
    // manualSorting: true, // If you handle sorting server-side
    debugTable: process.env.NODE_ENV === 'development', // Optional: for debugging
  });

  // Legacy selection state and handlers removed.
  // react-table's rowSelection and table.getSelectedRowModel() will be used.

  return (
    <>
      <PackingSlipBatchControls
        selectedOrderIds={table
          .getSelectedRowModel()
          .rows.map(row => (row.original as SerializableOrder).id)}
      />
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader className="sticky top-0 z-20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="text-xs">
                {headerGroup.headers.map(header => (
                  <TableHead
                    key={header.id}
                    style={{ width: header.getSize() !== 0 ? header.getSize() : undefined }}
                    className={cn(
                      'h-10 px-2 py-1 font-semibold',
                      header.column.columnDef.meta?.headerClassName as string
                    )}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                    {/* Sorting Arrows */}
                    {header.column.getCanSort() &&
                      (header.column.getIsSorted() === 'asc' ? (
                        <ArrowUp className="ml-2 h-4 w-4" />
                      ) : header.column.getIsSorted() === 'desc' ? (
                        <ArrowDown className="ml-2 h-4 w-4" />
                      ) : (
                        <ArrowUpDown className="ml-2 h-4 w-4 opacity-50" />
                      ))}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => {
                const order = row.original as SerializableOrder;
                const hasTags = Array.isArray(allTags) && allTags.length > 0;
                const isPrime =
                  hasTags &&
                  Array.isArray(order.tag_ids) &&
                  (order.tag_ids as number[]).some((tagId: number) => {
                    const tag = allTags.find(t => t?.shipstation_tag_id === tagId);
                    return tag?.name === 'Amazon Prime Order';
                  });
                const isPremium =
                  hasTags &&
                  Array.isArray(order.tag_ids) &&
                  (order.tag_ids as number[]).some((tagId: number) => {
                    const tag = allTags.find(t => t?.shipstation_tag_id === tagId);
                    return tag?.name === '*** PREMIUM DELIVERY ***';
                  });
                const isRushOrder =
                  hasTags &&
                  Array.isArray(order.tag_ids) &&
                  (order.tag_ids as number[]).some((tagId: number) => {
                    const tag = allTags.find(t => t?.shipstation_tag_id === tagId);
                    return tag?.name === 'RUSH ORDER';
                  });
                const hasCustomerNotes = order.customer_notes && order.customer_notes.trim() !== '';
                const hasInternalNotes = order.internal_notes && order.internal_notes.trim() !== '';
                const isPriority =
                  order.ship_by_date &&
                  new Date(order.ship_by_date).getTime() <
                    new Date().getTime() + 2 * 24 * 60 * 60 * 1000;
                const originalIndex = orders.findIndex(o => o.id === order.id);
                const isEvenRow = originalIndex !== -1 && originalIndex % 2 === 0;

                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className={cn(
                      'text-xs hover:bg-muted/50',
                      isRushOrder && 'bg-red-500/10 hover:bg-red-500/20',
                      hasCustomerNotes && !isRushOrder && 'bg-yellow-500/10 hover:bg-yellow-500/20',
                      hasInternalNotes &&
                        !isRushOrder &&
                        !hasCustomerNotes &&
                        'bg-purple-500/10 hover:bg-purple-500/20',
                      isPrime &&
                        !isRushOrder &&
                        !hasCustomerNotes &&
                        !hasInternalNotes &&
                        'bg-blue-50 dark:bg-blue-900/20',
                      isPremium &&
                        !isPrime &&
                        !isRushOrder &&
                        !hasCustomerNotes &&
                        !hasInternalNotes &&
                        'bg-purple-50 dark:bg-purple-900/20',
                      isPriority &&
                        !isPrime &&
                        !isPremium &&
                        !isRushOrder &&
                        !hasCustomerNotes &&
                        !hasInternalNotes &&
                        'bg-amber-50 dark:bg-amber-900/20',
                      !isPrime &&
                        !isPremium &&
                        !isPriority &&
                        !isRushOrder &&
                        !hasCustomerNotes &&
                        !hasInternalNotes &&
                        !isEvenRow &&
                        'bg-muted/25'
                    )}
                  >
                    {row.getVisibleCells().map(cell => (
                      <TableCell
                        key={cell.id}
                        style={{
                          width: cell.column.getSize() !== 0 ? cell.column.getSize() : undefined,
                        }}
                        className={cn(
                          'px-2 py-1 h-10 align-middle',
                          cell.column.columnDef.meta?.cellClassName as string
                        )}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-sm">
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* Pagination Controls - ensured inside the main fragment */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-2">
          <Label htmlFor="rows-per-page" className="text-sm font-medium whitespace-nowrap">
            Rows per page
          </Label>
          <Select
            value={`${table.getState().pagination.pageSize}`}
            onValueChange={value => {
              handlePageSizeChange(value);
            }}
          >
            <SelectTrigger id="rows-per-page" className="h-8 w-[80px]">
              <SelectValue placeholder={table.getState().pagination.pageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[50, 100, 250, 500, 1000].map(pageSizeOption => (
                <SelectItem key={pageSizeOption} value={`${pageSizeOption}`}>
                  {pageSizeOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </>
  );
}
