'use client';

import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { toast } from 'react-hot-toast';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface BulkUpdateProductNameModalProps {
  isOpen: boolean;
  onOpenChange: (_isOpen: boolean) => void;
  selectedTaskIds?: string[];
  onBulkUpdateStart?: () => void;
  onBulkUpdateEnd?: () => void;
}

export function BulkUpdateProductNameModal({
  isOpen,
  onOpenChange,
  selectedTaskIds: _ = [],
  onBulkUpdateStart,
  onBulkUpdateEnd,
}: BulkUpdateProductNameModalProps) {
  const [identifierType, setIdentifierType] = useState<'sku' | 'name'>('sku');
  const [identifierValue, setIdentifierValue] = useState('');
  const [newName, setNewName] = useState('');
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleSubmit = async () => {
    if (!identifierValue.trim()) {
      toast.error('Identifier Value cannot be empty.');
      return;
    }
    if (!newName.trim()) {
      toast.error('New Product Name cannot be empty.');
      return;
    }

    const payload = {
      identifier: {
        [identifierType]: identifierValue,
      },
      newName: newName,
    };

    startTransition(async () => {
      onBulkUpdateStart?.();
      try {
        const response = await fetch('/api/print-tasks/bulk-update-name', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });

        const responseData = await response.json();

        if (!response.ok) {
          throw new Error(
            responseData.message || `Bulk name update failed with status: ${response.status}`
          );
        }

        toast.success(responseData.message || 'Product names updated successfully!');
        router.refresh();
        onOpenChange(false);
        // Reset form fields
        setIdentifierValue('');
        setNewName('');
      } catch (error) {
        if (error instanceof Error) {
          toast.error(error.message);
        } else {
          toast.error('An unexpected error occurred during bulk name update.');
        }
      } finally {
        onBulkUpdateEnd?.();
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Bulk Update Product Name</DialogTitle>
          <DialogDescription>
            Update product names in bulk. Select an identifier type, enter the value, and the new
            product name.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="identifierType" className="text-right">
              Identifier Type
            </Label>
            <Select
              value={identifierType}
              onValueChange={value => setIdentifierType(value as 'sku' | 'name')}
            >
              <SelectTrigger className="col-span-3" id="identifierType">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sku">SKU</SelectItem>
                <SelectItem value="name">Current Product Name</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="identifierValue" className="text-right">
              Identifier Value
            </Label>
            <Input
              id="identifierValue"
              value={identifierValue}
              onChange={e => setIdentifierValue(e.target.value)}
              className="col-span-3"
              placeholder={identifierType === 'sku' ? 'Enter SKU' : 'Enter current product name'}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="newName" className="text-right">
              New Product Name
            </Label>
            <Input
              id="newName"
              value={newName}
              onChange={e => setNewName(e.target.value)}
              className="col-span-3"
              placeholder="Enter new product name"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isPending}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={isPending}>
            {isPending ? 'Updating...' : 'Update Names'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
