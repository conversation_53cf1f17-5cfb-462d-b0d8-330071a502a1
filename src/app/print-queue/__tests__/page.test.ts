import 'tsconfig-paths/register';
import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach, type Mock } from 'vitest';

// Mock the client component so we can inspect props
vi.mock('../PrintQueueClient', () => ({
  default: vi.fn(() => null),
}));

// Mock aliased modules used within the page
vi.mock('@/lib/order-utils', () => ({
  detectMarketplaceOrderNumber: vi.fn(() => ({ isMarketplaceNumber: false })),
}));

vi.mock('@/lib/order-processing', () => ({
  fixInvalidStlRenderStatus: vi.fn(),
}));

vi.mock('@/lib/prisma', () => {
  const mockPrismaClient = {
    $transaction: vi.fn(async ops => Promise.all(ops)),
    printOrderTask: {
      findMany: vi.fn().mockResolvedValue([]),
      count: vi.fn().mockResolvedValue(0),
    },
    product: {
      findMany: vi.fn().mockResolvedValue([]),
    },
    order: {
      findMany: vi.fn().mockResolvedValue([]),
    },
  };
  return {
    prisma: mockPrismaClient,
  };
});

// Set up React globally
globalThis.React = React;

// Import the components after mocks
import { prisma } from '@/lib/prisma'; // Import the mocked prisma instance

import PrintQueuePage from '../page';
import { validatePrintQueueParams } from '../utils';

// Get references to mocked functions after imports
// const mockPrisma = vi.mocked(vi.importActual('@/lib/prisma')).prisma; // Old way

beforeEach(async () => {
  // Made beforeEach async
  vi.clearAllMocks();
  // Ensure mocks are reset if needed, or re-assign if the importActual strategy is tricky
});

afterEach(() => {
  vi.clearAllMocks();
});

describe('PrintQueuePage searchParams validation', () => {
  it('falls back to defaults for invalid values', () => {
    // Using type assertion to test invalid values that should be handled by the validation function
    const result = validatePrintQueueParams({
      page: 'abc',
      limit: '-5',
      status: 'invalid' as 'pending' | 'active' | 'completed' | 'all', // Type assertion to bypass type checking for test purposes
      needsReview: 'maybe' as 'yes' | 'no' | 'all', // Type assertion to bypass type checking for test purposes
    });
    expect(result.validatedPage).toBe(1);
    expect(result.validatedLimit).toBe(250);
    expect(result.validatedStatus).toBe('active');
    expect(result.validatedNeedsReview).toBe('all');
  });

  it('parses valid values correctly', () => {
    const result = validatePrintQueueParams({
      page: '2',
      limit: '10',
      status: 'pending',
      needsReview: 'yes',
      query: 'foo',
    });
    expect(result.validatedPage).toBe(2);
    expect(result.validatedLimit).toBe(10);
    expect(result.validatedStatus).toBe('pending');
    expect(result.validatedNeedsReview).toBe('yes');
    expect(result.validatedQuery).toBe('foo');
  });

  it('handles underlying errors gracefully', async () => {
    // Get reference to the mocked function
    // Use the directly defined mock client to ensure correct reference
    (prisma.printOrderTask.findMany as Mock).mockRejectedValueOnce(new Error('db fail'));

    const result = await PrintQueuePage({ searchParams: {} });

    expect(result).toBeTruthy();
    if (typeof result === 'object' && result && 'type' in result) {
      expect(typeof (result as { type: React.FC }).type).toBe('function');
    }
  });
});
