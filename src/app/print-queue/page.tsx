import { PrintOrderTask_status, Prisma } from '@prisma/client';

// Unused imports removed
import { detectMarketplaceOrderNumber } from '@/lib/order-utils'; // Import order number detection
import { prisma } from '@/lib/prisma';
import { ClientPrintTaskData } from '@/types/print-tasks'; // Import the new client-safe type

import PrintQueueClient from './PrintQueueClient'; // Import the new client component
import { validatePrintQueueParams } from './utils'; // Import from utils.ts instead of defining inline

import type { PrintQueuePageSearchParams } from './types'; // Import type definition

// import PrintQueueSummaryServer from './PrintQueueSummaryServer';

// Using validatePrintQueueParams imported from utils.ts

// Restore dynamic rendering
export const dynamic = 'force-dynamic';

// PrintQueuePageSearchParams is now imported from './types'

// --- Refactored getPrintTasks ---
// Accepts validated, individual parameters
async function getPrintTasks({
  validatedPage,
  validatedLimit,
  validatedStatus,
  validatedNeedsReview,
  validatedQuery,
  validatedShipByDateStart,
  validatedShipByDateEnd,
  validatedColor1,
  validatedColor2,
  validatedProductName,
  validatedShippingMethod,
}: {
  validatedPage: number;
  validatedLimit: number;
  validatedStatus: PrintOrderTask_status | 'all' | 'active';
  validatedNeedsReview: 'yes' | 'no' | 'all';
  validatedQuery: string;
  validatedShipByDateStart?: string;
  validatedShipByDateEnd?: string;
  validatedColor1?: string;
  validatedColor2?: string;
  validatedProductName?: string;
  validatedShippingMethod?: string;
}): Promise<{ tasks: ClientPrintTaskData[]; total: number }> {
  try {
    // console.log('====== [getPrintTasks] Starting execution ======');
    // console.log('[getPrintTasks] Input parameters:', {
    //   page: validatedPage,
    //   limit: validatedLimit,
    //   status: validatedStatus,
    //   needsReview: validatedNeedsReview,
    //   query: validatedQuery,
    //   color1: validatedColor1,
    //   color2: validatedColor2,
    //   productName: validatedProductName,
    //   shippingMethod: validatedShippingMethod,
    // });

    const skip = Math.max(0, (validatedPage - 1) * validatedLimit);
    const filters: Prisma.PrintOrderTaskWhereInput[] = [];

    // Status filter
    if (validatedStatus === 'all') {
      filters.push({
        status: {
          in: [
            PrintOrderTask_status.pending,
            PrintOrderTask_status.in_progress,
            PrintOrderTask_status.completed,
            PrintOrderTask_status.cancelled,
          ],
        },
      });
    } else if (validatedStatus === 'active') {
      filters.push({
        status: {
          in: [PrintOrderTask_status.pending, PrintOrderTask_status.in_progress],
        },
      });
    } else {
      filters.push({ status: validatedStatus });
    }

    // Needs Review filter
    if (validatedNeedsReview !== 'all') {
      filters.push({ needs_review: validatedNeedsReview === 'yes' });
    }

    // Product Name filter
    if (validatedProductName && validatedProductName !== 'all') {
      filters.push({
        Product: {
          name: validatedProductName,
        },
      });
    }

    // Shipping Method filter
    if (validatedShippingMethod && validatedShippingMethod !== 'all') {
      filters.push({
        Order: {
          is: {
            requested_shipping_service: validatedShippingMethod,
          },
        },
      });
    }

    // Date filter
    const dateFilter: { gte?: Date; lte?: Date } = {};
    let dateFilterApplied = false;

    // Handle special keywords "today" and "tomorrow" if sent in validatedShipByDateStart
    // and validatedShipByDateEnd is not set (implying a single day target)
    if (validatedShipByDateStart && !validatedShipByDateEnd) {
      const todayDate = new Date();
      todayDate.setUTCHours(0, 0, 0, 0);

      const keyword = validatedShipByDateStart.toLowerCase();

      if (keyword === 'today') {
        dateFilter.gte = new Date(todayDate);
        const endOfToday = new Date(todayDate);
        endOfToday.setUTCHours(23, 59, 59, 999);
        dateFilter.lte = endOfToday;
        dateFilterApplied = true;
      } else if (keyword === 'tomorrow') {
        const tomorrowDate = new Date(todayDate);
        tomorrowDate.setUTCDate(todayDate.getUTCDate() + 1);
        dateFilter.gte = new Date(tomorrowDate); // Start of tomorrow
        const endOfTomorrow = new Date(tomorrowDate);
        endOfTomorrow.setUTCHours(23, 59, 59, 999);
        dateFilter.lte = endOfTomorrow;
        dateFilterApplied = true;
      }
    }

    // If not a keyword handled above, or if a date range is explicitly provided by start AND end dates
    if (!dateFilterApplied) {
      if (validatedShipByDateStart) {
        try {
          // Attempt to parse validatedShipByDateStart as a date string
          const startDate = new Date(validatedShipByDateStart);
          if (!isNaN(startDate.getTime())) {
            // Check if parsing was successful
            startDate.setUTCHours(0, 0, 0, 0);
            dateFilter.gte = startDate;
            dateFilterApplied = true;

            // If only startDate is provided (and it's a parsable date, not a keyword),
            // and no endDate is given, filter for that single day.
            if (!validatedShipByDateEnd) {
              const endOfStartDate = new Date(startDate);
              endOfStartDate.setUTCHours(23, 59, 59, 999);
              dateFilter.lte = endOfStartDate;
            }
          }
        } catch (_ignoredError) {
          // console.warn('Invalid shipByDateStart format received:', validatedShipByDateStart);
        }
      }
      // If validatedShipByDateEnd is provided, it defines the end of the range.
      // This will override the single-day lte if an explicit end date is given.
      if (validatedShipByDateEnd) {
        try {
          const endDate = new Date(validatedShipByDateEnd);
          if (!isNaN(endDate.getTime())) {
            // Check if parsing was successful
            endDate.setUTCHours(23, 59, 59, 999);
            dateFilter.lte = endDate;
            dateFilterApplied = true; // Ensure applied if only endDate was valid (and startDate might have been invalid)
          }
        } catch (_ignoredError) {
          // console.warn('Invalid shipByDateEnd format received:', validatedShipByDateEnd);
        }
      }
    }

    if (dateFilterApplied && (dateFilter.gte || dateFilter.lte)) {
      filters.push({ ship_by_date: dateFilter });
    }

    const orConditions: Prisma.PrintOrderTaskWhereInput[] = [];

    // Query filter OR conditions
    if (validatedQuery) {
      const trimmedQuery = validatedQuery.trim();
      if (trimmedQuery) {
        const detection = detectMarketplaceOrderNumber(trimmedQuery);
        if (detection.isMarketplaceNumber) {
          orConditions.push({ marketplace_order_number: { contains: trimmedQuery } });
          // For Amazon-style order IDs, also try without dashes
          if (trimmedQuery.includes('-')) {
            const withoutDashes = trimmedQuery.replace(/-/g, '');
            orConditions.push({ marketplace_order_number: { contains: withoutDashes } });
          }
        } else {
          orConditions.push({ marketplace_order_number: { contains: trimmedQuery } });
          orConditions.push({ custom_text: { contains: trimmedQuery } });
          orConditions.push({ color_1: { contains: trimmedQuery } });
          orConditions.push({ color_2: { contains: trimmedQuery } });
          orConditions.push({ Product: { name: { contains: trimmedQuery } } });
          orConditions.push({ Product: { sku: { contains: trimmedQuery } } });

          const parsedId = parseInt(trimmedQuery, 10);
          if (!isNaN(parsedId)) {
            orConditions.push({ orderItemId: parsedId });
            // DO NOT search PrintOrderTask.id (CUID string) with parsedId
          }
        }
      }
    }

    // Color 1 filter
    if (validatedColor1) {
      if (validatedColor1 === 'none') {
        filters.push({
          OR: [{ color_1: { equals: null } }, { color_1: { equals: '' } }],
        });
      } else {
        filters.push({ color_1: { contains: validatedColor1 } });
      }
    }

    // Color 2 filter
    if (validatedColor2) {
      if (validatedColor2 === 'none') {
        filters.push({
          OR: [{ color_2: { equals: null } }, { color_2: { equals: '' } }],
        });
      } else {
        filters.push({ color_2: { contains: validatedColor2 } });
      }
    }

    const whereClause: Prisma.PrintOrderTaskWhereInput = {
      AND: filters,
      ...(orConditions.length > 0 && { OR: orConditions }),
    };

    // console.log('[getPrintTasks] Final whereClause:', JSON.stringify(whereClause, null, 2));

    const [tasksData, total] = await prisma.$transaction([
      prisma.printOrderTask.findMany({
        where: whereClause,
        include: {
          Order: true,
          Product: true,
          Customer: true,
          OrderItem: true,
        },
        orderBy: { created_at: 'desc' },
        take: validatedLimit,
        skip,
      }),
      prisma.printOrderTask.count({ where: whereClause }),
    ]);

    // console.log(`[getPrintTasks] Fetched ${tasksData.length} tasks, total count ${total}`);

    const tasks: ClientPrintTaskData[] = tasksData.map(task => {
      const {
        // Destructure relations and dates that need special handling/conversion
        Order: rawOrder,
        Product: rawProduct,
        // Unused variables prefixed with underscore
        Customer: _rawCustomer,
        OrderItem: _rawOrderItem,
        // verifiedByUser removed as it doesn't exist in the schema
        created_at: task_created_at,
        updated_at: task_updated_at,
        ship_by_date: task_ship_by_date,
        // Prisma fields that are part of PrintOrderTask but might be relations or need specific handling
        // Ensure all direct scalar fields of PrintOrderTask are captured by `...coreTaskFields`
        ...coreTaskFields // These are the scalar fields from PrismaPrintOrderTask
      } = task;

      if (!rawProduct) {
        // This should ideally not happen with `include: { product: true }`
        // and proper data integrity. Consider logging or specific error handling.
        console.error(`Task ID ${task.id} is missing product data. This task may be incomplete.`);
        // To prevent crashes, you might return a distinctly identifiable error object or skip this task,
        // but that would require changing the return type or filtering later.
        // For now, proceed with an assertion, assuming product should always be there.
      }

      return {
        ...coreTaskFields, // Spreads all scalar fields from PrintOrderTask not in Omit list of ClientPrintTaskData

        // Verification fields removed as they don't exist in the schema
        verifiedAt: null,
        verifiedByUser: null,

        // Converted dates (as per ClientPrintTaskData definition)
        created_at: task_created_at.toISOString(),
        updated_at: task_updated_at ? task_updated_at.toISOString() : null,
        ship_by_date: task_ship_by_date ? task_ship_by_date.toISOString() : null,
        // Note: 'verifiedAt' is inherited as Date | null from PrismaPrintOrderTask if not Omitted or redefined in ClientPrintTaskData.
        // If ClientPrintTaskData needs 'verifiedAt' as string, it should be defined in ClientPrintTaskData and converted here.
        // Current ClientPrintTaskData definition does not redefine 'verifiedAt', so it remains Date | null via coreTaskFields.

        // Structured product object (as per ClientPrintTaskData definition)
        product: {
          ...rawProduct!,
          weight: rawProduct!.weight?.toString() ?? null,
          item_weight_value: rawProduct!.item_weight_value?.toString() ?? null,
          createdAt: rawProduct!.createdAt.toISOString(),
          updatedAt: rawProduct!.updatedAt ? rawProduct!.updatedAt.toISOString() : null,
        },

        // Optional structured order object (as per ClientPrintTaskData definition)
        order: rawOrder
          ? {
              requested_shipping_service: rawOrder.requested_shipping_service ?? null,
              marketplace: rawOrder.marketplace ?? null,
              marketplace_order_number: rawOrder.shipstation_order_number ?? null, // Corrected source field
            }
          : undefined,

        // Optional product_name (as per ClientPrintTaskData definition)
        product_name: rawProduct?.name ?? undefined, // Use undefined for optional fields not present

        orderLink: rawOrder ? `/orders/${rawOrder.id}` : undefined,
      };
    });

    // console.log('[getPrintTasks] Successfully transformed tasks for client.');
    return { tasks, total };
  } catch (error) {
    console.error('[getPrintTasks] Error fetching print tasks:', error);
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      console.error(`[getPrintTasks] Prisma Error Code: ${error.code}`);
      // console.error(`[getPrintTasks] Prisma Error Meta: ${JSON.stringify(error.meta)}`);
    }
    // Instead of throwing, return an empty list and 0 total to prevent page crash
    return { tasks: [], total: 0 };
  }
}

// Helper functions to fetch distinct values for filters
async function getDistinctProductNamesForTasks(): Promise<string[]> {
  try {
    const products = await prisma.product.findMany({
      where: {
        PrintOrderTask: {
          some: {},
        },
      },
      distinct: ['name'],
      select: { name: true },
      orderBy: { name: 'asc' },
    });
    return products.map(p => p.name).filter((name): name is string => !!name);
  } catch (error) {
    console.error('[PrintQueuePage] Error fetching distinct product names:', error);
    return [];
  }
}

async function getDistinctShippingMethodsForTasks(): Promise<string[]> {
  try {
    const orders = await prisma.order.findMany({
      where: {
        PrintOrderTask: {
          some: {},
        },
        requested_shipping_service: {
          not: null,
        },
      },
      distinct: ['requested_shipping_service'],
      select: { requested_shipping_service: true },
      orderBy: { requested_shipping_service: 'asc' },
    });
    return orders
      .map(o => o.requested_shipping_service)
      .filter((method): method is string => !!method);
  } catch (error) {
    console.error('[PrintQueuePage] Error fetching distinct shipping methods:', error);
    return [];
  }
}

// Now importing validatePrintQueueParams from utils.ts instead of exporting it here

export default async function PrintQueuePage({
  searchParams,
}: {
  searchParams: PrintQueuePageSearchParams;
}) {
  const formattedNow = new Date().toISOString();

  try {
    const {
      validatedPage,
      validatedLimit,
      validatedStatus,
      validatedNeedsReview,
      validatedQuery,
      shipByDateStartParam,
      shipByDateEndParam,
      color1Param,
      color2Param,
      validatedProductName,
      validatedShippingMethod,
    } = validatePrintQueueParams(searchParams);

    // --- Call getPrintTasks & getDistinctProductNames --- Fetch in parallel
    const [{ tasks, total }, productNames, shippingMethods] = await Promise.all([
      getPrintTasks({
        validatedPage,
        validatedLimit,
        validatedStatus,
        validatedNeedsReview,
        validatedQuery: validatedQuery,
        validatedShipByDateStart: shipByDateStartParam,
        validatedShipByDateEnd: shipByDateEndParam,
        validatedColor1: color1Param,
        validatedColor2: color2Param,
        validatedProductName: validatedProductName,
        validatedShippingMethod: validatedShippingMethod,
      }),
      getDistinctProductNamesForTasks(),
      getDistinctShippingMethodsForTasks(),
    ]);

    const initialFilters: PrintQueuePageSearchParams = {
      page: String(validatedPage),
      limit: String(validatedLimit),
      status: validatedStatus as PrintOrderTask_status | 'all' | 'active',
      needsReview: validatedNeedsReview as 'yes' | 'no' | 'all',
      query: validatedQuery,
      shipByDateStart: shipByDateStartParam,
      shipByDateEnd: shipByDateEndParam,
      color1: color1Param,
      color2: color2Param,
      productName: validatedProductName,
      shippingMethod: validatedShippingMethod,
    };

    return (
      <PrintQueueClient
        tasks={tasks} // This 'tasks' now conforms to ClientPrintTaskData[]
        totalTasks={total}
        page={validatedPage}
        limit={validatedLimit}
        productNames={productNames}
        availableShippingMethods={shippingMethods} // Pass shipping methods
        initialFilters={initialFilters}
        formattedNow={formattedNow}
      />
    );
  } catch (error) {
    console.error('[PrintQueuePage] Error fetching data:', error);
    let errorMessage = 'Failed to load print queue data.';
    if (error instanceof Error) {
      errorMessage += ` Error: ${error.message}`;
    }

    return (
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Print Queue</h2>
        </div>
        <div className="flex h-[calc(100vh-200px)] items-center justify-center rounded-md border border-dashed p-8 text-center animate-in fade-in-50">
          <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
            <p className="mt-4 text-lg font-semibold text-destructive">Error Loading Data</p>
            <p className="mb-4 mt-2 text-sm text-muted-foreground">{errorMessage}</p>
          </div>
        </div>
      </div>
    );
  }
}

// TODO: Add tests for validation logic
