'use client';

import { AutoRefresher } from '@/components/auto-refresher';
import { LimitSelector } from '@/components/limit-selector';
import { OrdersPagination } from '@/components/orders-pagination';
import { PrintQueueFilters } from '@/components/print-queue-filters';
import { PrintQueueHeader } from '@/components/print-queue-header';
import { PrintQueueTable } from '@/components/print-queue-table';
import { PrintQueueTaskTotals } from '@/components/print-queue-task-totals';
import { PrintTaskDetailModal } from '@/components/print-task-detail-modal';
import { PrintQueueModalProvider, usePrintQueueModal } from '@/contexts/PrintQueueModalContext';
import { ClientPrintTaskData } from '@/types/print-tasks';

import { PrintQueuePageSearchParams } from './types';

interface PrintQueueClientProps {
  tasks: ClientPrintTaskData[];
  totalTasks: number;
  page: number;
  limit: number;
  productNames: string[];
  availableShippingMethods?: string[];
  initialFilters: PrintQueuePageSearchParams;
  formattedNow: string;
}

export default function PrintQueueClient({
  tasks,
  totalTasks,
  page,
  limit,
  productNames,
  availableShippingMethods,
  initialFilters,
  formattedNow,
}: PrintQueueClientProps) {
  // The state for the modal (isOpen, onOpenChange) is managed by PrintQueueModalProvider
  // We can access its functions via the usePrintQueueModal hook

  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalTasks / limit);

  // This component needs to be a child of PrintQueueModalProvider to use the hook,
  // so we'll create a sub-component or move the part that needs the context.
  // For simplicity here, we'll assume PrintQueueClient itself isn't calling the hook directly,
  // but rather the logic is within the onSelectTask prop passed to PrintQueueTable,
  // which *is* a child of the Provider.
  // To make this explicit and allow PrintQueueTable to use the hook internally OR
  // for PrintQueueClient to use it if it were also wrapped, let's define the handler here.

  // Inner component to safely use the hook
  const TableInteractionHandler = () => {
    const { setSelectedTask, setIsModalOpen } = usePrintQueueModal();

    return (
      <PrintQueueTable
        data={tasks}
        onSelectTask={(task: ClientPrintTaskData) => {
          setSelectedTask(task);
          setIsModalOpen(true);
        }}
      />
    );
  };

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      {/* Pass necessary props to PrintQueueHeader */}
      <PrintQueueHeader formattedNow={formattedNow} />
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Pass tasks to PrintQueueTaskTotals - ensure it can handle ClientPrintTaskData */}
        <PrintQueueTaskTotals tasks={tasks} />
      </div>
      <div className="rounded-md border bg-background p-4 shadow">
        {/* Pass props to PrintQueueFilters, assuming it expects these names based on previous usage */}
        {/* If errors persist here, PrintQueueFiltersProps interface might need checking */}
        <PrintQueueFilters
          currentFilters={initialFilters} // Pass validated params as current filters
          availableProductNames={productNames} // Pass available product names
          availableShippingMethods={availableShippingMethods}
        />
        {/* Wrap Table and Modal with Provider */}
        <PrintQueueModalProvider>
          <TableInteractionHandler /> {/* Use the new inner component */}
          <PrintTaskDetailModal />
        </PrintQueueModalProvider>
      </div>
      <div className="mt-4 flex items-center justify-between">
        {/* Pass current limit to LimitSelector */}
        <LimitSelector currentLimit={limit} />
        {/* Pass calculated totalPages to OrdersPagination */}
        <OrdersPagination currentPage={page} totalPages={totalPages} limit={limit} />
      </div>
      {/* Pass interval in seconds to AutoRefresher */}
      <AutoRefresher intervalSeconds={60} />
      <div className="text-xs text-muted-foreground">Last updated: {formattedNow}</div>
    </div>
  );
}
