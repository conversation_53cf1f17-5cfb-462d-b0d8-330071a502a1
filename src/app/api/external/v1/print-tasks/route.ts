import crypto from 'crypto';

import { Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

import { withApiKeyAuth } from '@/lib/api/auth';
import { CreatePrintTaskQuerySchema, CreatePrintTaskDto } from '@/lib/api/external/v1/schemas';
import { prisma } from '@/lib/prisma';

async function handlePrintTaskRequest(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const action = searchParams.get('action');

  // Convert searchParams to a plain object for Zod parsing
  const queryParams: Record<string, string> = {};
  searchParams.forEach((value, key) => {
    // Zod coercion will handle type conversion from string
    // For keys that can appear multiple times, this will take the last one.
    // If multiple values for a key are needed, specific handling is required.
    queryParams[key] = value;
  });

  try {
    if (action === 'create') {
      const validatedData = CreatePrintTaskQuerySchema.parse(queryParams) as CreatePrintTaskDto;
      // Remove 'action' from data to be saved
      const { ...taskData } = validatedData;

      const printTask = await prisma.printOrderTask.create({
        data: {
          ...taskData,
          id: crypto.randomUUID(), // Required field
        },
      });
      return NextResponse.json({ success: true, data: printTask }, { status: 201 });
    }

    if (action === 'list') {
      // Define a Zod schema for list filters if it becomes complex
      // For now, simple direct checks.
      const whereClause: Prisma.PrintOrderTaskWhereInput = {};
      if (queryParams.marketplaceOrderNumber) {
        whereClause.marketplace_order_number = String(queryParams.marketplaceOrderNumber);
      }
      if (queryParams.status) {
        whereClause.status = String(
          queryParams.status
        ) as Prisma.PrintOrderTaskWhereInput['status']; // Needs enum validation
      }
      if (queryParams.productId) {
        whereClause.productId = Number(queryParams.productId);
      }
      // Add more filters as needed

      const printTasks = await prisma.printOrderTask.findMany({
        where: whereClause,
      });
      return NextResponse.json({ success: true, data: printTasks }, { status: 200 });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action specified' },
      { status: 400 }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }
    console.error('[API_EXTERNAL_PRINT_TASKS_ERROR]', error);
    return NextResponse.json(
      { success: false, error: 'Could not process request' },
      { status: 500 }
    );
  }
}

export const GET = withApiKeyAuth(handlePrintTaskRequest);
