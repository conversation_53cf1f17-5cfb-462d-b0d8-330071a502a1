import { Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

import { withApi<PERSON>eyAuth } from '@/lib/api/auth';
import { UpdatePrintTaskQuerySchema, UpdatePrintTaskDto } from '@/lib/api/external/v1/schemas';
import { prisma } from '@/lib/prisma';

async function handleSinglePrintTaskRequest(
  req: NextRequest,
  context?: { params?: { taskId?: string } } // Make context and taskId potentially optional to align with HOF
) {
  const taskId = context?.params?.taskId;
  if (!taskId) {
    return NextResponse.json({ success: false, error: 'Task ID missing in path' }, { status: 400 });
  }

  const { searchParams } = req.nextUrl;
  const action = searchParams.get('action');

  // Convert searchParams to a plain object for Zod parsing
  const queryParams: Record<string, string> = {};
  searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  try {
    if (action === 'details') {
      const printTask = await prisma.printOrderTask.findUnique({
        where: { id: taskId },
        // include: { order: true, product: true } // Optionally include relations
      });
      if (!printTask) {
        return NextResponse.json(
          { success: false, error: 'Print task not found' },
          { status: 404 }
        );
      }
      return NextResponse.json({ success: true, data: printTask }, { status: 200 });
    }

    if (action === 'update') {
      // Add taskId from path to queryParams for Zod validation
      const dataToValidate = { ...queryParams, taskId };
      const validatedData = UpdatePrintTaskQuerySchema.parse(dataToValidate) as UpdatePrintTaskDto;

      // Separate taskId from the actual data to update
      const { taskId: validatedTaskId, ...updatePayload } = validatedData;

      // Ensure no one tries to update the ID via query params, though schema should catch it if type is wrong.
      // The actual ID from the path (`taskId`) is authoritative here.
      if (validatedTaskId && validatedTaskId !== taskId) {
        return NextResponse.json(
          { success: false, error: 'Task ID in query does not match path ID.' },
          { status: 400 }
        );
      }

      // Remove 'action' if present in updatePayload
      if ('action' in updatePayload) {
        delete (updatePayload as { action?: string }).action;
      }

      const updatedPrintTask = await prisma.printOrderTask.update({
        where: { id: taskId },
        data: updatePayload,
      });
      return NextResponse.json({ success: true, data: updatedPrintTask }, { status: 200 });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action specified for task ID' },
      { status: 400 }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters for task operation',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
      return NextResponse.json(
        { success: false, error: 'Print task not found for update' },
        { status: 404 }
      );
    }
    console.error(`[API_EXTERNAL_PRINT_TASK_${action?.toUpperCase()}_ERROR]`, error);
    return NextResponse.json(
      { success: false, error: 'Could not process request for print task' },
      { status: 500 }
    );
  }
}

export const GET = withApiKeyAuth(handleSinglePrintTaskRequest);
