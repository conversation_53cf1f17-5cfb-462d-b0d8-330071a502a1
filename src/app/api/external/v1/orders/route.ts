import { Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

import { withApi<PERSON>eyAuth } from '@/lib/api/auth';
import { CreateOrderQuerySchema, CreateOrderDto } from '@/lib/api/external/v1/schemas';
import { prisma } from '@/lib/prisma';

async function handleOrderRequest(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const action = searchParams.get('action');

  const queryParams: Record<string, string> = {};
  searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  try {
    if (action === 'create') {
      const _validatedData = CreateOrderQuerySchema.parse(queryParams) as CreateOrderDto;

      // TODO: Handle nested customer/items if CreateOrderQuerySchema is expanded for it.
      // For now, assumes all fields are direct properties of Order.
      const validatedDataFromSchema = CreateOrderQuerySchema.parse(queryParams);

      // Now, construct the actual data payload for Prisma, excluding any extraneous fields like 'action'.
      // Prisma expects data that matches its model schema (Prisma.OrderCreateInput).
      const orderCreateInput: Prisma.OrderCreateInput = {
        // Map fields from validatedDataFromSchema to OrderCreateInput
        // This explicit mapping avoids 'any' and ensures type safety.
        ...(validatedDataFromSchema.shipstation_order_id && {
          shipstation_order_id: validatedDataFromSchema.shipstation_order_id,
        }),
        ...(validatedDataFromSchema.shipstation_order_number && {
          shipstation_order_number: validatedDataFromSchema.shipstation_order_number,
        }),
        ...(validatedDataFromSchema.customer_name && {
          customer_name: validatedDataFromSchema.customer_name,
        }),
        ...(validatedDataFromSchema.customerId && {
          customerId: validatedDataFromSchema.customerId,
        }),
        order_status: validatedDataFromSchema.order_status || 'awaiting_shipment', // Default if not provided
        ...(validatedDataFromSchema.order_date && {
          order_date: validatedDataFromSchema.order_date,
        }),
        ...(validatedDataFromSchema.payment_date && {
          payment_date: validatedDataFromSchema.payment_date,
        }),
        ...(validatedDataFromSchema.ship_by_date && {
          ship_by_date: validatedDataFromSchema.ship_by_date,
        }),
        ...(validatedDataFromSchema.shipping_price && {
          shipping_price: validatedDataFromSchema.shipping_price,
        }),
        ...(validatedDataFromSchema.tax_amount && {
          tax_amount: validatedDataFromSchema.tax_amount,
        }),
        ...(validatedDataFromSchema.discount_amount && {
          discount_amount: validatedDataFromSchema.discount_amount,
        }),
        total_price: validatedDataFromSchema.total_price, // Assuming total_price is mandatory in schema
        ...(validatedDataFromSchema.requested_shipping_service && {
          requested_shipping_service: validatedDataFromSchema.requested_shipping_service,
        }),
        ...(validatedDataFromSchema.carrier_code && {
          carrier_code: validatedDataFromSchema.carrier_code,
        }),
        ...(validatedDataFromSchema.service_code && {
          service_code: validatedDataFromSchema.service_code,
        }),
        ...(validatedDataFromSchema.customer_notes && {
          customer_notes: validatedDataFromSchema.customer_notes,
        }),
        ...(validatedDataFromSchema.internal_notes && {
          internal_notes: validatedDataFromSchema.internal_notes,
        }),
        ...(validatedDataFromSchema.marketplace && {
          marketplace: validatedDataFromSchema.marketplace,
        }),
        ...(validatedDataFromSchema.amount_paid && {
          amount_paid: validatedDataFromSchema.amount_paid,
        }),
        // TODO: Handle items, customer relations if they are part of CreateOrderQuerySchema and Prisma.OrderCreateInput
      };

      const order = await prisma.order.create({
        data: orderCreateInput,
      });
      return NextResponse.json({ success: true, data: order }, { status: 201 });
    }

    if (action === 'list') {
      const whereClause: Prisma.OrderWhereInput = {};
      if (queryParams.marketplaceOrderNumber) {
        // Assuming you might filter by this
        // Adjust field name if it's different on Order model, e.g. shipstation_order_number
        whereClause.shipstation_order_number = String(queryParams.marketplaceOrderNumber);
      }
      if (queryParams.order_status) {
        whereClause.order_status = String(queryParams.order_status);
      }
      if (queryParams.customerId) {
        whereClause.customerId = Number(queryParams.customerId);
      }
      // Add more filters as needed for Order listing

      const orders = await prisma.order.findMany({
        where: whereClause,
        // include: { items: true, customer: true } // Optionally include relations
      });
      return NextResponse.json({ success: true, data: orders }, { status: 200 });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action specified for orders' },
      { status: 400 }
    );
  } catch (error) {
    if (error instanceof ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters for order operation',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    console.error('[API_EXTERNAL_ORDERS_ERROR]', error);
    return NextResponse.json(
      { success: false, error: 'Could not process order request' },
      { status: 500 }
    );
  }
}

export const GET = withApiKeyAuth(handleOrderRequest);
