import { NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

export async function GET(): Promise<NextResponse> {
  try {
    const defs = await prisma.aiReportDefinition.findMany({
      orderBy: { name: 'asc' },
      select: {
        id: true,
        slug: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
      },
    });
    return NextResponse.json({ definitions: defs });
  } catch (err) {
    return NextResponse.json({ error: (err as Error).message }, { status: 500 });
  }
}
