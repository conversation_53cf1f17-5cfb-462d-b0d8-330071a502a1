import { PrintOrderTask_status, Prisma } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
// z import removed - currently unused

import { planWithAi, aiConcurrency } from '@/lib/plannerV2/aiCaller';
import { greedyBuild, TaskSequence, Task } from '@/lib/plannerV2/greedyBuild';
import { PlannerJob, canonicalSku, groupJobsBySku } from '@/lib/plannerV2/groupJobsBySku';
import { splitTrivialGroups } from '@/lib/plannerV2/splitTrivialGroups';
import { verifySequence } from '@/lib/plannerV2/verifySequence';
import { prisma } from '@/lib/prisma';
import { logToFile } from '@/lib/utils/logToFile';

export const dynamic = 'force-dynamic';

// InputSchema removed - currently unused

async function fetchJobs(): Promise<PlannerJob[]> {
  const pending = await prisma.printOrderTask.findMany({
    where: { status: PrintOrderTask_status.pending, needs_review: false },
    include: {
      Product: true,
      Order: { select: { marketplace: true, requested_shipping_service: true } },
    },
    orderBy: { ship_by_date: 'asc' },
    take: 200,
  });

  return pending.map(task => ({
    id: `${task.id}`,
    sku: task.Product?.sku ?? null,
    groupingSku: canonicalSku(task.Product?.sku ?? null),
    order: {
      marketplace: task.Order?.marketplace ?? null,
      marketplace_order_number: task.marketplace_order_number ?? null,
      requested_shipping_service: task.Order?.requested_shipping_service ?? null,
    },
    color1: task.color_1,
    color2: task.color_2,
    customText: task.custom_text,
    quantity: task.quantity,
    shipByDate: task.ship_by_date?.toISOString() ?? null,
  }));
}

function applyOrderHeuristic(tasks: Task[]): Task[] {
  return tasks.sort((a, b) => {
    const aColor = a.colorsLoaded[0] || '';
    const bColor = b.colorsLoaded[0] || '';
    return aColor.localeCompare(bColor);
  });
}

async function processAiGroups(
  groups: PlannerJob[][]
): Promise<{ tasks: Task[]; usedAi: boolean }> {
  const results: Task[] = [];
  let usedAi = false;
  const queue = [...groups];

  const worker = async () => {
    while (queue.length > 0) {
      const current = queue.shift();
      if (!current || current.length === 0) continue;

      try {
        // Try AI planning first
        const seq = await planWithAi(current);
        if (seq) {
          usedAi = true;
          seq.taskSequence.forEach(t => results.push(t));
          continue;
        }
      } catch (error) {
        console.error('AI planning failed, falling back to greedy:', error);
      }

      // Fall back to greedy algorithm if AI fails or returns null
      try {
        const fb = greedyBuild(current);
        fb.taskSequence.forEach(t => results.push(t));
      } catch (error) {
        console.error('Greedy build failed for group:', current, error);
      }
    }
  };

  const workers = Array.from({ length: aiConcurrency }, () => worker());
  await Promise.all(workers);

  return { tasks: results, usedAi };
}

export async function POST(_req: NextRequest) {
  const start = Date.now();

  const jobs = await fetchJobs();
  if (jobs.length === 0) {
    return NextResponse.json({
      success: true,
      taskSequence: { metadata: { totalJobs: 0, totalTasks: 0 }, taskSequence: [] },
    });
  }

  const grouped = groupJobsBySku(jobs);
  const { readyTasks, aiGroups } = splitTrivialGroups(grouped);

  let tasks: Task[] = [];
  readyTasks.forEach(rt => {
    const seq = greedyBuild(rt);
    tasks = tasks.concat(seq.taskSequence);
  });

  const aiResult = await processAiGroups(aiGroups);
  tasks = tasks.concat(aiResult.tasks);

  tasks.forEach((t, idx) => {
    t.taskNumber = idx + 1;
  });

  tasks = applyOrderHeuristic(tasks);

  let finalSeq: TaskSequence = {
    metadata: { totalJobs: jobs.length, totalTasks: tasks.length },
    taskSequence: tasks,
  };

  const check = verifySequence(jobs, finalSeq);
  if (!check.ok) {
    await logToFile({
      type: 'verifySequence-failure',
      problems: JSON.stringify(check.problems),
      totalJobs: jobs.length,
      totalTasks: tasks.length,
    });
    finalSeq = greedyBuild(jobs);
  }

  // Create a properly typed input object
  const reportData = {
    id: `planner_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    reportId: 'planner-v2',
    inputJson: { jobList: jobs } as unknown as Prisma.InputJsonValue,
    outputJson: JSON.stringify(finalSeq) as unknown as Prisma.InputJsonValue,
    rawResponse: aiResult.usedAi ? 'ai+greedy' : 'greedy',
    status: 'success' as const,
    finishedAt: new Date(),
    durationMs: Date.now() - start,
    aiUsed: aiResult.usedAi,
    numAiCalls: aiGroups.length,
  };

  // Save the report, ignoring any errors
  await prisma.aiReportRun.create({ data: reportData }).catch(error => {
    console.error('Failed to save AI report:', error);
  });

  await logToFile({ type: 'planner-v2', jobs: jobs.length, aiCalls: aiGroups.length });

  return NextResponse.json({ success: true, taskSequence: finalSeq });
}
