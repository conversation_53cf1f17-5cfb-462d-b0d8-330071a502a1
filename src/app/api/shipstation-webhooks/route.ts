// src/app/api/shipstation-webhooks/route.ts
import crypto from 'crypto';

import { PrismaClient as _PrismaClient } from '@prisma/client'; // Your Prisma client - uncomment when ready
import { NextRequest, NextResponse } from 'next/server';

// import { populatePrintQueueForOrder } from '@/utils/print-queue-logic'; // Hypothetical - uncomment and adjust path
// import { updateOrderStatusInDb } from '@/utils/order-db-logic'; // Hypothetical - uncomment and adjust path

const SHIPSTATION_API_SECRET = process.env.SHIPSTATION_API_SECRET;
const SHIPSTATION_API_KEY = process.env.SHIPSTATION_API_KEY; // Added for fetching full resource

async function fetchFullResource(resourceUrl: string) {
  if (!SHIPSTATION_API_KEY || !SHIPSTATION_API_SECRET) {
    console.error('ShipStation API Key or Secret is not configured for fetching full resource.');
    return null;
  }
  try {
    const response = await fetch(resourceUrl, {
      headers: {
        Authorization: `Basic ${Buffer.from(`${SHIPSTATION_API_KEY}:${SHIPSTATION_API_SECRET}`).toString('base64')}`,
      },
    });
    if (!response.ok) {
      console.error(
        `Failed to fetch full resource from ${resourceUrl}. Status: ${response.status}`
      );
      return null;
    }
    return await response.json();
  } catch (error) {
    console.error(`Error fetching full resource from ${resourceUrl}:`, error);
    return null;
  }
}

export async function POST(req: NextRequest) {
  if (!SHIPSTATION_API_SECRET) {
    console.error('ShipStation API Secret is not configured.');
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }

  try {
    const rawBody = await req.text();
    const signature = req.headers.get('x-shipstation-signature');

    // 1. Verify the webhook signature (CRITICAL FOR SECURITY)
    const expectedSignature = crypto
      .createHmac('sha256', SHIPSTATION_API_SECRET)
      .update(rawBody)
      .digest('base64');

    if (signature !== expectedSignature) {
      console.warn('Invalid ShipStation webhook signature.');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = JSON.parse(rawBody);
    const resourceType = payload.resource_type; // e.g., "ORDER_NOTIFY", "SHIP_NOTIFY"
    const resourceUrl = payload.resource_url; // URL to fetch full details

    console.log(`Received ShipStation webhook: ${resourceType} for resource URL: ${resourceUrl}`);
    // For debugging, you might want to log the payload, but be mindful of sensitive data in production logs.
    // console.log('Payload:', JSON.stringify(payload, null, 2));

    // 2. Fetch the full resource to ensure up-to-date and complete data
    const fullResourceData = await fetchFullResource(resourceUrl);

    if (!fullResourceData) {
      console.error(
        `Could not fetch full data for ${resourceType} from ${resourceUrl}. Processing with payload data might be risky or incomplete.`
      );
      // Decide if you want to proceed with payload data or return an error
      // For now, we'll log and potentially proceed with caution or skip.
      // return NextResponse.json({ error: 'Failed to fetch full resource data' }, { status: 500 });
    }

    // Use fullResourceData if available, otherwise fallback to payload (with caution)
    const eventData = fullResourceData || payload;

    // 3. Process the webhook based on its type
    if (resourceType === 'ORDER_NOTIFY') {
      // Ensure orderId is available, its name might vary in the payload (e.g. orderId, orderKey, id)
      // Check ShipStation's documentation for the exact field name in the full order object.
      // Assuming 'orderId' from common ShipStation payloads.
      const orderId = eventData.orderId || (eventData.orders && eventData.orders[0]?.orderId);

      console.log(
        `Processing ORDER_NOTIFY for order ID: ${orderId || 'N/A (check payload structure)'}`
      );
      if (orderId) {
        // TODO:
        // a. Upsert order data into your Prisma database using 'eventData'
        //    - This involves mapping ShipStation order fields to your Prisma schema.
        //    - Example: await prisma.order.upsert({ where: { shipstation_order_id: orderId }, update: { ... }, create: { ... } });
        //
        // b. Check order status from 'eventData.orderStatus'
        //    const status = eventData.orderStatus;
        //    if (status === 'awaiting_shipment' || status === 'on_hold') {
        //      console.log(`Order ${orderId} is ${status}, potentially triggering print queue population.`);
        //      // await populatePrintQueueForOrder(orderId); // Pass the internal DB order ID if different
        //    } else {
        //      console.log(`Order ${orderId} status is ${status}.`);
        //      // Handle other status changes if necessary (e.g., 'cancelled', 'awaiting_payment')
        //    }
        console.log('TODO: Implement ORDER_NOTIFY processing logic for orderId:', orderId);
      } else {
        console.warn(
          'ORDER_NOTIFY received without a clear order identifier in eventData:',
          eventData
        );
      }
    } else if (resourceType === 'SHIP_NOTIFY') {
      // Similar to ORDER_NOTIFY, ensure you get the correct order identifier from 'eventData'
      // The SHIP_NOTIFY payload might directly contain orderId or shipment details that link to an order.
      // Assuming 'orderId' is present in the shipment data.
      const orderId = eventData.orderId || (eventData.shipments && eventData.shipments[0]?.orderId);

      console.log(
        `Processing SHIP_NOTIFY for order ID: ${orderId || 'N/A (check payload structure)'}`
      );
      if (orderId) {
        // TODO:
        // a. Update order status to 'shipped' in your database
        //    // await updateOrderStatusInDb(orderId, 'shipped'); // Pass the internal DB order ID
        //
        // b. Update related PrintOrderTasks (e.g., mark as completed or cancelled)
        //    // await prisma.printOrderTask.updateMany({
        //    //   where: { Order: { shipstation_order_id: orderId }, status: { in: ['pending', 'in_progress'] } },
        //    //   data: { status: 'completed' }, // Or 'cancelled' if the order was cancelled then "shipped" (unlikely)
        //    // });
        console.log('TODO: Implement SHIP_NOTIFY processing logic for orderId:', orderId);
      } else {
        console.warn(
          'SHIP_NOTIFY received without a clear order identifier in eventData:',
          eventData
        );
      }
    } else {
      console.log(`Received unhandled webhook type: ${resourceType}`);
    }

    // 4. Respond quickly with a 2xx status
    return NextResponse.json({ received: true, processed_type: resourceType }, { status: 200 });
  } catch (error) {
    const _errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error processing ShipStation webhook:', error);
    // Avoid sending detailed internal error messages back in the response for security.
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}
