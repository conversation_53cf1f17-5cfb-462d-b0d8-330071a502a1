// File: /home/<USER>/Y3DHub_staging/src/app/api/print-tasks/[taskId]/verify/route.ts
import { Prisma } from '@prisma/client';
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { prisma } from '@/lib/prisma';

type VerificationUpdateInput = Prisma.PrintOrderTaskUpdateInput;

export async function PATCH(request: Request, { params }: { params: { taskId: string } }) {
  const session = await getServerSession();
  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated or user email missing' }, { status: 401 });
  }

  const currentUserEmail = session.user.email;

  const user = await prisma.user.findUnique({
    where: { email: currentUserEmail },
    select: { id: true },
  });

  if (!user?.id) {
    console.error('Failed to find user with email:', currentUserEmail);
    return NextResponse.json({ error: 'User not found' }, { status: 404 });
  }

  const currentUserIdInt = user.id;

  const { taskId } = params;

  if (!taskId) {
    return NextResponse.json({ error: 'Task ID is required' }, { status: 400 });
  }

  try {
    const task = await prisma.printOrderTask.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      return NextResponse.json({ error: 'Print task not found' }, { status: 404 });
    }

    const body = await request.json();

    // Validate body if necessary - using Zod is a good practice
    // For now, assuming body might contain: isVerified, needs_review, review_reason
    const {
      isVerified,
      needs_review,
      review_reason,
    }: {
      isVerified?: boolean;
      needs_review?: boolean;
      review_reason?: string | null;
    } = body;

    const dataToUpdate: VerificationUpdateInput = {};

    // Determine the intended state based on inputs
    // Explicitly setting isVerified to true takes precedence
    if (typeof isVerified === 'boolean' && isVerified === true) {
      dataToUpdate.isVerified = true;
      dataToUpdate.verifiedAt = new Date();
      dataToUpdate.verifiedByUser = {
        connect: { id: currentUserIdInt },
      };
      dataToUpdate.needs_review = false; // Verifying clears needs_review
      dataToUpdate.review_reason = null;
    }
    // Explicitly setting needs_review to true
    else if (typeof needs_review === 'boolean' && needs_review === true) {
      dataToUpdate.needs_review = true;
      dataToUpdate.review_reason = review_reason !== undefined ? review_reason : task.review_reason;
      // Don't change verification status when just marking for review
    }
    // Explicitly setting isVerified to false (and not needs_review true) - e.g. resetting to pending
    else if (typeof isVerified === 'boolean' && isVerified === false) {
      dataToUpdate.isVerified = false;
      dataToUpdate.verifiedAt = null;
      dataToUpdate.verifiedByUser = {
        disconnect: true,
      };
      // If needs_review is also explicitly set to false, or not provided, clear it
      if (needs_review === false || needs_review === undefined) {
        dataToUpdate.needs_review = false;
        dataToUpdate.review_reason = null;
      } else {
        // This case (isVerified:false, needs_review:true) is covered above
        dataToUpdate.needs_review = true;
        dataToUpdate.review_reason =
          review_reason !== undefined ? review_reason : task.review_reason;
      }
    }
    // Only review_reason is sent (implies needs_review should be true if reason is not null)
    else if (
      review_reason !== undefined &&
      needs_review === undefined &&
      isVerified === undefined
    ) {
      if (review_reason !== null && review_reason.trim() !== '') {
        dataToUpdate.needs_review = true;
        dataToUpdate.review_reason = review_reason;
      } else {
        // Empty or null review_reason sent, potentially to clear it if task is already needs_review
        if (task.needs_review) {
          dataToUpdate.review_reason = null;
          // Consider if needs_review should also become false here, or if that's a separate action
        }
      }
    }
    // If body is empty or doesn't match any explicit state changes, what should happen?
    // For now, if dataToUpdate is empty, we might not update, or throw error.
    // Let's assume client sends at least one relevant field.
    if (Object.keys(dataToUpdate).length === 0) {
      return NextResponse.json(
        { error: 'No valid verification update parameters provided' },
        { status: 400 }
      );
    }

    console.log(
      `[TASK VERIFY DEBUG] Task ID: ${taskId}, Current User ID (Int): ${currentUserIdInt}, Data to Update:`,
      JSON.stringify(dataToUpdate, null, 2)
    );
    const updatedTask = await prisma.printOrderTask.update({
      where: { id: taskId },
      data: dataToUpdate,
      include: {
        verifiedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error(`Error verifying/unverifying task ${taskId}:`, error);
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
        // Record to update not found
        return NextResponse.json({ error: 'Print task not found during update' }, { status: 404 });
      }
      // Foreign key constraint failed (e.g. verifiedByUserId does not exist in User table)
      if (
        error.code === 'P2003' &&
        error.meta &&
        typeof error.meta.field_name === 'string' &&
        error.meta.field_name.includes('verifiedByUserId')
      ) {
        console.error('Foreign key constraint failed for verifiedByUserId:', currentUserIdInt);
        return NextResponse.json(
          { error: 'Invalid user ID for verification reference.' },
          { status: 400 }
        );
      }
    }
    return NextResponse.json({ error: 'Failed to update verification status' }, { status: 500 });
  }
}
