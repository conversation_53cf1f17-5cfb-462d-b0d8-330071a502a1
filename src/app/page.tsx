export const runtime = 'nodejs';

// Icons can be re-added when dashboard cards are implemented
import DashboardContent from '@/components/dashboard/DashboardContent';
// eslint-disable-next-line import/no-named-as-default
import prisma from '@/lib/prisma';

// --- Data Fetching ---

// Combined function to fetch all dashboard data
async function getDashboardData() {
  const now = new Date();
  const startOfToday = new Date(now);
  startOfToday.setHours(0, 0, 0, 0);
  const startOfTomorrow = new Date(startOfToday);
  startOfTomorrow.setDate(startOfTomorrow.getDate() + 1);
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfYesterday.getDate() - 1);
  const startOfWeek = new Date(startOfToday);
  const day = startOfWeek.getDay();
  const diff = day === 0 ? 6 : day - 1; // assume Monday as start of week
  startOfWeek.setDate(startOfWeek.getDate() - diff);
  const startOfLastWeek = new Date(startOfWeek);
  startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);

  // Define same window yesterday for percentage comparisons
  const windowDuration = now.getTime() - startOfToday.getTime();
  const prevWindowEnd = new Date(startOfYesterday.getTime() + windowDuration);

  const [
    ordersTodayCount,
    ordersYesterdayCount,
    revenueTodayAgg,
    revenueYesterdayAgg,
    itemsSoldTodayAgg,
    itemsSoldYesterdayAgg,
    revenueThisWeekAgg,
    revenueLastWeekAgg,
    tasksPendingCount,
    tasksNeedReviewCount,
    recentOrders,
  ] = await prisma.$transaction([
    prisma.order.count({ where: { order_date: { gte: startOfToday, lt: now } } }),
    prisma.order.count({ where: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } } }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfToday, lt: now } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } },
    }),
    prisma.orderItem.aggregate({
      _sum: { quantity: true },
      where: { Order: { order_date: { gte: startOfToday, lt: now } } },
    }),
    prisma.orderItem.aggregate({
      _sum: { quantity: true },
      where: { Order: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfWeek, lt: startOfTomorrow } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfLastWeek, lt: startOfWeek } },
    }),
    prisma.printOrderTask.count({ where: { status: 'pending' } }),
    prisma.printOrderTask.count({ where: { needs_review: true } }),
    prisma.order.findMany({
      orderBy: { order_date: 'desc' },
      take: 10,
      select: {
        id: true,
        shipstation_order_number: true,
        customer_name: true,
        marketplace: true,
        total_price: true,
        order_status: true,
      },
    }),
  ]);

  // Convert Prisma Decimal values to numbers
  const revenueToday = Number((revenueTodayAgg._sum?.total_price ?? 0).toString());
  const revenueYesterday = Number((revenueYesterdayAgg._sum?.total_price ?? 0).toString());
  const totalItemsSoldToday = Number((itemsSoldTodayAgg._sum?.quantity ?? 0).toString());
  const itemsSoldYesterday = Number((itemsSoldYesterdayAgg._sum?.quantity ?? 0).toString());
  const revenueThisWeek = Number((revenueThisWeekAgg._sum?.total_price ?? 0).toString());
  const revenueLastWeek = Number((revenueLastWeekAgg._sum?.total_price ?? 0).toString());

  function formatChange(current: number, previous: number, label: string) {
    if (previous > 0) {
      const change = ((current - previous) / previous) * 100;
      const sign = change > 0 ? '+' : ''; // prepend + for positive change
      return `${sign}${change.toFixed(1)}% vs ${label}`;
    }
    return 'N/A';
  }

  const ordersTodayPrev = formatChange(ordersTodayCount, ordersYesterdayCount, 'yesterday');
  const revenueTodayPrev = formatChange(revenueToday, revenueYesterday, 'yesterday');
  const itemsSoldPrev = formatChange(totalItemsSoldToday, itemsSoldYesterday, 'yesterday');
  const revenueWeekPrev = formatChange(revenueThisWeek, revenueLastWeek, 'last week');
  const tasksPending = tasksPendingCount;
  const tasksNeedReview = tasksNeedReviewCount;

  const recentOrdersProcessed = recentOrders.map(order => ({
    ...order,
    // Ensure total_price is a number. Prisma's Decimal has toNumber().
    // Assuming total_price is non-nullable on the Order model as selected.
    total_price: order.total_price.toNumber(),
  }));

  return {
    recentOrders: recentOrdersProcessed,
    ordersOnHoldCount: 0, // TODO: implement on-hold count if needed
    ordersToday: ordersTodayCount,
    revenueToday,
    totalItemsSoldToday,
    revenueThisWeek,
    ordersTodayPrev,
    revenueTodayPrev,
    itemsSoldPrev,
    revenueWeekPrev,
    tasksPending,
    tasksNeedReview,
  };
}

// --- Page Component ---

export default async function DashboardPage() {
  const data = await getDashboardData();
  return <DashboardContent {...data} />;
}
