'use client';

import React from 'react';

const ApiDocsPage = () => {
  const apiKey = process.env.NEXT_PUBLIC_EXTERNAL_PULL_API_KEY || 'your_default_static_api_key_here_change_me (set NEXT_PUBLIC_EXTERNAL_PULL_API_KEY for client display or use server-side fetch)';

  const endpoints = [
    {
      group: 'Print Tasks',
      endpoints: [
        {
          method: 'GET',
          path: '/api/external/v1/print-tasks?action=list',
          description: 'Lists print tasks. Supports optional filters as query parameters.',
          exampleFilters: [
            'marketplaceOrderNumber=YOUR_ORDER_NUMBER',
            'status=pending',
            'productId=123',
          ],
        },
        {
          method: 'GET',
          path: '/api/external/v1/print-tasks?action=create',
          description: 'Creates a new print task. All task fields are passed as query parameters.',
          exampleParams: [
            'orderId=1',
            'orderItemId=1',
            'productId=1',
            'quantity=1',
            'status=pending',
            'custom_text=Example Text',
            'shorthandProductName=MyProduct',
            'taskIndex=0',
            '// ... and other PrintOrderTask fields'
          ],
        },
        {
          method: 'GET',
          path: '/api/external/v1/print-tasks/[taskId]?action=details',
          description: 'Retrieves details for a specific print task. Replace [taskId] with the actual task ID.',
          examplePath: '/api/external/v1/print-tasks/your_cuid_task_id?action=details',
        },
        {
          method: 'GET',
          path: '/api/external/v1/print-tasks/[taskId]?action=update',
          description: 'Updates an existing print task. Replace [taskId] with the actual task ID. Fields to update are passed as query parameters.',
          examplePath: '/api/external/v1/print-tasks/your_cuid_task_id?action=update&status=completed&custom_text=Updated Text',
        },
      ],
    },
    {
      group: 'Orders',
      endpoints: [
        {
          method: 'GET',
          path: '/api/external/v1/orders?action=list',
          description: 'Lists orders. Supports optional filters as query parameters.',
          exampleFilters: [
            'marketplaceOrderNumber=YOUR_ORDER_NUMBER',
            'order_status=awaiting_shipment',
            'customerId=456',
          ],
        },
        {
          method: 'GET',
          path: '/api/external/v1/orders?action=create',
          description: 'Creates a new order. All order fields are passed as query parameters.',
          exampleParams: [
            'total_price=19.99',
            'shipstation_order_number=SS123',
            'customer_name=John Doe',
            '// ... and other Order fields'
          ],
        },
        {
          method: 'GET',
          path: '/api/external/v1/orders/[orderId]?action=details',
          description: 'Retrieves details for a specific order. Replace [orderId] with the actual order ID (integer).',
          examplePath: '/api/external/v1/orders/123?action=details',
        },
        {
          method: 'GET',
          path: '/api/external/v1/orders/[orderId]?action=update',
          description: 'Updates an existing order. Replace [orderId] with the actual order ID (integer). Fields to update are passed as query parameters.',
          examplePath: '/api/external/v1/orders/123?action=update&order_status=shipped&internal_notes=Updated notes for order',
        },
      ],
    },
  ];

  // Basic styling - you can enhance this with Tailwind or NextUI components
  const styles = {
    container: { fontFamily: 'sans-serif', padding: '20px', maxWidth: '1000px', margin: '0 auto' },
    header: { borderBottom: '2px solid #eee', paddingBottom: '10px' },
    group: { marginTop: '30px' },
    endpoint: { 
      border: '1px solid #ddd', 
      padding: '15px', 
      marginBottom: '15px', 
      borderRadius: '5px', 
      backgroundColor: '#f9f9f9' 
    },
    path: { 
      fontFamily: 'monospace', 
      backgroundColor: '#eef', 
      padding: '2px 6px', 
      borderRadius: '3px', 
      color: '#337ab7', 
      fontWeight: 'bold'
    },
    description: { marginTop: '5px', color: '#555' },
    paramsList: { listStyleType: 'none', paddingLeft: '0', marginTop: '10px' },
    paramItem: { fontFamily: 'monospace', fontSize: '0.9em', backgroundColor: '#f0f0f0', padding: '3px', marginBottom: '3px', borderRadius: '3px' },
    apiKeyInfo: { marginTop: '20px', padding: '10px', backgroundColor: '#fffbe6', border: '1px solid #ffe58f', borderRadius: '4px' },
  };

  return (
    <div style={styles.container}>
      <h1 style={styles.header}>External API Documentation (All GET Requests)</h1>
      
      <div style={styles.apiKeyInfo}>
        <h3>Authentication</h3>
        <p>All endpoints require an API key passed in the Authorization header:</p>
        <p><code style={styles.path}>Authorization: ApiKey YOUR_API_KEY</code></p>
        <p>
          Your current API key (from environment for client display, or default):
           <strong style={styles.paramItem}>{apiKey}</strong>
        </p>
        <p>
          If <code>EXTERNAL_PULL_API_KEY</code> is not set in your <code>.env</code> file, a default development key will be used (check server console for warnings).
          <strong>For production, always set <code>EXTERNAL_PULL_API_KEY</code>.</strong>
        </p>
      </div>

      {endpoints.map((group, groupIndex) => (
        <section key={groupIndex} style={styles.group}>
          <h2>{group.group}</h2>
          {group.endpoints.map((endpoint, endpointIndex) => (
            <div key={endpointIndex} style={styles.endpoint}>
              <h4><code style={styles.path}>{endpoint.method} {endpoint.path}</code></h4>
              <p style={styles.description}>{endpoint.description}</p>
              {endpoint.examplePath && (
                <p style={styles.description}>Example: <code style={styles.path}>{endpoint.examplePath}</code></p>
              )}
              {endpoint.exampleFilters && endpoint.exampleFilters.length > 0 && (
                <>
                  <p style={styles.description}>Example Filters (append to URL):</p>
                  <ul style={styles.paramsList}>
                    {endpoint.exampleFilters.map((param, i) => <li key={i} style={styles.paramItem}>{param}</li>)}
                  </ul>
                </>
              )}
              {endpoint.exampleParams && endpoint.exampleParams.length > 0 && (
                <>
                  <p style={styles.description}>Example Parameters (append to URL):</p>
                  <ul style={styles.paramsList}>
                    {endpoint.exampleParams.map((param, i) => <li key={i} style={styles.paramItem}>{param}</li>)}
                  </ul>
                </>
              )}
            </div>
          ))}
        </section>
      ))}
    </div>
  );
};

export default ApiDocsPage; 