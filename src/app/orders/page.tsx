/* eslint-disable import/order */
import { Prisma, Tag } from '@prisma/client';
import { DollarSign, Package, TrendingUp } from 'lucide-react';

import { StatsCard } from '@/components/dashboard/stats-card';
import { OrdersPagination } from '@/components/orders-pagination';
import { OrdersSearchForm } from '@/components/orders-search-form';
import { OrdersPageHeader } from '@/components/orders/orders-page-header';
import { OrdersTable } from '@/components/orders/orders-table';
import { CURRENCY_SYMBOL } from '@/lib/constants';
import { detectMarketplaceOrderNumber } from '@/lib/order-utils';
import { orderSelectedFields } from '@/lib/orders/selected-fields';
// eslint-disable-next-line import/no-named-as-default
import prisma from '@/lib/prisma';
import { getLogger } from '@/lib/shared/logging';

const logger = getLogger('orders-page');

// Force dynamic rendering to ensure searchParams are handled correctly
export const dynamic = 'force-dynamic';
interface OrdersPageSearchParams {
  page?: string;
  limit?: string;
  search?: string;
  status?: string;
  marketplace?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
  shipByDateStart?: string;
  shipByDateEnd?: string;
  readyToPrint?: string; // Note: 'true'/'false' as string from URL params
  dateFilterType?: 'orderDate' | 'shipByDate';
}

// Define the currency symbol

// Replace mock data with actual database queries
async function getDashboardStats() {
  const now = new Date();
  const startOfToday = new Date(now);
  startOfToday.setHours(0, 0, 0, 0);
  const startOfTomorrow = new Date(startOfToday);
  startOfTomorrow.setDate(startOfTomorrow.getDate() + 1);
  const startOfYesterday = new Date(startOfToday);
  startOfYesterday.setDate(startOfYesterday.getDate() - 1);
  const startOfWeek = new Date(startOfToday);
  const day = startOfWeek.getDay();
  const diff = day === 0 ? 6 : day - 1; // Monday = start of week
  startOfWeek.setDate(startOfWeek.getDate() - diff);
  const startOfLastWeek = new Date(startOfWeek);
  startOfLastWeek.setDate(startOfLastWeek.getDate() - 7);

  // Define same window yesterday for more accurate comparisons (same duration as today so far)
  const windowDuration = now.getTime() - startOfToday.getTime();
  const prevWindowEnd = new Date(startOfYesterday.getTime() + windowDuration);

  // Fetch the required aggregates in a single transaction for efficiency
  const [
    ordersTodayCount,
    ordersYesterdayCount,
    revenueTodayAgg,
    revenueYesterdayAgg,
    itemsSoldTodayAgg,
    itemsSoldYesterdayAgg,
    revenueThisWeekAgg,
    revenueLastWeekAgg,
  ] = await prisma.$transaction([
    prisma.order.count({ where: { order_date: { gte: startOfToday, lt: now } } }),
    prisma.order.count({ where: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } } }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfToday, lt: now } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } },
    }),
    prisma.orderItem.aggregate({
      _sum: { quantity: true },
      where: { Order: { order_date: { gte: startOfToday, lt: now } } },
    }),
    prisma.orderItem.aggregate({
      _sum: { quantity: true },
      where: { Order: { order_date: { gte: startOfYesterday, lt: prevWindowEnd } } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfWeek, lt: startOfTomorrow } },
    }),
    prisma.order.aggregate({
      _sum: { total_price: true },
      where: { order_date: { gte: startOfLastWeek, lt: startOfWeek } },
    }),
  ]);

  // Convert Decimal values to numbers & provide fallbacks
  const revenueToday = Number((revenueTodayAgg._sum.total_price ?? 0).toString());
  const revenueYesterday = Number((revenueYesterdayAgg._sum.total_price ?? 0).toString());
  const totalItemsSoldToday = Number((itemsSoldTodayAgg._sum?.quantity ?? 0).toString());
  const itemsSoldYesterday = Number((itemsSoldYesterdayAgg._sum?.quantity ?? 0).toString());
  const revenueThisWeek = Number((revenueThisWeekAgg._sum.total_price ?? 0).toString());
  const revenueLastWeek = Number((revenueLastWeekAgg._sum.total_price ?? 0).toString());

  // Helper to format percentage changes (matches dashboard logic)
  const formatChange = (current: number, previous: number, label: string) => {
    if (previous > 0) {
      const change = ((current - previous) / previous) * 100;
      const sign = change > 0 ? '+' : '';
      return `${sign}${change.toFixed(1)}% vs ${label}`;
    }
    return 'N/A';
  };

  return {
    ordersToday: ordersTodayCount,
    revenueToday,
    totalItems: totalItemsSoldToday,
    revenueThisWeek,
    ordersTodayPrev: formatChange(ordersTodayCount, ordersYesterdayCount, 'yesterday'),
    revenueTodayPrev: formatChange(revenueToday, revenueYesterday, 'yesterday'),
    totalItemsPrev: formatChange(totalItemsSoldToday, itemsSoldYesterday, 'yesterday'),
    revenueWeekPrev: formatChange(revenueThisWeek, revenueLastWeek, 'last week'),
  };
}

// Import shared select fields for orders

// Type alias for a single order with the selected fields
type SelectedOrder = Prisma.OrderGetPayload<{ select: typeof orderSelectedFields }>;

// Updated getOrders to accept and apply filters
async function getOrders(
  page = 1,
  limit = 20,
  searchQuery?: string,
  statusFilter?: string,
  marketplaceFilter?: string,
  orderDateStart?: string,
  orderDateEnd?: string,
  shipByDateStart?: string,
  shipByDateEnd?: string,
  onlyReadyToPrint?: boolean
): Promise<{
  orders: SelectedOrder[];
  total: number;
}> {
  logger.debug('[getOrders] Received params', {
    page,
    limit,
    searchQuery,
    statusFilter,
    marketplaceFilter,
    orderDateStart,
    orderDateEnd,
    shipByDateStart,
    shipByDateEnd,
    onlyReadyToPrint,
  });

  const whereClauses: Prisma.OrderWhereInput = { AND: [] };

  // Status Filter
  if (statusFilter && statusFilter !== 'all') {
    (whereClauses.AND as Prisma.OrderWhereInput[]).push({ order_status: statusFilter });
  }

  // Marketplace Filter
  if (marketplaceFilter && marketplaceFilter !== 'all') {
    (whereClauses.AND as Prisma.OrderWhereInput[]).push({ marketplace: marketplaceFilter });
  }

  // Order Date Filter
  if (orderDateStart) {
    const startDate = new Date(orderDateStart);
    if (!isNaN(startDate.getTime())) {
      (whereClauses.AND as Prisma.OrderWhereInput[]).push({
        order_date: { gte: startDate },
      });
    }
  }
  if (orderDateEnd) {
    const endDate = new Date(orderDateEnd);
    if (!isNaN(endDate.getTime())) {
      endDate.setHours(23, 59, 59, 999);
      (whereClauses.AND as Prisma.OrderWhereInput[]).push({
        order_date: { lte: endDate },
      });
    }
  }

  // Ship By Date Filter
  if (shipByDateStart) {
    const startDate = new Date(shipByDateStart);
    if (!isNaN(startDate.getTime())) {
      (whereClauses.AND as Prisma.OrderWhereInput[]).push({
        ship_by_date: { gte: startDate },
      });
    } else {
      logger.warn('[getOrders] Invalid shipByDateStart', { shipByDateStart });
    }
  }
  if (shipByDateEnd) {
    const endDate = new Date(shipByDateEnd);
    if (!isNaN(endDate.getTime())) {
      endDate.setHours(23, 59, 59, 999);
      (whereClauses.AND as Prisma.OrderWhereInput[]).push({
        ship_by_date: { lte: endDate },
      });
    } else {
      logger.warn('[getOrders] Invalid shipByDateEnd', { shipByDateEnd });
    }
  }

  // Search Query Filter
  const trimmedQuery = searchQuery?.trim();
  if (trimmedQuery) {
    const orConditions: Prisma.OrderWhereInput[] = [
      { customer_name: { contains: trimmedQuery } },
      { shipstation_order_number: { contains: trimmedQuery } },
    ];

    const parsedId = parseInt(trimmedQuery, 10);
    if (!isNaN(parsedId)) {
      orConditions.push({ id: parsedId });

      if (onlyReadyToPrint) {
        orConditions.push({
          OrderItem: {
            some: {
              id: parsedId,
              PrintOrderTask: { some: { status: 'pending', stl_render_state: 'completed' } },
            },
          },
        });
      } else {
        orConditions.push({ OrderItem: { some: { id: parsedId } } });
      }
    }

    (whereClauses.AND as Prisma.OrderWhereInput[]).push({ OR: orConditions });
  }

  // Marketplace-specific Order Number Search
  if (trimmedQuery) {
    const detectedMarketplaceInfo = detectMarketplaceOrderNumber(trimmedQuery);
    if (detectedMarketplaceInfo.isMarketplaceNumber) {
      const marketplaceSpecificSearchConditions: Prisma.OrderWhereInput[] = [];

      marketplaceSpecificSearchConditions.push({
        PrintOrderTask: {
          some: {
            marketplace_order_number: trimmedQuery,
          },
        },
      });

      if (marketplaceSpecificSearchConditions.length > 0) {
        (whereClauses.AND as Prisma.OrderWhereInput[]).push({
          OR: marketplaceSpecificSearchConditions,
        });
      }
    }
  }

  // Ready to Print Filter (only if no specific OrderItem ID was searched via parsedId)
  if (onlyReadyToPrint) {
    const isNumericSearch = trimmedQuery && !isNaN(parseInt(trimmedQuery, 10));
    if (!isNumericSearch) {
      (whereClauses.AND as Prisma.OrderWhereInput[]).push({
        OrderItem: {
          some: {
            PrintOrderTask: { some: { status: 'pending', stl_render_state: 'completed' } },
          },
        },
      });
    }
  }

  logger.debug('[getOrders] Constructed whereClauses', {
    whereClauses,
  });

  // If AND array is empty, remove it to avoid Prisma errors with empty AND
  if ((whereClauses.AND as Prisma.OrderWhereInput[]).length === 0) {
    delete whereClauses.AND;
  }

  const skip = Math.max(0, (page - 1) * limit);

  const [orders, total] = await prisma.$transaction([
    prisma.order.findMany({
      where: whereClauses,
      skip: skip,
      take: limit,
      orderBy: {
        created_at: 'desc',
      },
      select: orderSelectedFields,
    }),
    prisma.order.count({ where: whereClauses }),
  ]);
  return { orders, total };
}

// Fetch distinct filter options
async function getFilterOptions(): Promise<{
  statuses: string[];
  marketplaces: string[];
  tags: Tag[];
}> {
  const [statusesResult, marketplacesResult, tagsResult]: [
    { order_status: string | null }[],
    { marketplace: string | null }[],
    Tag[],
  ] = await Promise.all([
    prisma.order.findMany({
      select: { order_status: true },
      distinct: ['order_status'],
    }),
    prisma.order.findMany({
      select: { marketplace: true },
      distinct: ['marketplace'],
    }),
    prisma.tag.findMany({ orderBy: { name: 'asc' } }),
  ]);

  const statuses = statusesResult.map(s => s.order_status).filter((s): s is string => !!s);
  const marketplaces = marketplacesResult.map(m => m.marketplace).filter((m): m is string => !!m);
  const tags = tagsResult;

  return { statuses, marketplaces, tags };
}

export default async function OrdersPage({
  searchParams,
}: {
  searchParams?: OrdersPageSearchParams;
}) {
  const stats = await getDashboardStats();

  const parsedPage = parseInt(searchParams?.page ?? '1', 10);
  const validatedPage = Number.isNaN(parsedPage) || parsedPage < 1 ? 1 : parsedPage;
  const parsedLimit = parseInt(searchParams?.limit ?? '20', 10);
  const validatedLimit = Number.isNaN(parsedLimit) ? 20 : Math.min(100, Math.max(5, parsedLimit));
  const onlyReadyToPrint = searchParams?.readyToPrint === 'true';

  let orderDateStart, orderDateEnd, shipByDateStart, shipByDateEnd;
  if (searchParams?.dateFilterType === 'shipByDate') {
    shipByDateStart = searchParams.shipByDateStart;
    shipByDateEnd = searchParams.shipByDateEnd;
  } else {
    orderDateStart = searchParams?.orderDateStart;
    orderDateEnd = searchParams?.orderDateEnd;
  }

  const { orders: fetchedOrders, total } = await getOrders(
    validatedPage,
    validatedLimit,
    searchParams?.search,
    searchParams?.status,
    searchParams?.marketplace,
    orderDateStart,
    orderDateEnd,
    shipByDateStart,
    shipByDateEnd,
    onlyReadyToPrint
  );
  logger.debug('OrdersPage parameters', {
    shipByDateStart,
    shipByDateEnd,
    onlyReadyToPrint,
    fetchedCount: fetchedOrders.length,
    total,
  });

  // Transform orders to make them serializable for client components
  const serializableOrders = fetchedOrders.map(order => ({
    ...order,
    // Convert Decimal fields to numbers or strings
    total_price:
      order.total_price !== null && order.total_price !== undefined
        ? Number(order.total_price)
        : null,
    // Ensure other Date fields are also in a string format if they are not already
    // Prisma typically returns Date objects as strings in JSON, but good to be mindful
    order_date: order.order_date ? new Date(order.order_date).toISOString() : null,
    shipped_date: order.shipped_date ? new Date(order.shipped_date).toISOString() : null,
    ship_by_date: order.ship_by_date ? new Date(order.ship_by_date).toISOString() : null,
    lastPackingSlipAt: order.lastPackingSlipAt
      ? new Date(order.lastPackingSlipAt).toISOString()
      : null,
  }));

  const { statuses, marketplaces, tags: allTags } = await getFilterOptions();

  const totalPages = Math.ceil(total / validatedLimit);

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <OrdersPageHeader />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Revenue (Today)"
          value={`${CURRENCY_SYMBOL}${stats.revenueToday.toFixed(2)}`}
          description={stats.revenueTodayPrev}
          icon={DollarSign}
          color="blue" // Added
          className="p-3" // Added
        />
        <StatsCard
          title="Orders (Today)"
          value={`${stats.ordersToday}`}
          description={stats.ordersTodayPrev}
          icon={Package}
          color="green" // Added
          className="p-3" // Added
        />
        <StatsCard
          title="Items Sold (Today)"
          value={`${stats.totalItems}`}
          description={stats.totalItemsPrev}
          icon={Package}
          color="purple" // Added
          className="p-3" // Added
        />
        <StatsCard
          title="Revenue (This Week)"
          value={`${CURRENCY_SYMBOL}${stats.revenueThisWeek.toFixed(2)}`}
          description={stats.revenueWeekPrev}
          icon={TrendingUp}
          color="orange" // Added
          className="p-3" // Added
        />
      </div>
      {/* Orders Table Section */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <OrdersSearchForm
          statuses={statuses}
          marketplaces={marketplaces}
          currentSearch={searchParams?.search as string | undefined}
          currentStatus={searchParams?.status as string | undefined}
          currentMarketplace={searchParams?.marketplace as string | undefined}
          currentOrderDateStart={searchParams?.orderDateStart as string | undefined}
          currentOrderDateEnd={searchParams?.orderDateEnd as string | undefined}
          currentShipByDateStart={searchParams?.shipByDateStart as string | undefined}
          currentShipByDateEnd={searchParams?.shipByDateEnd as string | undefined}
          currentReadyToPrint={searchParams?.readyToPrint === 'true'}
          dateFilterType={
            searchParams?.dateFilterType === 'shipByDate' ? 'shipByDate' : 'orderDate'
          }
        />
        {/* Render the new OrdersTable client component */}
        <OrdersTable orders={serializableOrders} allTags={allTags} currentLimit={validatedLimit} />
        {/* Pagination Controls (Existing) */}
        <div className="flex justify-between items-center mt-4">
          <OrdersPagination
            currentPage={validatedPage}
            totalPages={totalPages}
            limit={validatedLimit}
          />
        </div>
        {serializableOrders.length === 0 && (
          <p className="text-center mt-4">No orders found for the current filters.</p>
        )}{' '}
        {/* Update empty message */}
      </div>{' '}
      {/* Close card div */}
    </div>
  );
}

// Optional: Add revalidation if data changes frequently
// export const revalidate = 60; // Revalidate every 60 seconds
