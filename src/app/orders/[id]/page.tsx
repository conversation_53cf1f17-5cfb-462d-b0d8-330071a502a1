import { ClockIcon } from '@radix-ui/react-icons';
import { MapPin, StickyNote, User } from 'lucide-react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import { MoreDetailsCard } from '@/components/orders/more-details-card';
import { OrderItemsCard } from '@/components/orders/order-items-card';
import { ShippingInfoCard } from '@/components/orders/shipping-info-card';
import StatusBadge from '@/components/orders/status-badge';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CURRENCY_SYMBOL } from '@/lib/constants';
import { formatCountryCode } from '@/lib/formatting';
import { getMarketplaceAlias } from '@/lib/mapping-utils';
// eslint-disable-next-line import/no-named-as-default
import prisma from '@/lib/prisma';
import { formatDateForTable } from '@/lib/shared/date-utils';
import type { SerializableOrderDetailsData } from '@/types/order-details';

// TODO: Fix proper typings in a future PR
async function getOrderDetails(orderId: string): Promise<{
  order: SerializableOrderDetailsData | null;
  allTags: string[];
}> {
  // Fetch order with all related data
  // Convert orderId string to number for Prisma
  const order = await prisma.order.findUnique({
    where: { id: parseInt(orderId, 10) },
    include: {
      Customer: true,
      OrderItem: {
        include: {
          PrintOrderTask: true, // Removing nested include for now to avoid type errors
        },
      },
    },
  });

  if (!order) {
    return { order: null, allTags: [] };
  }

  // Get all available tags for the order tags dropdown
  const tags = await prisma.tag.findMany({
    orderBy: { name: 'asc' },
  });

  const allTags = tags.map(tag => tag.name);

  // Process and serialize the order data for the client component
  // Using type casting to bypass TypeScript checks due to schema/code mismatch
  // TODO: Fix proper typings in a follow-up PR to match the current schema
  const orderAny = order as Record<string, unknown>;

  const serializedOrder = orderAny as unknown as SerializableOrderDetailsData;

  // Convert Date fields to ISO strings
  if (serializedOrder.order_date && orderAny.order_date instanceof Date) {
    serializedOrder.order_date = orderAny.order_date.toISOString();
  }

  if (serializedOrder.created_at && orderAny.created_at instanceof Date) {
    serializedOrder.created_at = orderAny.created_at.toISOString();
  }

  if (serializedOrder.updated_at && orderAny.updated_at instanceof Date) {
    serializedOrder.updated_at = orderAny.updated_at.toISOString();
  }

  if (serializedOrder.ship_by_date && orderAny.ship_by_date instanceof Date) {
    serializedOrder.ship_by_date = orderAny.ship_by_date.toISOString();
  }

  if (serializedOrder.shipped_date && orderAny.shipped_date instanceof Date) {
    serializedOrder.shipped_date = orderAny.shipped_date.toISOString();
  }

  if (serializedOrder.payment_date && orderAny.payment_date instanceof Date) {
    serializedOrder.payment_date = orderAny.payment_date.toISOString();
  }

  if (serializedOrder.void_date && orderAny.void_date instanceof Date) {
    serializedOrder.void_date = orderAny.void_date.toISOString();
  }

  if (serializedOrder.last_sync_date && orderAny.last_sync_date instanceof Date) {
    serializedOrder.last_sync_date = orderAny.last_sync_date.toISOString();
  }

  if (serializedOrder.lastPackingSlipAt && orderAny.lastPackingSlipAt instanceof Date) {
    serializedOrder.lastPackingSlipAt = orderAny.lastPackingSlipAt.toISOString();
  }

  if (serializedOrder.Customer) {
    if (serializedOrder.Customer.created_at) {
      serializedOrder.Customer.created_at = new Date(
        serializedOrder.Customer.created_at
      ).toISOString();
    }

    if (serializedOrder.Customer.updated_at) {
      serializedOrder.Customer.updated_at = new Date(
        serializedOrder.Customer.updated_at
      ).toISOString();
    }
  }

  if (serializedOrder.OrderItem) {
    serializedOrder.OrderItem = serializedOrder.OrderItem.map(item => {
      const itemCopy = { ...item };

      if (itemCopy.created_at) {
        itemCopy.created_at = new Date(itemCopy.created_at).toISOString();
      }

      if (itemCopy.updated_at) {
        itemCopy.updated_at = new Date(itemCopy.updated_at).toISOString();
      }

      if (typeof itemCopy.unit_price !== 'string') {
        itemCopy.unit_price = String(itemCopy.unit_price || '0.00');
      }

      if (itemCopy.PrintOrderTask) {
        itemCopy.PrintOrderTask = (
          Array.isArray(itemCopy.PrintOrderTask) ? itemCopy.PrintOrderTask : []
        ).map(task => {
          const taskCopy = { ...task };

          if (
            taskCopy.created_at &&
            (typeof taskCopy.created_at === 'string' ||
              typeof taskCopy.created_at === 'number' ||
              Object.prototype.toString.call(taskCopy.created_at) === '[object Date]')
          ) {
            taskCopy.created_at = new Date(taskCopy.created_at).toISOString();
          }

          if (
            taskCopy.updated_at &&
            (typeof taskCopy.updated_at === 'string' ||
              typeof taskCopy.updated_at === 'number' ||
              Object.prototype.toString.call(taskCopy.updated_at) === '[object Date]')
          ) {
            taskCopy.updated_at = new Date(taskCopy.updated_at).toISOString();
          }

          if (
            taskCopy.ship_by_date &&
            (typeof taskCopy.ship_by_date === 'string' ||
              typeof taskCopy.ship_by_date === 'number' ||
              Object.prototype.toString.call(taskCopy.ship_by_date) === '[object Date]')
          ) {
            taskCopy.ship_by_date = new Date(taskCopy.ship_by_date).toISOString();
          }

          return taskCopy;
        });
      }

      return itemCopy;
    });
  }

  return { order: serializedOrder, allTags };
}

interface OrderDetailPageProps {
  params: { id: string };
}

// --- Status Color Mappings ---
const orderStatusColors: Record<string, string> = {
  awaiting_shipment: 'bg-blue-500 text-white dark:bg-blue-600 dark:text-blue-100',
  shipped: 'bg-green-600 text-white dark:bg-green-700 dark:text-green-100',
  on_hold: 'bg-yellow-500 text-white dark:bg-yellow-600 dark:text-yellow-100',
  cancelled: 'bg-red-600 text-white dark:bg-red-700 dark:text-red-100',
  default: 'bg-gray-500 text-white dark:bg-gray-600 dark:text-gray-100',
};

const internalStatusColors: Record<string, string> = {
  new: 'bg-sky-500 text-white dark:bg-sky-600 dark:text-sky-100',
  processing: 'bg-purple-500 text-white dark:bg-purple-600 dark:text-purple-100',
};

function _getStatusClass(status: string | null | undefined, type: 'order' | 'internal'): string {
  const map = type === 'order' ? orderStatusColors : internalStatusColors;
  const key = status?.toLowerCase() ?? 'default';
  return map[key] || map.default;
}

export default async function OrderDetailPage({ params }: OrderDetailPageProps) {
  const { id } = params;

  // Fetch order data with all related items
  const { order: orderData, allTags: _allTags } = await getOrderDetails(id);

  if (!orderData) {
    redirect('/orders');
  }

  const order = orderData as SerializableOrderDetailsData;

  return (
    <div className="container max-w-screen-xl mx-auto py-4 space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start gap-4 pb-3">
        <Button variant="outline" size="sm" className="h-8" asChild>
          <Link href="/orders" className="flex items-center">
            <ClockIcon className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>

        <div>
          {(order.status as string | undefined) && (
            <StatusBadge status={order.status as string} className="ml-2" size="lg" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column: Summary, Customer, Shipping Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Summary Card */}
          <Card className="border-l-4 border-amber-500">
            <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-amber-500/30 via-orange-500/30 to-yellow-500/30 dark:from-amber-700/50 dark:via-orange-700/50 dark:to-yellow-700/50 rounded-t-lg px-4 py-3">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                Order #{order.order_number}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Order Date:</p>
                  <p className="font-medium">
                    {order.order_date ? formatDateForTable(new Date(order.order_date)) : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">Ship By:</p>
                  <p className="font-medium">
                    {order.ship_by_date ? formatDateForTable(new Date(order.ship_by_date)) : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">Marketplace:</p>
                  <p className="font-medium">{getMarketplaceAlias(order.marketplace)}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Order Status:</p>
                  <StatusBadge status={order.status} size="sm" />
                </div>
                <div>
                  <p className="text-muted-foreground">Created:</p>
                  <p className="font-medium">{formatDateForTable(new Date(order.created_at))}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Last Updated:</p>
                  <p className="font-medium">
                    {order.updated_at ? formatDateForTable(new Date(order.updated_at)) : 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row justify-between pb-4 px-4 pt-1 bg-muted/20">
              <div className="w-full space-y-1">
                <div className="flex items-center gap-1 text-sm">
                  <span className="text-muted-foreground">Tags:</span>
                  {order.tag_ids && Array.isArray(order.tag_ids) && order.tag_ids.length > 0 ? (
                    order.tag_ids.map((tagId, i: number) => (
                      <Badge key={i} variant="secondary">
                        {String(tagId)}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground ml-1">-</span>
                  )}
                </div>
                <div className="font-bold text-lg pt-1">
                  Total: {CURRENCY_SYMBOL}
                  {order.total_price ?? '0.00'}
                </div>
              </div>
            </CardFooter>
          </Card>

          {/* Customer Information Card */}
          {order.Customer && (
            <Card className="border-l-4 border-purple-500">
              <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-purple-500/30 via-fuchsia-500/30 to-pink-500/30 dark:from-purple-700/50 dark:via-fuchsia-700/50 dark:to-pink-700/50 rounded-t-lg px-4 py-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm pt-4">
                <p className="font-medium">
                  {order.Customer?.name || order.customer_name || 'N/A'}
                </p>
                <div className="text-muted-foreground">
                  <p>Customer ID: {order.Customer?.shipstation_customer_id || 'N/A'}</p>
                  <p>Email: {order.Customer?.email || 'N/A'}</p>
                  <p>Phone: {order.Customer?.phone || 'N/A'}</p>
                </div>
                <div className="flex items-start pt-2">
                  <MapPin className="mr-2 mt-1 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                  <div className="text-muted-foreground">
                    <p className="font-medium text-foreground">Shipping Address</p>
                    {order.Customer?.street1 || order.Customer?.city ? (
                      <address className="not-italic bg-muted/40 p-2 rounded text-foreground/90 text-sm">
                        {order.Customer?.company && (
                          <span>
                            {order.Customer.company}
                            <br />
                          </span>
                        )}
                        {order.Customer?.street1 && (
                          <span>
                            {order.Customer.street1}
                            <br />
                          </span>
                        )}
                        {order.Customer?.street2 && (
                          <span>
                            {order.Customer.street2}
                            <br />
                          </span>
                        )}
                        {order.Customer?.city}, {order.Customer?.state}{' '}
                        {order.Customer?.postal_code}
                        <br />
                        {formatCountryCode(order.Customer?.country_code)}
                      </address>
                    ) : (
                      <p>No address provided</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <ShippingInfoCard order={order} />
        </div>

        {/* Right Column: Order Items, Customer Notes, More Details */}
        <div className="lg:col-span-1 space-y-6">
          <OrderItemsCard items={order.OrderItem} />

          {/* Customer Notes Section */}
          {order.customer_notes && (
            <Card className="border-l-4 border-rose-500">
              <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-rose-500/30 via-red-500/30 to-pink-500/30 dark:from-rose-700/50 dark:via-red-700/50 dark:to-pink-700/50 rounded-t-lg px-4 py-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <StickyNote className="h-5 w-5 text-muted-foreground" />
                  Customer Notes
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {order.customer_notes}
                </p>
              </CardContent>
            </Card>
          )}

          {/* More Details Card */}
          <MoreDetailsCard
            className="border-l-4 border-gray-500"
            orderData={{
              payment_date: order.payment_date,
              order_key: order.order_key,
              shipstation_store_id: order.shipstation_store_id
                ? Number(order.shipstation_store_id)
                : null,
              payment_method: order.payment_method,
              amount_paid: order.amount_paid,
              shipping_price: order.shipping_price,
              tax_amount: order.tax_amount,
              discount_amount: order.discount_amount,
              shipping_amount_paid: order.shipping_amount_paid,
              shipping_tax: order.shipping_tax,
              gift: order.gift,
              gift_message: order.gift_message,
              internal_notes: order.internal_notes,
              last_sync_date: order.last_sync_date,
              order_weight_value: order.order_weight_value,
              order_weight_units: order.order_weight_units,
              dimensions_units: order.dimensions_units,
              dimensions_length: order.dimensions_length,
              dimensions_width: order.dimensions_width,
              dimensions_height: order.dimensions_height,
              insurance_provider: order.insurance_provider,
              insurance_insure_shipment: order.insurance_insure_shipment,
              insurance_insured_value: order.insurance_insured_value,
              gift_email: order.gift_email,
              notes: order.notes,
            }}
          />
        </div>
      </div>
    </div>
  );
}
