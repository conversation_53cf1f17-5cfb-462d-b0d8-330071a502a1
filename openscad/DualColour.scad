//// 'Customizable Multiline Tag or Keychain' by wstein 
//// is licensed under the Creative Commons - Attribution - Share Alike license. 
// Version 4.3 (c) May 2015
// please refer the complete license here: http://creativecommons.org/licenses/by-sa/3.0/legalcode

// Version 1.1: 
// - new slottet lug
// - Text height can be lower than bar height
// - new Font Weight to optimize the font. especially if the font is not bold enough
// Version 2.0:
// - Complete rewrite
// - Outline added
// - Catch Diacritical Sign added
// Version 2.1:
// - bugfix for some fonts
// - new Font Outline Style
// - Extra tab removed
// Version 2.2:
// - new Catch Diacritical Sign Strategy
// - new Text Horizontal Shift
// - some parameters clarified
// Version 2.3:
// - Colors added 
// Version 2.4:
// - Bar frame added, best for plate style  
// Version 2.4a:
// - some bugs fixed
// Version 2.4b:
// - rendering issue if outline thickness smaller than bar thickness fixed
// Version 2.4c:
// - bug fixed see http://www.thingiverse.com/thing:762705
// Version 2.4d:
// - Outline Width = 0 bug fixed see http://www.thingiverse.com/thing:773447
// Version 2.4e:
// - vertical shift bug fixed see http://www.thingiverse.com/thing:790327
// Version 3.0:
// - multi extrusion added
// - ruler for preview in customizer added
// - new border and margin setting
// - optimized for 0.1mm, 0.2mm, 0.3mm layer thickness
// - writing direction added 
// - more than 90 Noto fonts added
// Version 3.0a:
// - Customizer did not work with default settings
// - smaller fixes
// Version 3.5:
// - new Multiline support
// - smaller fixes
// Version 3.5a:
// - bugfix stencil tag
// Version 4.0:
// - Text extrusion bugfix
// - some parameters optimized, like "Font Size" and "Layers"
// - some fonts like "Alex One" optimized (adding extra weight)
// - a down stripped 'main' font list with 250 fonts and a complete list with all font styles (many Thanks to tbuser for giving me the [list](http://www.thingiverse.com/download:1335652) of the installed fonts)
// Version 4.1:
// - pointy style bug fix
// - new default style Surround Text with border
// - some minor changes
// Version 4.2:
// - hole_extra_margin to extend the hole and the bar
//   Thanks to ajwest: "This is awesome! My only suggestion would be to add a parameter to choose the lug distance from the edge (like the Lug Text Distance parameter but for the other side) because I find the hole is a bit too thin on that side and I'd like to make sure it's not going to break off on my keychain."


/*[Text]*/
line1="Name";
line2="";
line3="";
line4="";
line5="";

// Ruler is only shown in Customizer.
ruler_unit=10; //[10:cm, 25.4:inch]
 
/*[Style]*/
character_spacing=1.02; // [0.5:0.05:5.0]
line_spacing=1.12; //[0.5:0.05:5.0]

bar_style="surround_text"; //[bar_only:Simple Bar,surround_text:Surround Text]
//for multiline tag
lug_style="plate"; //[pointy:Pointy (multiline),plate:Plate]

// Direction of the text flow.
writing_direction="ltr"; //[ltr:left-to-right, rtl:right-to-left, ttb:top-to-bottom, btt:bottom-to-top]
function is_vertical()=writing_direction=="btt"||writing_direction=="ttb"?90:0;

/*[Font]*/
//(a preselection of about 250 fonts)
font_name="Comfortaa"; //[SimHei,Lobster,Cherry Bomb One,Autumn in November,Symbola,Comfortaa,Arial Unicode MS,Abril Fatface,Aclonica,Acme,Advent Pro,Akronim,Aladin,Alegreya,Alegreya Sans,Alegreya SC,Alex Brush,Alfa Slab One,Alice,Amaranth,Architects Daughter,Archivo Black,Archivo Narrow,Arimo,Arvo,Asap,Astloch,Asul,Averia Libre,Averia Serif Libre,Bangers,Basic,Belgrano,Berkshire Swash,Bigshot One,Bilbo Swash Caps,Black Ops One,Bonbon,Bowlby One SC,Brawler,Bubblegum Sans,Butterfly Kids,Cabin Condensed,Caesar Dressing,Cagliostro,Calligraffitti,Capriola,Carter One,Changa One,Chango,Chelsea Market,Cherry Cream Soda,Chewy,Chicle,Chivo,Clicker Script,Coming Soon,Concert One,Condiment,Cookie,Courgette,Covered By Your Grace,Crimson Text,Dancing Script,DejaVu Sans,DejaVu Serif,Dhurjati,Doppio One,Dosis,Droid Sans,Eagle Lake,Electrolize,Emilys Candy,Encode Sans,Encode Sans Compressed,Euphoria Script,Exo,Exo 2,Faster One,Federo,Finger Paint,Fjord One,Fontdiner Swanky,Freckle Face,Fruktur,Gabriela,Geo,Germania One,Give You Glory,Gloria Hallelujah,Goudy Bookletter 1911,Graduate,Grand Hotel,Great Vibes,Griffy,Hanalei Fill,Happy Monkey,Henny Penny,Hind,IM Fell English SC,Indie Flower,Irish Grover,Italianno,Jacques Francois Shadow,Jolly Lodger,Josefin Slab,Joti One,Judson,Just Another Hand,Kalam,Kameron,Karma,Kavoon,Knewave,Kranky,Kristi,Laila,Lakki Reddy,Lancelot,Lato,Leckerli One,Ledger,Lekton,Lemon One,Liberation Sans,Libre Caslon Text,Life Savers,Lilita One,Lily Script One,Limelight,Lobster,Lobster Two,Londrina Outline,Londrina Shadow,Londrina Solid,Lora,Love Ya Like A Sister,Loved by the King,Lovers Quarrel,Luckiest Guy,Lusitana,Macondo,Macondo Swash Caps,Mako,Marck Script,Margarine,Marko One,Maven Pro,McLaren,MedievalSharp,Merienda One,Merriweather,Mervale Script,Metal Mania,Metrophobic,Milonga,Miltonian Tattoo,Miss Fajardose,Molle,Montez,Montserrat,Mr De Haviland,Mystery Quest,Neucha,New Rocker,Niconne,Nosifer,Nothing You Could Do,Noto Sans Oriya,Noto Serif,Nova Square,Nunito,Old Standard TT,Oleo Script,Oleo Script Swash Caps,Orbitron,Oregano,Orienta,Original Surfer,Oswald,Over the Rainbow,Overlock,Oxygen,Pacifico,Paprika,Parisienne,Patrick Hand SC,Paytone One,Peralta,Permanent Marker,Piedra,Pirata One,Play,Playball,Poetsen One,Poppins,Press Start 2P,Princess Sofia,PT Mono,Qwigley,Racing Sans One,Raleway,Rancho,Ranga,Ribeye,Roboto,Roboto Condensed,Roboto Slab,Rochester,Rock Salt,Rubik One,Sail,Salsa,Sansita One,Sarina,Satisfy,Schoolbell,Seaweed Script,Sevillana,Shadows Into Light,Shadows Into Light Two,Share,Six Caps,Skranji,Source Sans Pro,Spicy Rice,Stardos Stencil,Stoke,Syncopate,Teko,Terminal Dosis,The Girl Next Door,Tillana,Timmana,Titillium Web,Ubuntu,Ultra,Underdog,UnifrakturCook,UnifrakturMaguntia,Vampiro One,Vidaloka,Viga,Voces,Volkhov,Vollkorn,Voltaire,Waiting for the Sunrise,Wallpoet,Wellfleet,Wendy One,Yellowtail,Yesteryear,Zeyada]

//ignores Font Name setting
font_with_style=""; //[SimHei:style=Regular,ABeeZee (Regular),ABeeZee (Italic),Abel (Regular),Abril Fatface (Regular),Aclonica (Regular),Acme (Regular),Actor (Regular),Adamina (Regular),Advent Pro (Thin),Advent Pro (ExtraLight),Advent Pro (Light),Advent Pro (Regular),Advent Pro (Medium),Advent Pro (SemiBold),Advent Pro (Bold),Aguafina Script (Regular),Akronim (Regular),Aksara Bali Galang (Regular),Aladin (Regular),Aldrich (Regular),Alef (Regular),Alef (Bold),Alegreya Sans (Thin),Alegreya Sans (Light),Alegreya Sans (Regular),Alegreya Sans (Medium),Alegreya Sans (Bold),Alegreya Sans (ExtraBold),Alegreya Sans (Black),Alegreya Sans (Thin Italic),Alegreya Sans (Light Italic),Alegreya Sans (Italic),Alegreya Sans (Medium Italic),Alegreya Sans (Bold Italic),Alegreya Sans (ExtraBold Italic),Alegreya Sans (Black Italic),Alegreya Sans SC (Thin),Alegreya Sans SC (Light),Alegreya Sans SC (Regular),Alegreya Sans SC (Medium),Alegreya Sans SC (Bold),Alegreya Sans SC (ExtraBold),Alegreya Sans SC (Black),Alegreya Sans SC (Thin Italic),Alegreya Sans SC (Light Italic),Alegreya Sans SC (Italic),Alegreya Sans SC (Medium Italic),Alegreya Sans SC (Bold Italic),Alegreya Sans SC (ExtraBold Italic),Alegreya Sans SC (Black Italic),Alegreya SC (Regular),Alegreya SC (Bold),Alegreya SC (Black),Alegreya SC (Italic),Alegreya SC (Bold Italic),Alegreya SC (Black Italic),Alegreya (Regular),Alegreya (Bold),Alegreya (Black),Alegreya (Italic),Alegreya (Bold Italic),Alegreya (Black Italic),Alex Brush (Regular),Alfa Slab One (Regular),Alice (Regular),Alike Angular (Regular),Alike (Regular),Allan (Regular),Allan (Bold),Allerta Stencil (Regular),Allerta (Stencil),Allerta (Regular),Allerta (Medium),Allura (Regular),Almendra Display (Regular),Almendra SC (Regular),Almendra SC (Bold),Almendra SC (Italic),Almendra SC (Bold Italic),Almendra (Regular),Almendra (Bold),Almendra (Italic),Almendra (Bold Italic),Amarante (Regular),Amaranth (Regular),Amaranth (Bold),Amaranth (Italic),Amaranth (Bold Italic),Amatic SC (Regular),Amatic SC (Bold),Amethysta (Regular),Amiri (Regular),Amiri (Bold),Amiri (Slanted),Amiri (Bold Slanted),Anaheim (Regular),Andada SC (Regular),Andada SC (Bold),Andada SC (Italic),Andada SC (Bold Italic),Andada (Regular),Andada (Bold),Andada (Italic),Andada (Bold Italic),Andika (Regular),Angkor (Regular),Annie Use Your Telescope (Regular),Anonymous Pro (Regular),Anonymous Pro (Bold),Anonymous Pro (Italic),Anonymous Pro (Bold Italic),Antic Didone (Regular),Antic Slab (Regular),Antic (Regular),Antonio (Light),Antonio (Regular),Antonio (Bold),Anton (Regular),Arapey (Regular),Arapey (Italic),Arbutus Slab (Regular),Arbutus (Regular),Architects Daughter (Regular),Archivo Black (Regular),Archivo Narrow (Regular),Archivo Narrow (Bold),Archivo Narrow (Italic),Archivo Narrow (Bold Italic),Arimo (Regular),Arimo (Bold),Arimo (Italic),Arimo (Bold Italic),Arizonia (Regular),Armata (Regular),Artifika (Medium),Arvo (Regular),Arvo (Bold),Arvo (Italic),Arvo (Bold Italic),Asap (Regular),Asap (Bold),Asap (Italic),Asap (Bold Italic),Asset (Regular),Astloch (Regular),Astloch (Bold),Asul (Regular),Asul (Bold),Atomic Age (Regular),Aubrey (Regular),Audiowide (Regular),Autour One (Regular),Average Sans (Regular),Average (Regular),Averia Gruesa Libre (Regular),Averia Libre (Light),Averia Libre (Regular),Averia Libre (Bold),Averia Libre (Light Italic),Averia Libre (Italic),Averia Libre (Bold Italic),Averia Sans Libre (Light),Averia Sans Libre (Regular),Averia Sans Libre (Bold),Averia Sans Libre (Light Italic),Averia Sans Libre (Italic),Averia Sans Libre (Bold Italic),Averia Serif Libre (Light),Averia Serif Libre (Regular),Averia Serif Libre (Bold),Averia Serif Libre (Light Italic),Averia Serif Libre (Italic),Averia Serif Libre (Bold Italic),Bad Script (Regular),Bali Galang (Regular),Balthazar (Regular),Bangers (Regular),Basic (Regular),Battambang (Regular),Battambang (Bold),Baumans (Regular),Bayon (Regular),Belgrano (Regular),Belleza (Regular),BenchNine (Light),BenchNine (Regular),BenchNine (Bold),Bentham (Regular),Berkshire Swash (Regular),Bevan (Regular),Bhavuka (Regular),Bigelow Rules (Regular),Bigshot One (Regular),Bilbo (Regular),Bilbo Swash Caps (Regular),Bitter (Regular),Bitter (Bold),Bitter (Italic),Black Ops One (Regular),BM HANNA_TTF (Regular),Bokor (Regular),Bonbon (Regular),Boogaloo (Regular),Bowlby One SC (Regular),Bowlby One (Regular),Brawler (Regular),Bree Serif (Regular),Bruno Ace SC (Regular),Bruno Ace (Regular),Bubblegum Sans (Regular),Bubbler One (Regular),Buda (light),Buenard (Regular),Buenard (Bold),Butcherman Caps (Regular),Butcherman (Regular),Butterfly Kids (Regular),Cabin Condensed (Regular),Cabin Condensed (Medium),Cabin Condensed (SemiBold),Cabin Condensed (Bold),CabinSketch (Bold),Cabin Sketch (Regular),Cabin (Regular),Cabin (Medium),Cabin (SemiBold),Cabin (Bold),Cabin (Italic),Cabin (Medium Italic),Cabin (SemiBold Italic),Cabin (Bold Italic),Caesar Dressing (Regular),Cagliostro (Regular),Calligraffitti (Regular),Cambay (Regular),Cambay (Bold),Cambay (Italic),Cambay (Bold Italic),Cambo (Regular),Candal (Regular),Cantarell (Regular),Cantarell (Bold),Cantarell (Oblique),Cantarell (BoldOblique),Cantata One (Regular),CantoraOne (Regular),Capriola (Regular),Cardo (Regular),Cardo (Bold),Cardo (Italic),Carme (Regular),CarroisGothic Caps (Regular),CarroisGothic (Regular),Carter One (Regular),Caudex (Regular),Caudex (Bold),Caudex (Italic),Caudex (Bold Italic),Cedarville Cursive (Regular),Century Schoolbook L (Roman),Century Schoolbook L (Bold),Century Schoolbook L (Italic),Century Schoolbook L (Bold Italic),Ceviche One (Regular),Changa One (Regular),Changa One (Italic),Changa (Regular),Chango (Regular),Chau Philomene One (Regular),Chau Philomene One (Italic),Chela One (Regular),Chelsea Market (Regular),Chenla (Regular),Cherry Cream Soda (Regular),Cherry Swash (Regular),Cherry Swash (Bold),Chewy (Regular),Chicle (Regular),Chivo (Regular),Chivo (Black),Chivo (Italic),Chivo (Black Italic),Cinzel (Regular),Cinzel (Bold),Cinzel (Black),Cinzel Decorative (Regular),Cinzel Decorative (Bold),Cinzel Decorative (Black),Clara (Regular),Clicker Script (Regular),Coda Caption (Heavy),Coda (Regular),Coda (Heavy),Codystar (Light),Codystar (Regular),Combo (Regular),Comfortaa (Light),Comfortaa (Regular),Comfortaa (Bold),Coming Soon (Regular),Concert One (Regular),Condiment (Regular),Content (Regular),Content (Bold),Contrail One (Regular),Convergence (Regular),Cookie (Regular),Copse (Regular),Corben (Regular),Corben (Bold),Courgette (Regular),Cousine (Regular),Cousine (Bold),Cousine (Italic),Cousine (Bold Italic),Coustard (Regular),Coustard (Bold),Covered By Your Grace (Regular),Crafty Girls (Regular),Creepster Caps (Regular),Creepster (Regular),Crete Round (Regular),Crete Round (Italic),Crimson Text (Roman),Crimson Text (Semibold),Crimson Text (Bold),Crimson Text (Italic),Crimson Text (SemiboldItalic),Crimson Text (BoldItalic),Croissant One (Regular),Crushed (Regular),Cuprum (Regular),Cuprum (Bold),Cuprum (Italic),Cuprum (Bold Italic),Cutive Mono (Regular),Cutive (Regular),Damion (Regular),Dancing Script (Regular),Dancing Script (Bold),Dangrek (Regular),Dawning of a New Day (Regular),Days One (Regular),DejaVu Sans Mono (Book),DejaVu Sans Mono (Bold),DejaVu Sans (Book),DejaVu Sans (Bold),DejaVu Serif (Book),DejaVu Serif (Bold),Dekko (Regular),Delius (Regular),Delius Swash Caps (Regular),Delius Unicase (Regular),Delius Unicase (Bold),Della Respira (Regular),Denk One (Regular),Devonshire (Regular),Dhurjati (Regular),Dhyana (Regular),Dhyana (Bold),Didact Gothic (Regular),Dinah (Regular),DinahVAA (Bold),Dingbats (Regular),Diplomata SC (Regular),Diplomata (Regular),Domine (Regular),Domine (Bold),Donegal One (Regular),Doppio One (Regular),Dorsa (Regular),Dosis (ExtraLight),Dosis (Light),Dosis (Regular),Dosis (Medium),Dosis (SemiBold),Dosis (Bold),Dosis (ExtraBold),Droid Arabic Kufi (Regular),Droid Arabic Kufi (Bold),Droid Arabic Naskh (Regular),Droid Arabic Naskh (Bold),Droid Sans Ethiopic (Regular),Droid Sans Ethiopic (Bold),Droid Sans Japanese (Regular),Droid Sans Mono (Regular),Droid Sans (Regular),Droid Sans (Bold),Droid Sans Tamil (Regular),Droid Sans Tamil (Bold),Droid Sans Thai (Regular),Droid Sans Thai (Bold),Droid Serif (Regular),Droid Serif (Bold),Droid Serif (Italic),Droid Serif (Bold Italic),DroidSerifThai (Bold),Droid Serif Thai (Regular),Dr Sugiyama (Regular),Duru Sans (Regular),Dynalight (Regular),Eagle Lake (Regular),Eater Caps (Regular),Eater (Regular),EB Garamond (Regular),Economica (Regular),Economica (Bold),Economica (Italic),Economica (Bold Italic),Eczar (Regular),Eczar (Medium),Eczar (Semibold),Eczar (Bold),Eczar (Extrabold),Ek Mukta (ExtraLight),Ek Mukta (Light),Ek Mukta (Regular),Ek Mukta (Medium),Ek Mukta (SemiBold),Ek Mukta (Bold),Ek Mukta (ExtraBold),Electrolize (Regular),Elsie (Regular),Elsie (Black),Elsie Swash Caps (Regular),Elsie Swash Caps (Black),Emblema One (Regular),Emilys Candy (Regular),Encode Sans Compressed (Thin),Encode Sans Compressed (ExtraLight),Encode Sans Compressed (Light),Encode Sans Compressed (Regular),Encode Sans Compressed (Medium),Encode Sans Compressed (SemiBold),Encode Sans Compressed (Bold),Encode Sans Compressed (ExtraBold),Encode Sans Compressed (Black),Encode Sans Condensed (Thin),Encode Sans Condensed (ExtraLight),Encode Sans Condensed (Light),Encode Sans Condensed (Regular),Encode Sans Condensed (Medium),Encode Sans Condensed (SemiBold),Encode Sans Condensed (Bold),Encode Sans Condensed (ExtraBold),Encode Sans Condensed (Black),Encode Sans Narrow (Thin),Encode Sans Narrow (ExtraLight),Encode Sans Narrow (Light),Encode Sans Narrow (Regular),Encode Sans Narrow (Medium),Encode Sans Narrow (SemiBold),Encode Sans Narrow (Bold),Encode Sans Narrow (ExtraBold),Encode Sans Narrow (Black),Encode Sans (Thin),Encode Sans (ExtraLight),Encode Sans (Light),Encode Sans (Regular),Encode Sans (Medium),Encode Sans (SemiBold),Encode Sans (Bold),Encode Sans (ExtraBold),Encode Sans (Black),Encode Sans Wide (Thin),Encode Sans Wide (ExtraLight),Encode Sans Wide (Light),Encode Sans Wide (Regular),Encode Sans Wide (Medium),Encode Sans Wide (SemiBold),Encode Sans Wide (Bold),Encode Sans Wide (ExtraBold),Encode Sans Wide (Black),Engagement (Regular),Englebert (Regular),Enriqueta (Regular),Enriqueta (Bold),Erica One (Regular),Esteban (Regular),Euphoria Script (Regular),Ewert (Regular),Exo 2 (Thin),Exo 2 (Extra Light),Exo 2 (Light),Exo 2 (Regular),Exo 2 (Medium),Exo 2 (Semi Bold),Exo 2 (Bold),Exo 2 (Extra Bold),Exo 2 (Black),Exo 2 (Thin Italic),Exo 2 (Extra Light Italic),Exo 2 (Light Italic),Exo 2 (Regular Italic),Exo 2 (Medium Italic),Exo 2 (Semi Bold Italic),Exo 2 (Bold Italic),Exo 2 (Extra Bold Italic),Exo 2 (Black Italic),Exo (Thin),Exo (ExtraLight),Exo (Light),Exo (Regular),Exo (Medium),Exo (DemiBold),Exo (Bold),Exo (ExtraBold),Exo (Black),Exo (ThinItalic),Exo (ExtraLightItalic),Exo (LightItalic),Exo (Italic),Exo (MediumItalic),Exo (DemiBoldItalic),Exo (BoldItalic),Exo (ExtraBoldItalic),Exo (BlackItalic),Expletus Sans (Regular),Expletus Sans (Medium),Expletus Sans (SemiBold),Expletus Sans (Bold),Expletus Sans (Italic),Expletus Sans (Medium Italic),Expletus Sans (SemiBold Italic),Expletus Sans (Bold Italic),Fanwood Text (Regular),Fanwood Text (Italic),Fascinate Inline (Regular),Fascinate (Regular),Faster One (Regular),Fasthand (Regular),Fauna One (Regular),Federant (Medium),Federo (Regular),Felipa (Regular),Fenix (Regular),FingerPaint (Regular),Fira Mono (Regular),Fira Mono (Medium),Fira Mono (Bold),Fira Sans (Light),Fira Sans (Regular),Fira Sans (Medium),Fira Sans (Bold),Fira Sans (Light Italic),Fira Sans (Italic),Fira Sans (Medium Italic),Fira Sans (Bold Italic),Fjalla One (Regular),Fjord (One),Flamenco (Light),Flamenco (Regular),Flavors (Regular),Fondamento (Regular),Fondamento (Italic),Fontdiner Swanky (Regular),Forum (Regular),Francois One (Regular),Freckle Face (Regular),Fredericka the Great (Regular),Fredoka One (Regular),Freehand (Regular),Fresca (Regular),Frijole (Regular),Fruktur (Regular),Fugaz One (Regular),Gabriela (Regular),Gafata (Regular),Galdeano (Regular),Galindo (Regular),Gentium Basic (Regular),Gentium Basic (Bold),Gentium Basic (Italic),Gentium Basic (Bold Italic),Gentium Book Basic (Regular),Gentium Book Basic (Bold),Gentium Book Basic (Italic),Gentium Book Basic (Bold Italic),Geostar Fill (Regular),Geostar (Regular),Geo (Regular),Geo (Oblique),Germania One (Regular),GFS Didot (Regular),GFS Didot (Bold),GFS Didot (Italic),GFS Didot (Bold Italic),GFS Neohellenic (Regular),GFS Neohellenic (Bold),GFS Neohellenic (Italic),GFS Neohellenic (Bold Italic),Gidugu (Regular),Gilda Display (Regular),Give You Glory (Regular),Glass Antiqua (Regular),Glegoo (Regular),Glegoo (Bold),Gloria Hallelujah (Regular),Goblin One (Regular),Gochi Hand (Regular),Gorditas (Regular),Gorditas (Bold),Goudy Bookletter 1911 (Regular),Graduate (Regular),Grand Hotel (Regular),Gravitas One (Regular),Great Vibes (Regular),Griffy (Regular),Gruppo (Regular),Gudea (Regular),Gudea (Bold),Gudea (Italic),Gurajada (Regular),Habibi (Regular),Halant (Light),Halant (Regular),Halant (Medium),Halant (Semibold),Halant (Bold),HammersmithOne (Regular),Hanalei Fill (Regular),Hanalei (Regular),Handlee (Regular),Hanuman (Regular),Hanuman (Bold),Happy Monkey (Regular),HeadlandOne (Regular),Henny Penny (Regular),Hermeneus One (Regular),Herr Von Muellerhoff (Regular),Hind (Light),Hind (Regular),Hind (Medium),Hind (Semibold),Hind (Bold),HintTest (Regular),Holtwood One SC (Regular),Homemade Apple (Regular),Homenaje (Regular),Iceberg (Regular),Iceland (Regular),IM FELL Double Pica SC (Regular),IM FELL Double Pica (Regular),IM FELL Double Pica (Italic),IM FELL DW Pica SC (Regular),IM FELL DW Pica (Regular),IM FELL DW Pica (Italic),IM FELL English SC (Regular),IM FELL English (Regular),IM FELL English (Italic),IM FELL French Canon SC (Regular),IM FELL French Canon (Regular),IM FELL French Canon (Italic),IM FELL Great Primer SC (Regular),IM FELL Great Primer (Regular),IM FELL Great Primer (Italic),Imprima (Regular),Inconsolata (Regular),Inconsolata (Bold),Inder (Regular),Indie Flower (Regular),Inika (Regular),Inika (Bold),InknutAntiqua (Regular),InknutAntiqua (Bold),Irish Grover (Regular),Irish Growler (Regular),Istok Web (Regular),Istok Web (Bold),Istok Web (Italic),Istok Web (BoldItalic),Italiana (Regular),Italianno (Regular),Jacques Francois Shadow (Regular),Jacques Francois (Regular),Jaldi (Regular),Jaldi (Bold),JejuGothic (Regular),JejuHallasan (Regular),JejuMyeongjo (Regular),Jim Nightshade (Regular),Jockey One (Regular),Jolly Lodger (Regular),Jomolhari (Regular),Josefin Sans Std (Regular),Josefin Sans (Thin),Josefin Sans (Light),Josefin Sans (Regular),Josefin Sans (SemiBold),Josefin Sans (Bold),Josefin Sans (Light Italic),Josefin Sans (Bold Italic),Josefin Slab (Thin),Josefin Slab (Light),Josefin Slab (Regular),Josefin Slab (SemiBold),Josefin Slab (Bold),Josefin Slab (Thin Italic),Josefin Slab (Light Italic),Josefin Slab (Italic),Josefin Slab (SemiBold Italic),Josefin Slab (Bold Italic),Joti One (Regular),Judson (Regular),Judson (Bold),Judson (Italic),Julee (Regular),Julius Sans One (Regular),Junge (Regular),Jura (Light),Jura (Book),Jura (Medium),Jura (DemiBold),Just Another Hand (Regular),Just Me Again Down Here (Regular),Kadwa (Regular),Kadwa (Bold),Kalam (Regular),Kalam (Regular),Kalam (Bold),Kameron (Regular),Kameron (Bold),Kantumruy Regular (Regular),Kantumruy Regular (Regular),Kantumruy Regular (Bold),Karla (Regular),Karla (Bold),Karla (Italic),Karla (BoldItalic),Karla Tamil (Regular),Karla Tamil (Bold),Karla Tamil (Inclined),Karla Tamil (Bold Inclined),Karma (Light),Karma (Regular),Karma (Medium),Karma (Semibold),Karma (Bold),Kaushan Script (Regular),Kavoon (Regular),Kdam Thmor (Regular),Keania One (Regular),Kelly Slab (Regular),Kenia (Regular),Khand (Regular),Khand (Medium),Khmer (Regular),Khula (Light),Khula (Regular),Khula (Semibold),Khula (Bold),Khula (ExtraBold),Kite One (Regular),Knewave (Regular),KoPub Batang (Light),KoPub Batang (Regular),KoPub Batang (Bold),Kotta One (Regular),Koulen (Regular),Kranky (Regular),Kreon (Light),Kreon (Regular),Kreon (Bold),Kristi (Medium),Krona One (Regular),La Belle Aurore (Regular),Laila (Light),Laila (Regular),Laila (Medium),Laila (Semibold),Laila (Bold),Lakki Reddy (Regular),Lancelot (Regular),Lao Muang Don (Regular),Lao Muang Khong (Regular),Lao Sans Pro (Regular),Lateef (Regular),Lato (Hairline),Lato (Light),Lato (Regular),Lato (Bold),Lato (Black),Lato (Hairline Italic),Lato (Light Italic),Lato (Italic),Lato (Bold Italic),Lato (Black Italic),League Script (League Script),Leckerli One (Regular),Ledger (Regular),Lekton (Regular),Lekton (Bold),Lekton (Italic),Lemon One (Regular),Lemon (Regular),Libre Baskerville (Regular),Libre Baskerville (Bold),Libre Baskerville (Italic),Libre Caslon Text (Regular),Libre Caslon Text (Bold),Libre Caslon Text (Italic),Life Savers (Regular),Life Savers (Bold),Life Savers (ExtraBold),Lilita One (Regular),Lily Script One (Regular),Limelight (Regular),Linden Hill (Regular),Linden Hill (Italic),Lobster (Regular),Lobster Two (Regular),Lobster Two (Bold),Lobster Two (Italic),Lobster Two (Bold Italic),Lohit Bengali (Regular),Lohit Devanagari (Regular),Lohit Tamil (Regular),Londrina Outline (Regular),Londrina Shadow (Regular),Londrina Sketch (Regular),Londrina Solid (Regular),Lora (Regular),Lora (Bold),Lora (Italic),Lora (Bold Italic),Loved by the King (Regular),Lovers Quarrel (Regular),Love Ya Like A Sister (Regular),Luckiest Guy (Regular),Lusitana (Regular),Lusitana (Bold),Lustria (Regular),Macondo (Regular),Macondo Swash Caps (Regular),Magra (Regular),Magra (Bold),Maiden Orange (Regular),Mako (Regular),Mallanna (Regular),Mandali (Regular),Marcellus SC (Regular),Marcellus (Regular),Marck Script (Regular),Margarine (Regular),Marko One (Regular),Marmelad (Regular),Martel Sans (ExtraLight),Martel Sans (Light),Martel Sans (Regular),Martel Sans (SemiBold),Martel Sans (Bold),Martel Sans (ExtraBold),Martel Sans (Black),Marvel (Regular),Marvel (Bold),Marvel (Italic),Marvel (Bold Italic),Mate SC (Regular),Mate (Regular),Mate (Italic),Maven Pro (Regular),Maven Pro (Medium),Maven Pro (Bold),Maven Pro (Black),McLaren (Regular),Meddon (Regular),MedievalSharp (Regular),Medula One (Regular),Megrim (Medium),Meie Script (Regular),Merge One (Regular),Merienda One (Regular),Merienda (Regular),Merienda (Bold),Merriweather Sans (Light),Merriweather Sans (Regular),Merriweather Sans (Bold),Merriweather Sans (ExtraBold),Merriweather Sans (Light Italic),Merriweather Sans (Italic),Merriweather Sans (Bold Italic),Merriweather Sans (ExtraBold Italic),Merriweather (Light),Merriweather (Regular),Merriweather (Bold),Merriweather (Heavy),Merriweather (Light Italic),Merriweather (Italic),Merriweather (Bold Italic),Merriweather (Heavy Italic),Mervale Script (Regular),Metal Mania (Regular),Metal (Regular),Metamorphous (Regular),Metrophobic (Regular),Miama (Regular),Michroma (Regular),Milonga (Regular),Miltonian (Regular),Miltonian Tattoo (Regular),Miniver (Regular),Miss Fajardose (Regular),Modak (Regular),Modern Antiqua (Regular),Molengo (Regular),Molle (Regular),Monda (Regular),Monda (Bold),Monda (Bold),Monofett (Regular),Monoton (Regular),Monsieur La Doulaise (Regular),Montaga (Regular),Montez (Regular),Montserrat Alternates (Regular),Montserrat Alternates (Bold),Montserrat (Thin),Montserrat (Light),Montserrat (Regular),Montserrat (Bold),Montserrat (Black),Montserrat Subrayada (Regular),Montserrat Subrayada (Bold),Moul Pali (Regular),Moul (Regular),Mountains of Christmas (Regular),Mountains of Christmas (Bold),Mouse Memoirs (Regular),Mr Bedfort (Regular),Mr Dafoe (Regular),Mr De Haviland (Regular),Mrs Saint Delafield (Regular),Mrs Sheppards (Regular),Muli (Light),Muli (Regular),Muli (Light Italic),Muli (Italic),Myanmar Sans Pro (Regular),Mystery Quest (Regular),Nanum Brush Script (Regular),NanumGothicCoding (Regular),NanumGothicCoding (Bold),NanumGothic (Regular),NanumGothic (Bold),NanumGothic (ExtraBold),NanumMyeongjo (Regular),NanumMyeongjo (Bold),NanumMyeongjo (ExtraBold),Nanum Pen (Regular),NATS (Regular),Neucha (Regular),Neuton (ExtraLight),Neuton (Light),Neuton (Regular),Neuton (Bold),Neuton (ExtraBold),Neuton (Italic),New Rocker (Regular),News Cycle (Regular),News Cycle (Bold),Niconne (Regular),Nimbus Mono L (Regular),Nimbus Mono L (Bold),Nimbus Mono L (Regular Oblique),Nimbus Mono L (Bold Oblique),Nimbus Roman No9 L (Regular),Nimbus Roman No9 L (Medium),Nimbus Roman No9 L (Regular Italic),Nimbus Roman No9 L (Medium Italic),Nimbus Sans L (Regular Condensed),Nimbus Sans L (Regular),Nimbus Sans L (Bold Condensed),Nimbus Sans L (Bold),Nimbus Sans L (Regular Condensed Italic),Nimbus Sans L (Regular Italic),Nimbus Sans L (Bold Condensed Italic),Nimbus Sans L (Bold Italic),Nixie One (Regular),Nobile (Regular),Nobile (Regular),Nobile (Medium),Nobile (Italic),Nobile (Medium Italic),Nobile (Bold Italic),Nokora Regular (Nokora Regular),Nokora Regular (Regular),Norican (Regular),Nosifer Caps (Regular),Nosifer (Regular),Nothing You Could Do (Regular),Noticia Text (Regular),Noticia Text (Bold),Noticia Text (Italic),Noticia Text (Bold Italic),Noto Kufi Arabic (Regular),Noto Kufi Arabic (Bold),Noto Naskh Arabic (Regular),Noto Naskh Arabic (Bold),Noto Nastaliq Urdu Draft (Regular),Noto Sans Armenian (Regular),Noto Sans Armenian (Bold),Noto Sans Avestan (Regular),Noto Sans Bengali (Regular),Noto Sans Bengali (Bold),Noto Sans Brahmi (Regular),Noto Sans Carian (Regular),Noto Sans Cherokee (Regular),Noto Sans Coptic (Regular),Noto Sans Cypriot Syllabary (Regular),Noto Sans Deseret (Regular),Noto Sans Devanagari (Regular),Noto Sans Devanagari (Bold),Noto Sans Devanagari UI (Regular),Noto Sans Devanagari UI (Bold),Noto Sans Egyptian Hieroglyphs (Regular),Noto Sans Ethiopic (Regular),Noto Sans Ethiopic (Bold),Noto Sans Georgian (Regular),Noto Sans Georgian (Bold),Noto Sans Glagolitic (Regular),Noto Sans Gujarati (Regular),Noto Sans Gujarati (Bold),Noto Sans Gujarati UI (Regular),Noto Sans Gujarati UI (Bold),Noto Sans Gurmukhi (Regular),Noto Sans Gurmukhi (Bold),Noto Sans Hanunoo (Regular),Noto Sans Hebrew (Regular),Noto Sans Hebrew (Bold),Noto Sans Imperial Aramaic (Regular),Noto Sans Kaithi (Regular),Noto Sans Kannada (Regular),Noto Sans Kannada (Bold),Noto Sans Kayah Li (Regular),Noto Sans Kharoshthi (Regular),Noto Sans Kufi Arabic (Regular),Noto Sans Kufi Arabic (Bold),Noto Sans Lao (Regular),Noto Sans Lao (Bold),Noto Sans Lao UI (Regular),Noto Sans Lao UI (Bold),Noto Sans Lisu (Regular),Noto Sans Lycian (Regular),Noto Sans Lydian (Regular),Noto Sans Malayalam (Regular),Noto Sans Malayalam (Bold),Noto Sans Mandaic (Regular),Noto Sans Meetei Mayek (Regular),Noto Sans Myanmar (Regular),Noto Sans Myanmar (Bold),Noto Sans NKo (Regular),Noto Sans (Regular),Noto Sans (Regular),Noto Sans (Regular),Noto Sans (Bold),Noto Sans (Bold),Noto Sans (Bold),Noto Sans (Italic),Noto Sans (Bold Italic),Noto Sans Old South Arabian (Regular),Noto Sans Old Turkic (Regular),Noto Sans Osmanya (Regular),Noto Sans Phoenician (Regular),Noto Sans Shavian (Regular),Noto Sans Sinhala (Regular),Noto Sans Sinhala (Bold),Noto Sans Symbols (Regular),Noto Sans Tagalog (Regular),Noto Sans Tai Tham (Regular),Noto Sans Tai Viet (Regular),Noto Sans Tamil (Regular),Noto Sans Tamil (Bold),Noto Sans Tamil UI (Regular),Noto Sans Tamil UI (Bold),Noto Sans Telugu (Regular),Noto Sans Telugu (Bold),Noto Sans Thai (Regular),Noto Sans Thai (Bold),Noto Sans Thai UI (Regular),Noto Sans Thai UI (Bold),Noto Sans Ugaritic (Regular),Noto Sans UI (Regular),Noto Sans UI (Bold),Noto Sans UI (Italic),Noto Sans UI (Bold Italic),Noto Sans Vai (Regular),Noto Serif Armenian (Regular),Noto Serif Armenian (Bold),Noto Serif Georgian (Regular),Noto Serif Georgian (Bold),Noto Serif Khmer (Regular),Noto Serif Khmer (Bold),Noto Serif Lao (Regular),Noto Serif Lao (Bold),Noto Serif (Regular),Noto Serif (Bold),Noto Serif (Italic),Noto Serif (Bold Italic),Noto Serif Thai (Regular),Noto Serif Thai (Bold),Nova Cut (Book),Nova Flat (Book),NovaMono (Regular),Nova Oval (Book),Nova Round (Book),Nova Script (Book),Nova Slim (Book),Nova Square (Book),NTR (Regular),Numans (Regular),Nunito (Light),Nunito (Regular),Nunito (Bold),OdorMeanChey (Regular),Offside (Regular),OFL Sorts Mill Goudy TT (Regular),OFL Sorts Mill Goudy TT (Italic),Oldenburg (Regular),Old Standard TT (Regular),Old Standard TT (Bold),Old Standard TT (Italic),Oleo Script (Regular),Oleo Script (Bold),Oleo Script Swash Caps (Regular),Oleo Script Swash Caps (Bold),Open Sans Condensed (Bold),Open Sans Hebrew Condensed (Light),Open Sans Hebrew Condensed (Regular),Open Sans Hebrew Condensed (Bold),Open Sans Hebrew Condensed (Extra Bold),Open Sans Hebrew Condensed (Light Italic),Open Sans Hebrew Condensed (Italic),Open Sans Hebrew Condensed (Bold Italic),Open Sans Hebrew Condensed (Extra Bold Italic),Open Sans Hebrew (Light),Open Sans Hebrew (Regular),Open Sans Hebrew (Bold),Open Sans Hebrew (Extra Bold),Open Sans Hebrew (Light Italic),Open Sans Hebrew (Italic),Open Sans Hebrew (Bold Italic),Open Sans Hebrew (Extra Bold Italic),Open Sans (Condensed Light),Open Sans (Light),Open Sans (Regular),Open Sans (Semibold),Open Sans (Bold),Open Sans (Extrabold),Open Sans (Condensed Light Italic),Open Sans (Light Italic),Open Sans (Italic),Open Sans (Semibold Italic),Open Sans (Bold Italic),Open Sans (Extrabold Italic),Oranienbaum (Regular),Orbitron (Regular),Orbitron (Medium),Orbitron (Bold),Orbitron (Black),Oregano (Regular),Oregano (Italic),Orienta (Regular),Original Surfer (Regular),Oswald (Light),Oswald (Regular),Oswald (Bold),Overlock SC (Regular),Overlock (Regular),Overlock (Bold),Overlock (Black),Overlock (Italic),Overlock (Bold Italic),Overlock (Black Italic),Over the Rainbow (Regular),Ovo (Regular),Oxygen Mono (Regular),Oxygen (Light),Oxygen (Regular),Oxygen (Bold),Pacifico (Regular),Padauk (Regular),Padauk (Regular),Padauk (Bold),Padauk (Bold),Palanquin Dark (Regular),Palanquin Dark (Medium),Palanquin Dark (SemiBold),Palanquin Dark (Bold),Palanquin (Thin),Palanquin (ExtraLight),Palanquin (Light),Palanquin (Regular),Palanquin (Medium),Palanquin (SemiBold),Palanquin (Bold),Paprika (Regular),Parisienne (Regular),Passero One (Regular),Passion One (Bold),Passion One (Black),Passion (Bold),Pathway Gothic One (Regular),Patrick Hand SC (Regular),Patrick Hand (Regular),Patua One (Regular),Paytone One (Regular),Pecita (Book),Peddana (Regular),Peralta (Regular),Permanent Marker (Regular),Petit Formal Script (Regular),Petrona (Regular),Phetsarath (Regular),Phetsarath (Bold),Philosopher (Regular),Philosopher (Bold),Philosopher (Italic),Philosopher (Bold Italic),Piedra (Regular),Pinyon Script (Regular),Pirata One (Regular),Plaster (Regular),Playball (Regular),Playfair Display (Regular),Playfair Display (Bold),Playfair Display (Black),Playfair Display (Italic),Playfair Display (Bold Italic),Playfair Display (Black Italic),Playfair Display SC (Regular),Playfair Display SC (Bold),Playfair Display SC (Black),Playfair Display SC (Italic),Playfair Display SC (Bold Italic),Playfair Display SC (Black Italic),Play (Regular),Play (Bold),Podkova (Regular),Podkova (Bold),PoetsenOne (Regular),Poiret One (Regular),Poller One (Regular),Poly (Regular),Poly (Italic),Pompiere  (Regular),Ponnala (Regular),Pontano Sans (Regular),Poppins (Light),Poppins (Regular),Poppins (Medium),Poppins (SemiBold),Poppins (Bold),Porter Sans Block (Block),Port Lligat Sans (Regular),Port Lligat Slab (Regular),Prata (Regular),Preah Vihear (Regular),Press Start 2P (Regular),Princess Sofia (Regular),Prociono (Regular),Prosto One (Regular),PT Mono (Regular),PT Sans Caption (Regular),PT Sans Caption (Bold),PT Sans Narrow (Regular),PT Sans Narrow (Bold),PT Sans (Regular),PT Sans (Bold),PT Sans (Italic),PT Sans (Bold Italic),PT Serif Caption (Regular),PT Serif Caption (Italic),PT Serif (Regular),PT Serif (Bold),PT Serif (Italic),PT Serif (Bold Italic),Puralecka Narrow (Regular),Puralecka Narrow (Bold),Puritan (Regular),Puritan (Bold),Puritan (Italic),Puritan (BoldItalic),Purple Purse (Regular),Quando (Regular),Quantico (Regular),Quantico (Bold),Quantico (Italic),Quantico (Bold Italic),Quattrocento Roman (Regular),Quattrocento Sans (Regular),Quattrocento Sans (Bold),Quattrocento Sans (Italic),Quattrocento Sans (Bold Italic),Quattrocento (Bold),Questrial (Regular),Quicksand (Light),Quicksand (Regular),Quicksand (Bold),Quicksand (LightItalic),Quicksand (Italic),Quicksand (BoldItalic),Quintessential (Regular),Qwigley (Regular),Racing Sans One (Regular),Radley (Regular),Radley (Italic),Rajdhani (Light),Rajdhani (Regular),Rajdhani (Medium),Rajdhani (Semibold),Rajdhani (Bold),Raleway Dots  (Regular),Raleway (Thin),Raleway (ExtraLight),Raleway (Light),Raleway (Regular),Raleway (Medium),Raleway (SemiBold),Raleway (Bold),Raleway (ExtraBold),Raleway (Black),Raleway (Thin Italic),Raleway (ExtraLight Italic),Raleway (Light Italic),Raleway (Italic),Raleway (Medium Italic),Raleway (SemiBold Italic),Raleway (Bold Italic),Raleway (ExtraBold Italic),Raleway (Black Italic),Ramabhadra (Regular),Ramaraja (Regular),Rambla (Regular),Rambla (Bold),Rambla (Italic),Rambla (Bold Italic),Rammetto One (Regular),Ranchers (Regular),Rancho (Regular),Ranga (Regular),Ranga (Bold),Rationale (Regular),Ravi Prakash (Regular),Redacted Script (Regular),Redacted Script (Bold),Redacted (Regular),Redressed (Regular),Reenie Beanie (Regular),Revalia (Regular),Ribeye Marrow (Regular),Ribeye (Regular),Righteous (Regular),Risque (Regular),Roboto Condensed (Light),Roboto Condensed (Regular),Roboto Condensed (Bold),Roboto Condensed (Light Italic),Roboto Condensed (Italic),Roboto Condensed (Bold Italic),Roboto (Thin),Roboto (Light),Roboto (Regular),Roboto (Medium),Roboto (Bold),Roboto (Black),Roboto (Thin Italic),Roboto (Light Italic),Roboto (Italic),Roboto (Medium Italic),Roboto (Bold Italic),Roboto (Black Italic),Roboto Slab (Thin),Roboto Slab (Light),Roboto Slab (Regular),Roboto Slab (Bold),Rochester (Regular),Rock Salt (Regular),Rokkitt (Light),Rokkitt (Regular),Rokkitt (Bold),Romanesco (Regular),Ropa Sans (Regular),Ropa Sans (Italic),Rosario (Regular),Rosario (Bold),Rosario (Italic),Rosario (Bold Italic),Rosarivo (Regular),Rosarivo (Italic),Rouge Script (Regular),Rozha One (Regular),Rubik Mono One (Regular),Rubik One (Regular),Ruda (Regular),Ruda (Bold),Ruda (Black),Rufina (Regular),Rufina (Bold),Ruge Boogie (Regular),Ruluko (Regular),Rum Raisin (Regular),Ruslan Display (Regular),Russo One (Regular),Ruthie (Regular),Rye (Regular),Sacramento (Regular),Sail (Regular),Salsa (Regular),Sanchez (Regular),Sanchez (Italic),Sancreek (Regular),Sansation Light (Light),Sansation Light (Light Italic),Sansation (Regular),Sansation (Bold),Sansation (Italic),Sansation (Bold Italic),Sansita One (Regular),Sarabun (Regular),Sarabun (Bold),Sarabun (Italic),Sarabun (Bold Italic),Sarala (Regular),Sarala (Bold),Sarina (Regular),Sarpanch (Regular),Sarpanch (Medium),Sarpanch (SemiBold),Sarpanch (Bold),Sarpanch (ExtraBold),Sarpanch (Black),Satisfy (Regular),Scada (Regular),Scada (Bold),Scada (Italic),Scada (Bold Italic),Scheherazade (Regular),Schoolbell (Regular),Seaweed Script (Regular),Sedan SC (Regular),Sedan (Regular),Sedan (Italic),SeoulHangang CB (Regular),SeoulHangang CBL (Regular),SeoulHangang CEB (Regular),SeoulHangang CL (Regular),SeoulHangang CM (Regular),SeoulHangang Smart B (Regular),SeoulNamsan CB (Regular),SeoulNamsan CBL (Regular),SeoulNamsan CEB (Regular),SeoulNamsan CL (Regular),SeoulNamsan CM (Regular),SeoulNamsan Smart B (Regular),Sevillana (Regular),Seymour One (Book),Shadows Into Light (Regular),Shadows Into Light Two (Regular),Shanti (Regular),Share (Regular),Share (Bold),Share (Italic),Share (Bold Italic),Share Tech Mono (Regular),Share Tech (Regular),Shojumaru (Regular),Short Stack (Regular),Siemreap (Regular),Sigmar One (Regular),Signika Negative (Light),Signika Negative (Regular),Signika Negative (Semibold),Signika Negative (Bold),Signika (Light),Signika (Regular),Signika (Semibold),Signika (Bold),Simonetta (Regular),Simonetta (Black),Simonetta (Italic),Simonetta (Black Italic),Sintony (Regular),Sintony (Bold),SirinStencil (Regular),Six Caps (Regular),Skranji (Regular),Skranji (Bold),Slabo 13px (Regular),Slabo 27px (Regular),Slackey (Regular),Smokum (Regular),Smythe (Regular),Sniglet (Regular),Sniglet (ExtraBold),Snippet (Regular),Snowburst One (Regular),Sofadi One (Regular),Sofia (Regular),Sonsie One (Regular),Sorts Mill Goudy (Regular),Sorts Mill Goudy (Italic),Souliyo Unicode (Regular),Source Code Pro (ExtraLight),Source Code Pro (Light),Source Code Pro (Regular),Source Code Pro (Medium),Source Code Pro (Semibold),Source Code Pro (Bold),Source Code Pro (Black),Source Sans Pro (ExtraLight),Source Sans Pro (Light),Source Sans Pro (Regular),Source Sans Pro (Semibold),Source Sans Pro (Bold),Source Sans Pro (Black),Source Sans Pro (ExtraLight Italic),Source Sans Pro (Light Italic),Source Sans Pro (Italic),Source Sans Pro (Semibold Italic),Source Sans Pro (Bold Italic),Source Sans Pro (Black Italic),Source Serif Pro (Regular),Source Serif Pro (Semibold),Source Serif Pro (Bold),Special Elite (Regular),Spicy Rice (Regular),Spinnaker (Regular),Spirax (Regular),Squada One (Regular),Sree Krushnadevaraya (Regular),Stalemate (Regular),Stalinist One (Regular),Stalin One (Regular),Standard Symbols L (Regular),Stardos Stencil (Regular),Stardos Stencil (Bold),Stint Ultra Condensed (Regular),Stint Ultra Expanded (Regular),Stoke (Light),Stoke (Regular),Strait (Regular),Strong (Regular),Sue Ellen Francisco  (Regular),Sunshiney (Regular),Supermercado (Regular),Suranna (Regular),Suravaram (Regular),Swanky and Moo Moo (Regular),Syncopate (Regular),Syncopate (Bold),Tangerine (Regular),Tangerine (Bold),Taprom (Regular),Tauri (Regular),Teko (Light),Teko (Regular),Teko (Medium),Teko (Semibold),Teko (Bold),Telex (Regular),Tenali Ramakrishna (Regular),Tenor Sans (Regular),Terminal Dosis (ExtraLight),Terminal Dosis (Light),Terminal Dosis (Regular),Terminal Dosis (Medium),Terminal Dosis (SemiBold),Terminal Dosis (Bold),Terminal Dosis (ExtraBold),Text Me One (Regular),Thabit (Regular),Thabit (Bold),Thabit (Oblique),Thabit (Bold Oblique),TharLon (Regular),The Girl Next Door (Regular),Tienne (Regular),Tienne (Bold),Tienne (Heavy),Tillana (Regular),Tillana (Medium),Tillana (SemiBold),Tillana (Bold),Tillana (ExtraBold),Timmana (Regular),Tinos (Regular),Tinos (Bold),Tinos (Italic),Tinos (Bold Italic),Titan One (Regular),Titillium Web (Thin),Titillium Web (Light),Titillium Web (Regular),Titillium Web (SemiBold),Titillium Web (Bold),Titillium Web (Black),Titillium Web (Thin Italic),Titillium Web (Light Italic),Titillium Web (Italic),Titillium Web (SemiBold Italic),Titillium Web (Bold Italic),Trade Winds (Regular),Trocchi (Regular),Trochut (Regular),Trochut (Bold),Trochut (Italic),Trykker (Regular),Tuffy (Regular),Tuffy (Bold),Tuffy (Italic),Tuffy (BoldItalic),Tulpen One (Regular),Tulpen (Light),Ubuntu Condensed (Regular),Ubuntu Monospaced (Regular),Ubuntu Monospaced (Bold),Ubuntu Monospaced (Italic),Ubuntu Monospaced (Bold Italic),Ubuntu Mono (Regular),Ubuntu Mono (Bold),Ubuntu Mono (Italic),Ubuntu Mono (Bold Italic),Ubuntu (Light),Ubuntu (Regular),Ubuntu (Medium),Ubuntu (Bold),Ubuntu (Light Italic),Ubuntu (Italic),Ubuntu (Medium Italic),Ubuntu (Bold Italic),Ultra (Regular),Uncial Antiqua (Regular),Underdog (Regular),Unica One (Regular),UnifrakturCook (Bold),UnifrakturMaguntia (Book),Unkempt (Regular),Unkempt (Bold),Unlock (Regular),Unna (Regular),URW Bookman L (Light),URW Bookman L (Demi Bold),URW Bookman L (Light Italic),URW Bookman L (Demi Bold Italic),URW Chancery L (Medium Italic),URW Gothic L (Book),URW Gothic L (Demi),URW Gothic L (Book Oblique),URW Gothic L (Demi Oblique),URW Palladio L (Roman),URW Palladio L (Bold),URW Palladio L (Italic),URW Palladio L (Bold Italic),Vampiro One (Regular),Varela Round (Regular),Varela (Regular),Vast Shadow (Regular),Vesper Libre (Regular),Vesper Libre (Medium),Vesper Libre (Bold),Vesper Libre (Heavy),Vibur (Medium),Vidaloka  (Regular),Viga (Regular),Voces (Regular),Volkhov (Regular),Volkhov (Bold),Volkhov (Italic),Volkhov (Bold Italic),Vollkorn (Regular),Vollkorn (Bold),Vollkorn (Italic),Vollkorn (Bold Italic),Voltaire (Regular),VT323 (Regular),Waiting for the Sunrise (Regular),Wallpoet (Regular),Walter Turncoat (Regular),Warnes (Regular),Wellfleet (Regular),Wendy One (Regular),Wire One (Regular),Yanone Kaffeesatz (Extra Light),Yanone Kaffeesatz (Light),Yanone Kaffeesatz (Regular),Yanone Kaffeesatz (Bold),Yellowtail (Regular),Yeseva One (Regular),Yesteryear (Regular),Zeyada (Regular)]

// small fonts are hard to print!
font_size=20; //[5,5.5,6,6.5,7,7.5,8,8.5,9,9.5,10,10.5,11,11.5,12,13,14,15,16,17,18,19,20,22,24,26,28,30,32,34,36,38,40,42,44,46,48,51,54,57,60,63,66,69,72,76,80,84,88,92,96]

font_narrow_widen=0; // [-50:100]
font_weight=7; // [-25:50]

//use 0 to remove outline
font_outline_width=0.1; //[0:0.1:10]
font_outline_style="rounded"; //[chamfer,delta:delta - does not work on some fonts (such as "Finger Paint"),rounded]

/*[Lug hole]*/
//in mm
lug_text_distance=2; //[-10:0.2:50]
//set to 0 to remove lug hole
lug_length=4.0; //[0:0.5:50]
lug_width=6.0; //[0:0.5:50]
//extends the hole and the bar
hole_extra_margin=0.4; //[0:0.2:25]
//
/*[Bar]*/

bar_shift=0; //[-50:50]

// in mm. Zero to remove.
function barlenfun(y=7) = (len(line2)>0) ? 35 : 17 ;
bar_width = barlenfun();//[0:.5:100]
bar_length_trim=0; //[-50:100]

//joins the glyph on the base layer
glyph_coalesce=25; // [0:100]
glyph_coalesce_strategy="bar and glyphs"; //[off,glyphs only,bar and glyphs]

/*[Border]*/

border_width=0;//[0:0.1:10]
inner_margin_width=1.4; //[0:0.1:10]
outer_margin_width=1.0; //[0:0.1:10]

/*[Layers]*/
// set to 0 to remove bar.
bar_thickness=3.1; 
outline_thickness=1;
text_thickness=4.7;
border_thickness=4.7;

/*[Colors]*/
// for Customizer preview only.
bar_color="Khaki";    //[---Purples---,Lavender,Thistle,Plum,Violet,Orchid,Fuchsia,Magenta,MediumOrchid,MediumPurple,BlueViolet,DarkViolet,DarkOrchid,DarkMagenta,Purple,Indigo,DarkSlateBlue,SlateBlue,MediumSlateBlue,,---Pinks---,Pink,LightPink,HotPink,DeepPink,MediumVioletRed,PaleVioletRed, ,---Blues---,Aqua,Cyan,LightCyan,PaleTurquoise,Aquamarine,Turquoise,MediumTurquoise,DarkTurquoise,CadetBlue,SteelBlue,LightSteelBlue,PowderBlue,LightBlue,SkyBlue,LightSkyBlue,DeepSkyBlue,DodgerBlue,CornflowerBlue,RoyalBlue,Blue,MediumBlue,DarkBlue,Navy,MidnightBlue,,---Reds---,IndianRed,LightCoral,Salmon,DarkSalmon,LightSalmon,Red,Crimson,FireBrick,DarkRed, ,---Greens---,GreenYellow,Chartreuse,LawnGreen,Lime,LimeGreen,PaleGreen,LightGreen,MediumSpringGreen,SpringGreen,MediumSeaGreen,SeaGreen,ForestGreen,Green,DarkGreen,YellowGreen,OliveDrab,Olive,DarkOliveGreen,MediumAquamarine,DarkSeaGreen,LightSeaGreen,DarkCyan,Teal,,---Oranges---,LightSalmon,Coral,Tomato,OrangeRed,DarkOrange,Orange, ,---Yellows---,Gold,Yellow,LightYellow,LemonChiffon,LightGoldenrodYellow,PapayaWhip,Moccasin,PeachPuff,PaleGoldenrod,Khaki,DarkKhaki,,---Browns---,Cornsilk,BlanchedAlmond,Bisque,NavajoWhite,Wheat,BurlyWood,Tan,RosyBrown,SandyBrown,Goldenrod,DarkGoldenrod,Peru,Chocolate,SaddleBrown,Sienna,Brown,Maroon, ,---Whites---,White,Snow,Honeydew,MintCream,Azure,AliceBlue,GhostWhite,WhiteSmoke,Seashell,Beige,OldLace,FloralWhite,Ivory,AntiqueWhite,Linen,LavenderBlush,MistyRose, ,---Grays---,Gainsboro,LightGray,Silver,DarkGray,Gray,DimGray,LightSlateGray,SlateGray,DarkSlateGray,Black]
outline_color="Gray"; //[---Purples---,Lavender,Thistle,Plum,Violet,Orchid,Fuchsia,Magenta,MediumOrchid,MediumPurple,BlueViolet,DarkViolet,DarkOrchid,DarkMagenta,Purple,Indigo,DarkSlateBlue,SlateBlue,MediumSlateBlue,,---Pinks---,Pink,LightPink,HotPink,DeepPink,MediumVioletRed,PaleVioletRed, ,---Blues---,Aqua,Cyan,LightCyan,PaleTurquoise,Aquamarine,Turquoise,MediumTurquoise,DarkTurquoise,CadetBlue,SteelBlue,LightSteelBlue,PowderBlue,LightBlue,SkyBlue,LightSkyBlue,DeepSkyBlue,DodgerBlue,CornflowerBlue,RoyalBlue,Blue,MediumBlue,DarkBlue,Navy,MidnightBlue,,---Reds---,IndianRed,LightCoral,Salmon,DarkSalmon,LightSalmon,Red,Crimson,FireBrick,DarkRed, ,---Greens---,GreenYellow,Chartreuse,LawnGreen,Lime,LimeGreen,PaleGreen,LightGreen,MediumSpringGreen,SpringGreen,MediumSeaGreen,SeaGreen,ForestGreen,Green,DarkGreen,YellowGreen,OliveDrab,Olive,DarkOliveGreen,MediumAquamarine,DarkSeaGreen,LightSeaGreen,DarkCyan,Teal,,---Oranges---,LightSalmon,Coral,Tomato,OrangeRed,DarkOrange,Orange, ,---Yellows---,Gold,Yellow,LightYellow,LemonChiffon,LightGoldenrodYellow,PapayaWhip,Moccasin,PeachPuff,PaleGoldenrod,Khaki,DarkKhaki,,---Browns---,Cornsilk,BlanchedAlmond,Bisque,NavajoWhite,Wheat,BurlyWood,Tan,RosyBrown,SandyBrown,Goldenrod,DarkGoldenrod,Peru,Chocolate,SaddleBrown,Sienna,Brown,Maroon, ,---Whites---,White,Snow,Honeydew,MintCream,Azure,AliceBlue,GhostWhite,WhiteSmoke,Seashell,Beige,OldLace,FloralWhite,Ivory,AntiqueWhite,Linen,LavenderBlush,MistyRose, ,---Grays---,Gainsboro,LightGray,Silver,DarkGray,Gray,DimGray,LightSlateGray,SlateGray,DarkSlateGray,Black]
text_color="DarkRed"; //[---Purples---,Lavender,Thistle,Plum,Violet,Orchid,Fuchsia,Magenta,MediumOrchid,MediumPurple,BlueViolet,DarkViolet,DarkOrchid,DarkMagenta,Purple,Indigo,DarkSlateBlue,SlateBlue,MediumSlateBlue,,---Pinks---,Pink,LightPink,HotPink,DeepPink,MediumVioletRed,PaleVioletRed, ,---Blues---,Aqua,Cyan,LightCyan,PaleTurquoise,Aquamarine,Turquoise,MediumTurquoise,DarkTurquoise,CadetBlue,SteelBlue,LightSteelBlue,PowderBlue,LightBlue,SkyBlue,LightSkyBlue,DeepSkyBlue,DodgerBlue,CornflowerBlue,RoyalBlue,Blue,MediumBlue,DarkBlue,Navy,MidnightBlue,,---Reds---,IndianRed,LightCoral,Salmon,DarkSalmon,LightSalmon,Red,Crimson,FireBrick,DarkRed, ,---Greens---,GreenYellow,Chartreuse,LawnGreen,Lime,LimeGreen,PaleGreen,LightGreen,MediumSpringGreen,SpringGreen,MediumSeaGreen,SeaGreen,ForestGreen,Green,DarkGreen,YellowGreen,OliveDrab,Olive,DarkOliveGreen,MediumAquamarine,DarkSeaGreen,LightSeaGreen,DarkCyan,Teal,,---Oranges---,LightSalmon,Coral,Tomato,OrangeRed,DarkOrange,Orange, ,---Yellows---,Gold,Yellow,LightYellow,LemonChiffon,LightGoldenrodYellow,PapayaWhip,Moccasin,PeachPuff,PaleGoldenrod,Khaki,DarkKhaki,,---Browns---,Cornsilk,BlanchedAlmond,Bisque,NavajoWhite,Wheat,BurlyWood,Tan,RosyBrown,SandyBrown,Goldenrod,DarkGoldenrod,Peru,Chocolate,SaddleBrown,Sienna,Brown,Maroon, ,---Whites---,White,Snow,Honeydew,MintCream,Azure,AliceBlue,GhostWhite,WhiteSmoke,Seashell,Beige,OldLace,FloralWhite,Ivory,AntiqueWhite,Linen,LavenderBlush,MistyRose, ,---Grays---,Gainsboro,LightGray,Silver,DarkGray,Gray,DimGray,LightSlateGray,SlateGray,DarkSlateGray,Black]
border_color="Black"; //[---Purples---,Lavender,Thistle,Plum,Violet,Orchid,Fuchsia,Magenta,MediumOrchid,MediumPurple,BlueViolet,DarkViolet,DarkOrchid,DarkMagenta,Purple,Indigo,DarkSlateBlue,SlateBlue,MediumSlateBlue,,---Pinks---,Pink,LightPink,HotPink,DeepPink,MediumVioletRed,PaleVioletRed, ,---Blues---,Aqua,Cyan,LightCyan,PaleTurquoise,Aquamarine,Turquoise,MediumTurquoise,DarkTurquoise,CadetBlue,SteelBlue,LightSteelBlue,PowderBlue,LightBlue,SkyBlue,LightSkyBlue,DeepSkyBlue,DodgerBlue,CornflowerBlue,RoyalBlue,Blue,MediumBlue,DarkBlue,Navy,MidnightBlue,,---Reds---,IndianRed,LightCoral,Salmon,DarkSalmon,LightSalmon,Red,Crimson,FireBrick,DarkRed, ,---Greens---,GreenYellow,Chartreuse,LawnGreen,Lime,LimeGreen,PaleGreen,LightGreen,MediumSpringGreen,SpringGreen,MediumSeaGreen,SeaGreen,ForestGreen,Green,DarkGreen,YellowGreen,OliveDrab,Olive,DarkOliveGreen,MediumAquamarine,DarkSeaGreen,LightSeaGreen,DarkCyan,Teal,,---Oranges---,LightSalmon,Coral,Tomato,OrangeRed,DarkOrange,Orange, ,---Yellows---,Gold,Yellow,LightYellow,LemonChiffon,LightGoldenrodYellow,PapayaWhip,Moccasin,PeachPuff,PaleGoldenrod,Khaki,DarkKhaki,,---Browns---,Cornsilk,BlanchedAlmond,Bisque,NavajoWhite,Wheat,BurlyWood,Tan,RosyBrown,SandyBrown,Goldenrod,DarkGoldenrod,Peru,Chocolate,SaddleBrown,Sienna,Brown,Maroon, ,---Whites---,White,Snow,Honeydew,MintCream,Azure,AliceBlue,GhostWhite,WhiteSmoke,Seashell,Beige,OldLace,FloralWhite,Ivory,AntiqueWhite,Linen,LavenderBlush,MistyRose, ,---Grays---,Gainsboro,LightGray,Silver,DarkGray,Gray,DimGray,LightSlateGray,SlateGray,DarkSlateGray,Black]


/*[Extruders]*/
//to print. 
which_extruder="monochrome_"; //[monochrome_:Monochrome,extruder1_:Extruder 1,extruder2_:Extruder 2,extruder3_:Extruder 3,extruder4_:Extruder 4]

bar_extruder="extruder1_"; //[extruder1_:Extruder 1,extruder2_:Extruder 2,extruder3_:Extruder 3,extruder4_:Extruder 4]
outline_extruder="extruder1_"; //[extruder1_:Extruder 1,extruder2_:Extruder 2,extruder3_:Extruder 3,extruder4_:Extruder 4]
text_extruder="extruder2_"; //[extruder1_:Extruder 1,extruder2_:Extruder 2,extruder3_:Extruder 3,extruder4_:Extruder 4]
border_extruder="extruder2_"; //[extruder1_:Extruder 1,extruder2_:Extruder 2,extruder3_:Extruder 3,extruder4_:Extruder 4]
function monochrome_part()="monochrome_";

minimal_color_layer_thickness = 0.6; //[0:.1:5]

/*[Hidden]*/

$fn=50;

fonts=[["ABeeZee",["Regular","Italic"]],["Abel",["Regular"]],["Abril Fatface",["Regular"]],["Aclonica",["Regular"]],["Acme",["Regular"]],["Actor",["Regular"]],["Adamina",["Regular"]],["Advent Pro",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold"]],["Aguafina Script",["Regular"]],["Akronim",["Regular"]],["Aksara Bali Galang",["Regular"]],["Aladin",["Regular"]],["Aldrich",["Regular"]],["Alef",["Regular","Bold"]],["Alegreya Sans",["Thin","Light","Regular","Medium","Bold","ExtraBold","Black","Thin Italic","Light Italic","Italic","Medium Italic","Bold Italic","ExtraBold Italic","Black Italic"]],["Alegreya Sans SC",["Thin","Light","Regular","Medium","Bold","ExtraBold","Black","Thin Italic","Light Italic","Italic","Medium Italic","Bold Italic","ExtraBold Italic","Black Italic"]],["Alegreya SC",["Regular","Bold","Black","Italic","Bold Italic","Black Italic"]],["Alegreya",["Regular","Bold","Black","Italic","Bold Italic","Black Italic"]],["Alex Brush",["Regular"]],["Alfa Slab One",["Regular"]],["Alice",["Regular"]],["Alike Angular",["Regular"]],["Alike",["Regular"]],["Allan",["Regular","Bold"]],["Allerta Stencil",["Regular"]],["Allerta",["Stencil","Regular","Medium"]],["Allura",["Regular"]],["Almendra Display",["Regular"]],["Almendra SC",["Regular","Bold","Italic","Bold Italic"]],["Almendra",["Regular","Bold","Italic","Bold Italic"]],["Amarante",["Regular"]],["Amaranth",["Regular","Bold","Italic","Bold Italic"]],["Amatic SC",["Regular","Bold"]],["Amethysta",["Regular"]],["Amiri",["Regular","Bold","Slanted","Bold Slanted"]],["Anaheim",["Regular"]],["Andada SC",["Regular","Bold","Italic","Bold Italic"]],["Andada",["Regular","Bold","Italic","Bold Italic"]],["Andika",["Regular"]],["Angkor",["Regular"]],["Annie Use Your Telescope",["Regular"]],["Anonymous Pro",["Regular","Bold","Italic","Bold Italic"]],["Antic Didone",["Regular"]],["Antic Slab",["Regular"]],["Antic",["Regular"]],["Antonio",["Light","Regular","Bold"]],["Anton",["Regular"]],["Arapey",["Regular","Italic"]],["Arbutus Slab",["Regular"]],["Arbutus",["Regular"]],["Architects Daughter",["Regular"]],["Archivo Black",["Regular"]],["Archivo Narrow",["Regular","Bold","Italic","Bold Italic"]],["Arimo",["Regular","Bold","Italic","Bold Italic"]],["Arizonia",["Regular"]],["Armata",["Regular"]],["Artifika",["Medium"]],["Arvo",["Regular","Bold","Italic","Bold Italic"]],["Asap",["Regular","Bold","Italic","Bold Italic"]],["Asset",["Regular"]],["Astloch",["Regular","Bold"]],["Asul",["Regular","Bold"]],["Atomic Age",["Regular"]],["Aubrey",["Regular"]],["Audiowide",["Regular"]],["Autour One",["Regular"]],["Average Sans",["Regular"]],["Average",["Regular"]],["Averia Gruesa Libre",["Regular"]],["Averia Libre",["Light","Regular","Bold","Light Italic","Italic","Bold Italic"]],["Averia Sans Libre",["Light","Regular","Bold","Light Italic","Italic","Bold Italic"]],["Averia Serif Libre",["Light","Regular","Bold","Light Italic","Italic","Bold Italic"]],["Bad Script",["Regular"]],["Bali Galang",["Regular"]],["Balthazar",["Regular"]],["Bangers",["Regular"]],["Basic",["Regular"]],["Battambang",["Regular","Bold"]],["Baumans",["Regular"]],["Bayon",["Regular"]],["Belgrano",["Regular"]],["Belleza",["Regular"]],["BenchNine",["Light","Regular","Bold"]],["Bentham",["Regular"]],["Berkshire Swash",["Regular"]],["Bevan",["Regular"]],["Bhavuka",["Regular"]],["Bigelow Rules",["Regular"]],["Bigshot One",["Regular"]],["Bilbo",["Regular"]],["Bilbo Swash Caps",["Regular"]],["Bitter",["Regular","Bold","Italic"]],["Black Ops One",["Regular"]],["BM HANNA_TTF",["Regular"]],["Bokor",["Regular"]],["Bonbon",["Regular"]],["Boogaloo",["Regular"]],["Bowlby One SC",["Regular"]],["Bowlby One",["Regular"]],["Brawler",["Regular"]],["Bree Serif",["Regular"]],["Bruno Ace SC",["Regular"]],["Bruno Ace",["Regular"]],["Bubblegum Sans",["Regular"]],["Bubbler One",["Regular"]],["Buda",["light"]],["Buenard",["Regular","Bold"]],["Butcherman Caps",["Regular"]],["Butcherman",["Regular"]],["Butterfly Kids",["Regular"]],["Cabin Condensed",["Regular","Medium","SemiBold","Bold"]],["CabinSketch",["Bold"]],["Cabin Sketch",["Regular"]],["Cabin",["Regular","Medium","SemiBold","Bold","Italic","Medium Italic","SemiBold Italic","Bold Italic"]],["Caesar Dressing",["Regular"]],["Cagliostro",["Regular"]],["Calligraffitti",["Regular"]],["Cambay",["Regular","Bold","Italic","Bold Italic"]],["Cambo",["Regular"]],["Candal",["Regular"]],["Cantarell",["Regular","Bold","Oblique","BoldOblique"]],["Cantata One",["Regular"]],["CantoraOne",["Regular"]],["Capriola",["Regular"]],["Cardo",["Regular","Bold","Italic"]],["Carme",["Regular"]],["CarroisGothic Caps",["Regular"]],["CarroisGothic",["Regular"]],["Carter One",["Regular"]],["Caudex",["Regular","Bold","Italic","Bold Italic"]],["Cedarville Cursive",["Regular"]],["Century Schoolbook L",["Roman","Bold","Italic","Bold Italic"]],["Ceviche One",["Regular"]],["Changa One",["Regular","Italic"]],["Changa",["Regular"]],["Chango",["Regular"]],["Chau Philomene One",["Regular","Italic"]],["Chela One",["Regular"]],["Chelsea Market",["Regular"]],["Chenla",["Regular"]],["Cherry Cream Soda",["Regular"]],["Cherry Swash",["Regular","Bold"]],["Chewy",["Regular"]],["Chicle",["Regular"]],["Chivo",["Regular","Black","Italic","Black Italic"]],["Cinzel",["Regular","Bold","Black"]],["Cinzel Decorative",["Regular","Bold","Black"]],["Clara",["Regular"]],["Clicker Script",["Regular"]],["Coda Caption",["Heavy"]],["Coda",["Regular","Heavy"]],["Codystar",["Light","Regular"]],["Combo",["Regular"]],["Comfortaa",["Light","Regular","Bold"]],["Coming Soon",["Regular"]],["Concert One",["Regular"]],["Condiment",["Regular"]],["Content",["Regular","Bold"]],["Contrail One",["Regular"]],["Convergence",["Regular"]],["Cookie",["Regular"]],["Copse",["Regular"]],["Corben",["Regular","Bold"]],["Courgette",["Regular"]],["Cousine",["Regular","Bold","Italic","Bold Italic"]],["Coustard",["Regular","Bold"]],["Covered By Your Grace",["Regular"]],["Crafty Girls",["Regular"]],["Creepster Caps",["Regular"]],["Creepster",["Regular"]],["Crete Round",["Regular","Italic"]],["Crimson Text",["Roman","Semibold","Bold","Italic","SemiboldItalic","BoldItalic"]],["Croissant One",["Regular"]],["Crushed",["Regular"]],["Cuprum",["Regular","Bold","Italic","Bold Italic"]],["Cutive Mono",["Regular"]],["Cutive",["Regular"]],["Damion",["Regular"]],["Dancing Script",["Regular","Bold"]],["Dangrek",["Regular"]],["Dawning of a New Day",["Regular"]],["Days One",["Regular"]],["DejaVu Sans Mono",["Book","Bold"]],["DejaVu Sans",["Book","Bold"]],["DejaVu Serif",["Book","Bold"]],["Dekko",["Regular"]],["Delius",["Regular"]],["Delius Swash Caps",["Regular"]],["Delius Unicase",["Regular","Bold"]],["Della Respira",["Regular"]],["Denk One",["Regular"]],["Devonshire",["Regular"]],["Dhurjati",["Regular"]],["Dhyana",["Regular","Bold"]],["Didact Gothic",["Regular"]],["Dinah",["Regular"]],["DinahVAA",["Bold"]],["Dingbats",["Regular"]],["Diplomata SC",["Regular"]],["Diplomata",["Regular"]],["Domine",["Regular","Bold"]],["Donegal One",["Regular"]],["Doppio One",["Regular"]],["Dorsa",["Regular"]],["Dosis",["ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold"]],["Droid Arabic Kufi",["Regular","Bold"]],["Droid Arabic Naskh",["Regular","Bold"]],["Droid Sans Ethiopic",["Regular","Bold"]],["Droid Sans Japanese",["Regular"]],["Droid Sans Mono",["Regular"]],["Droid Sans",["Regular","Bold"]],["Droid Sans Tamil",["Regular","Bold"]],["Droid Sans Thai",["Regular","Bold"]],["Droid Serif",["Regular","Bold","Italic","Bold Italic"]],["DroidSerifThai",["Bold"]],["Droid Serif Thai",["Regular"]],["Dr Sugiyama",["Regular"]],["Duru Sans",["Regular"]],["Dynalight",["Regular"]],["Eagle Lake",["Regular"]],["Eater Caps",["Regular"]],["Eater",["Regular"]],["EB Garamond",["Regular"]],["Economica",["Regular","Bold","Italic","Bold Italic"]],["Eczar",["Regular","Medium","Semibold","Bold","Extrabold"]],["Ek Mukta",["ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold"]],["Electrolize",["Regular"]],["Elsie",["Regular","Black"]],["Elsie Swash Caps",["Regular","Black"]],["Emblema One",["Regular"]],["Emilys Candy",["Regular"]],["Encode Sans Compressed",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Encode Sans Condensed",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Encode Sans Narrow",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Encode Sans",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Encode Sans Wide",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Engagement",["Regular"]],["Englebert",["Regular"]],["Enriqueta",["Regular","Bold"]],["Erica One",["Regular"]],["Esteban",["Regular"]],["Euphoria Script",["Regular"]],["Ewert",["Regular"]],["Exo 2",["Thin","Extra Light","Light","Regular","Medium","Semi Bold","Bold","Extra Bold","Black","Thin Italic","Extra Light Italic","Light Italic","Regular Italic","Medium Italic","Semi Bold Italic","Bold Italic","Extra Bold Italic","Black Italic"]],["Exo",["Thin","ExtraLight","Light","Regular","Medium","DemiBold","Bold","ExtraBold","Black","ThinItalic","ExtraLightItalic","LightItalic","Italic","MediumItalic","DemiBoldItalic","BoldItalic","ExtraBoldItalic","BlackItalic"]],["Expletus Sans",["Regular","Medium","SemiBold","Bold","Italic","Medium Italic","SemiBold Italic","Bold Italic"]],["Fanwood Text",["Regular","Italic"]],["Fascinate Inline",["Regular"]],["Fascinate",["Regular"]],["Faster One",["Regular"]],["Fasthand",["Regular"]],["Fauna One",["Regular"]],["Federant",["Medium"]],["Federo",["Regular"]],["Felipa",["Regular"]],["Fenix",["Regular"]],["FingerPaint",["Regular"]],["Fira Mono",["Regular","Medium","Bold"]],["Fira Sans",["Light","Regular","Medium","Bold","Light Italic","Italic","Medium Italic","Bold Italic"]],["Fjalla One",["Regular"]],["Fjord",["One"]],["Flamenco",["Light","Regular"]],["Flavors",["Regular"]],["Fondamento",["Regular","Italic"]],["Fontdiner Swanky",["Regular"]],["Forum",["Regular"]],["Francois One",["Regular"]],["Freckle Face",["Regular"]],["Fredericka the Great",["Regular"]],["Fredoka One",["Regular"]],["Freehand",["Regular"]],["Fresca",["Regular"]],["Frijole",["Regular"]],["Fruktur",["Regular"]],["Fugaz One",["Regular"]],["Gabriela",["Regular"]],["Gafata",["Regular"]],["Galdeano",["Regular"]],["Galindo",["Regular"]],["Gentium Basic",["Regular","Bold","Italic","Bold Italic"]],["Gentium Book Basic",["Regular","Bold","Italic","Bold Italic"]],["Geostar Fill",["Regular"]],["Geostar",["Regular"]],["Geo",["Regular","Oblique"]],["Germania One",["Regular"]],["GFS Didot",["Regular","Bold","Italic","Bold Italic"]],["GFS Neohellenic",["Regular","Bold","Italic","Bold Italic"]],["Gidugu",["Regular"]],["Gilda Display",["Regular"]],["Give You Glory",["Regular"]],["Glass Antiqua",["Regular"]],["Glegoo",["Regular","Bold"]],["Gloria Hallelujah",["Regular"]],["Goblin One",["Regular"]],["Gochi Hand",["Regular"]],["Gorditas",["Regular","Bold"]],["Goudy Bookletter 1911",["Regular"]],["Graduate",["Regular"]],["Grand Hotel",["Regular"]],["Gravitas One",["Regular"]],["Great Vibes",["Regular"]],["Griffy",["Regular"]],["Gruppo",["Regular"]],["Gudea",["Regular","Bold","Italic"]],["Gurajada",["Regular"]],["Habibi",["Regular"]],["Halant",["Light","Regular","Medium","Semibold","Bold"]],["HammersmithOne",["Regular"]],["Hanalei Fill",["Regular"]],["Hanalei",["Regular"]],["Handlee",["Regular"]],["Hanuman",["Regular","Bold"]],["Happy Monkey",["Regular"]],["HeadlandOne",["Regular"]],["Henny Penny",["Regular"]],["Hermeneus One",["Regular"]],["Herr Von Muellerhoff",["Regular"]],["Hind",["Light","Regular","Medium","Semibold","Bold"]],["HintTest",["Regular"]],["Holtwood One SC",["Regular"]],["Homemade Apple",["Regular"]],["Homenaje",["Regular"]],["Iceberg",["Regular"]],["Iceland",["Regular"]],["IM FELL Double Pica SC",["Regular"]],["IM FELL Double Pica",["Regular","Italic"]],["IM FELL DW Pica SC",["Regular"]],["IM FELL DW Pica",["Regular","Italic"]],["IM FELL English SC",["Regular"]],["IM FELL English",["Regular","Italic"]],["IM FELL French Canon SC",["Regular"]],["IM FELL French Canon",["Regular","Italic"]],["IM FELL Great Primer SC",["Regular"]],["IM FELL Great Primer",["Regular","Italic"]],["Imprima",["Regular"]],["Inconsolata",["Regular","Bold"]],["Inder",["Regular"]],["Indie Flower",["Regular"]],["Inika",["Regular","Bold"]],["InknutAntiqua",["Regular","Bold"]],["Irish Grover",["Regular"]],["Irish Growler",["Regular"]],["Istok Web",["Regular","Bold","Italic","BoldItalic"]],["Italiana",["Regular"]],["Italianno",["Regular"]],["Jacques Francois Shadow",["Regular"]],["Jacques Francois",["Regular"]],["Jaldi",["Regular","Bold"]],["JejuGothic",["Regular"]],["JejuHallasan",["Regular"]],["JejuMyeongjo",["Regular"]],["Jim Nightshade",["Regular"]],["Jockey One",["Regular"]],["Jolly Lodger",["Regular"]],["Jomolhari",["Regular"]],["Josefin Sans Std",["Regular"]],["Josefin Sans",["Thin","Light","Regular","SemiBold","Bold","Light Italic","Bold Italic"]],["Josefin Slab",["Thin","Light","Regular","SemiBold","Bold","Thin Italic","Light Italic","Italic","SemiBold Italic","Bold Italic"]],["Joti One",["Regular"]],["Judson",["Regular","Bold","Italic"]],["Julee",["Regular"]],["Julius Sans One",["Regular"]],["Junge",["Regular"]],["Jura",["Light","Book","Medium","DemiBold"]],["Just Another Hand",["Regular"]],["Just Me Again Down Here",["Regular"]],["Kadwa",["Regular","Bold"]],["Kalam",["Regular","Regular","Bold"]],["Kameron",["Regular","Bold"]],["Kantumruy Regular",["Regular","Regular","Bold"]],["Karla",["Regular","Bold","Italic","BoldItalic"]],["Karla Tamil",["Regular","Bold","Inclined","Bold Inclined"]],["Karma",["Light","Regular","Medium","Semibold","Bold"]],["Kaushan Script",["Regular"]],["Kavoon",["Regular"]],["Kdam Thmor",["Regular"]],["Keania One",["Regular"]],["Kelly Slab",["Regular"]],["Kenia",["Regular"]],["Khand",["Regular","Medium"]],["Khmer",["Regular"]],["Khula",["Light","Regular","Semibold","Bold","ExtraBold"]],["Kite One",["Regular"]],["Knewave",["Regular"]],["KoPub Batang",["Light","Regular","Bold"]],["Kotta One",["Regular"]],["Koulen",["Regular"]],["Kranky",["Regular"]],["Kreon",["Light","Regular","Bold"]],["Kristi",["Medium"]],["Krona One",["Regular"]],["La Belle Aurore",["Regular"]],["Laila",["Light","Regular","Medium","Semibold","Bold"]],["Lakki Reddy",["Regular"]],["Lancelot",["Regular"]],["Lao Muang Don",["Regular"]],["Lao Muang Khong",["Regular"]],["Lao Sans Pro",["Regular"]],["Lateef",["Regular"]],["Lato",["Hairline","Light","Regular","Bold","Black","Hairline Italic","Light Italic","Italic","Bold Italic","Black Italic"]],["League Script",["League Script"]],["Leckerli One",["Regular"]],["Ledger",["Regular"]],["Lekton",["Regular","Bold","Italic"]],["Lemon One",["Regular"]],["Lemon",["Regular"]],["Libre Baskerville",["Regular","Bold","Italic"]],["Libre Caslon Text",["Regular","Bold","Italic"]],["Life Savers",["Regular","Bold","ExtraBold"]],["Lilita One",["Regular"]],["Lily Script One",["Regular"]],["Limelight",["Regular"]],["Linden Hill",["Regular","Italic"]],["Lobster",["Regular"]],["Lobster Two",["Regular","Bold","Italic","Bold Italic"]],["Lohit Bengali",["Regular"]],["Lohit Devanagari",["Regular"]],["Lohit Tamil",["Regular"]],["Londrina Outline",["Regular"]],["Londrina Shadow",["Regular"]],["Londrina Sketch",["Regular"]],["Londrina Solid",["Regular"]],["Lora",["Regular","Bold","Italic","Bold Italic"]],["Loved by the King",["Regular"]],["Lovers Quarrel",["Regular"]],["Love Ya Like A Sister",["Regular"]],["Luckiest Guy",["Regular"]],["Lusitana",["Regular","Bold"]],["Lustria",["Regular"]],["Macondo",["Regular"]],["Macondo Swash Caps",["Regular"]],["Magra",["Regular","Bold"]],["Maiden Orange",["Regular"]],["Mako",["Regular"]],["Mallanna",["Regular"]],["Mandali",["Regular"]],["Marcellus SC",["Regular"]],["Marcellus",["Regular"]],["Marck Script",["Regular"]],["Margarine",["Regular"]],["Marko One",["Regular"]],["Marmelad",["Regular"]],["Martel Sans",["ExtraLight","Light","Regular","SemiBold","Bold","ExtraBold","Black"]],["Marvel",["Regular","Bold","Italic","Bold Italic"]],["Mate SC",["Regular"]],["Mate",["Regular","Italic"]],["Maven Pro",["Regular","Medium","Bold","Black"]],["McLaren",["Regular"]],["Meddon",["Regular"]],["MedievalSharp",["Regular"]],["Medula One",["Regular"]],["Megrim",["Medium"]],["Meie Script",["Regular"]],["Merge One",["Regular"]],["Merienda One",["Regular"]],["Merienda",["Regular","Bold"]],["Merriweather Sans",["Light","Regular","Bold","ExtraBold","Light Italic","Italic","Bold Italic","ExtraBold Italic"]],["Merriweather",["Light","Regular","Bold","Heavy","Light Italic","Italic","Bold Italic","Heavy Italic"]],["Mervale Script",["Regular"]],["Metal Mania",["Regular"]],["Metal",["Regular"]],["Metamorphous",["Regular"]],["Metrophobic",["Regular"]],["Miama",["Regular"]],["Michroma",["Regular"]],["Milonga",["Regular"]],["Miltonian",["Regular"]],["Miltonian Tattoo",["Regular"]],["Miniver",["Regular"]],["Miss Fajardose",["Regular"]],["Modak",["Regular"]],["Modern Antiqua",["Regular"]],["Molengo",["Regular"]],["Molle",["Regular"]],["Monda",["Regular","Bold","Bold"]],["Monofett",["Regular"]],["Monoton",["Regular"]],["Monsieur La Doulaise",["Regular"]],["Montaga",["Regular"]],["Montez",["Regular"]],["Montserrat Alternates",["Regular","Bold"]],["Montserrat",["Thin","Light","Regular","Bold","Black"]],["Montserrat Subrayada",["Regular","Bold"]],["Moul Pali",["Regular"]],["Moul",["Regular"]],["Mountains of Christmas",["Regular","Bold"]],["Mouse Memoirs",["Regular"]],["Mr Bedfort",["Regular"]],["Mr Dafoe",["Regular"]],["Mr De Haviland",["Regular"]],["Mrs Saint Delafield",["Regular"]],["Mrs Sheppards",["Regular"]],["Muli",["Light","Regular","Light Italic","Italic"]],["Myanmar Sans Pro",["Regular"]],["Mystery Quest",["Regular"]],["Nanum Brush Script",["Regular"]],["NanumGothicCoding",["Regular","Bold"]],["NanumGothic",["Regular","Bold","ExtraBold"]],["NanumMyeongjo",["Regular","Bold","ExtraBold"]],["Nanum Pen",["Regular"]],["NATS",["Regular"]],["Neucha",["Regular"]],["Neuton",["ExtraLight","Light","Regular","Bold","ExtraBold","Italic"]],["New Rocker",["Regular"]],["News Cycle",["Regular","Bold"]],["Niconne",["Regular"]],["Nimbus Mono L",["Regular","Bold","Regular Oblique","Bold Oblique"]],["Nimbus Roman No9 L",["Regular","Medium","Regular Italic","Medium Italic"]],["Nimbus Sans L",["Regular Condensed","Regular","Bold Condensed","Bold","Regular Condensed Italic","Regular Italic","Bold Condensed Italic","Bold Italic"]],["Nixie One",["Regular"]],["Nobile",["Regular","Regular","Medium","Italic","Medium Italic","Bold Italic"]],["Nokora Regular",["Nokora Regular","Regular"]],["Norican",["Regular"]],["Nosifer Caps",["Regular"]],["Nosifer",["Regular"]],["Nothing You Could Do",["Regular"]],["Noticia Text",["Regular","Bold","Italic","Bold Italic"]],["Noto Kufi Arabic",["Regular","Bold"]],["Noto Naskh Arabic",["Regular","Bold"]],["Noto Nastaliq Urdu Draft",["Regular"]],["Noto Sans Armenian",["Regular","Bold"]],["Noto Sans Avestan",["Regular"]],["Noto Sans Bengali",["Regular","Bold"]],["Noto Sans Brahmi",["Regular"]],["Noto Sans Carian",["Regular"]],["Noto Sans Cherokee",["Regular"]],["Noto Sans Coptic",["Regular"]],["Noto Sans Cypriot Syllabary",["Regular"]],["Noto Sans Deseret",["Regular"]],["Noto Sans Devanagari",["Regular","Bold"]],["Noto Sans Devanagari UI",["Regular","Bold"]],["Noto Sans Egyptian Hieroglyphs",["Regular"]],["Noto Sans Ethiopic",["Regular","Bold"]],["Noto Sans Georgian",["Regular","Bold"]],["Noto Sans Glagolitic",["Regular"]],["Noto Sans Gujarati",["Regular","Bold"]],["Noto Sans Gujarati UI",["Regular","Bold"]],["Noto Sans Gurmukhi",["Regular","Bold"]],["Noto Sans Hanunoo",["Regular"]],["Noto Sans Hebrew",["Regular","Bold"]],["Noto Sans Imperial Aramaic",["Regular"]],["Noto Sans Kaithi",["Regular"]],["Noto Sans Kannada",["Regular","Bold"]],["Noto Sans Kayah Li",["Regular"]],["Noto Sans Kharoshthi",["Regular"]],["Noto Sans Kufi Arabic",["Regular","Bold"]],["Noto Sans Lao",["Regular","Bold"]],["Noto Sans Lao UI",["Regular","Bold"]],["Noto Sans Lisu",["Regular"]],["Noto Sans Lycian",["Regular"]],["Noto Sans Lydian",["Regular"]],["Noto Sans Malayalam",["Regular","Bold"]],["Noto Sans Mandaic",["Regular"]],["Noto Sans Meetei Mayek",["Regular"]],["Noto Sans Myanmar",["Regular","Bold"]],["Noto Sans NKo",["Regular"]],["Noto Sans",["Regular","Regular","Regular","Bold","Bold","Bold","Italic","Bold Italic"]],["Noto Sans Old South Arabian",["Regular"]],["Noto Sans Old Turkic",["Regular"]],["Noto Sans Osmanya",["Regular"]],["Noto Sans Phoenician",["Regular"]],["Noto Sans Shavian",["Regular"]],["Noto Sans Sinhala",["Regular","Bold"]],["Noto Sans Symbols",["Regular"]],["Noto Sans Tagalog",["Regular"]],["Noto Sans Tai Tham",["Regular"]],["Noto Sans Tai Viet",["Regular"]],["Noto Sans Tamil",["Regular","Bold"]],["Noto Sans Tamil UI",["Regular","Bold"]],["Noto Sans Telugu",["Regular","Bold"]],["Noto Sans Thai",["Regular","Bold"]],["Noto Sans Thai UI",["Regular","Bold"]],["Noto Sans Ugaritic",["Regular"]],["Noto Sans UI",["Regular","Bold","Italic","Bold Italic"]],["Noto Sans Vai",["Regular"]],["Noto Serif Armenian",["Regular","Bold"]],["Noto Serif Georgian",["Regular","Bold"]],["Noto Serif Khmer",["Regular","Bold"]],["Noto Serif Lao",["Regular","Bold"]],["Noto Serif",["Regular","Bold","Italic","Bold Italic"]],["Noto Serif Thai",["Regular","Bold"]],["Nova Cut",["Book"]],["Nova Flat",["Book"]],["NovaMono",["Regular"]],["Nova Oval",["Book"]],["Nova Round",["Book"]],["Nova Script",["Book"]],["Nova Slim",["Book"]],["Nova Square",["Book"]],["NTR",["Regular"]],["Numans",["Regular"]],["Nunito",["Light","Regular","Bold"]],["OdorMeanChey",["Regular"]],["Offside",["Regular"]],["OFL Sorts Mill Goudy TT",["Regular","Italic"]],["Oldenburg",["Regular"]],["Old Standard TT",["Regular","Bold","Italic"]],["Oleo Script",["Regular","Bold"]],["Oleo Script Swash Caps",["Regular","Bold"]],["Open Sans Condensed",["Bold"]],["Open Sans Hebrew Condensed",["Light","Regular","Bold","Extra Bold","Light Italic","Italic","Bold Italic","Extra Bold Italic"]],["Open Sans Hebrew",["Light","Regular","Bold","Extra Bold","Light Italic","Italic","Bold Italic","Extra Bold Italic"]],["Open Sans",["Condensed Light","Light","Regular","Semibold","Bold","Extrabold","Condensed Light Italic","Light Italic","Italic","Semibold Italic","Bold Italic","Extrabold Italic"]],["Oranienbaum",["Regular"]],["Orbitron",["Regular","Medium","Bold","Black"]],["Oregano",["Regular","Italic"]],["Orienta",["Regular"]],["Original Surfer",["Regular"]],["Oswald",["Light","Regular","Bold"]],["Overlock SC",["Regular"]],["Overlock",["Regular","Bold","Black","Italic","Bold Italic","Black Italic"]],["Over the Rainbow",["Regular"]],["Ovo",["Regular"]],["Oxygen Mono",["Regular"]],["Oxygen",["Light","Regular","Bold"]],["Pacifico",["Regular"]],["Padauk",["Regular","Regular","Bold","Bold"]],["Palanquin Dark",["Regular","Medium","SemiBold","Bold"]],["Palanquin",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold"]],["Paprika",["Regular"]],["Parisienne",["Regular"]],["Passero One",["Regular"]],["Passion One",["Bold","Black"]],["Passion",["Bold"]],["Pathway Gothic One",["Regular"]],["Patrick Hand SC",["Regular"]],["Patrick Hand",["Regular"]],["Patua One",["Regular"]],["Paytone One",["Regular"]],["Pecita",["Book"]],["Peddana",["Regular"]],["Peralta",["Regular"]],["Permanent Marker",["Regular"]],["Petit Formal Script",["Regular"]],["Petrona",["Regular"]],["Phetsarath",["Regular","Bold"]],["Philosopher",["Regular","Bold","Italic","Bold Italic"]],["Piedra",["Regular"]],["Pinyon Script",["Regular"]],["Pirata One",["Regular"]],["Plaster",["Regular"]],["Playball",["Regular"]],["Playfair Display",["Regular","Bold","Black","Italic","Bold Italic","Black Italic"]],["Playfair Display SC",["Regular","Bold","Black","Italic","Bold Italic","Black Italic"]],["Play",["Regular","Bold"]],["Podkova",["Regular","Bold"]],["PoetsenOne",["Regular"]],["Poiret One",["Regular"]],["Poller One",["Regular"]],["Poly",["Regular","Italic"]],["Pompiere ",["Regular"]],["Ponnala",["Regular"]],["Pontano Sans",["Regular"]],["Poppins",["Light","Regular","Medium","SemiBold","Bold"]],["Porter Sans Block",["Block"]],["Port Lligat Sans",["Regular"]],["Port Lligat Slab",["Regular"]],["Prata",["Regular"]],["Preah Vihear",["Regular"]],["Press Start 2P",["Regular"]],["Princess Sofia",["Regular"]],["Prociono",["Regular"]],["Prosto One",["Regular"]],["PT Mono",["Regular"]],["PT Sans Caption",["Regular","Bold"]],["PT Sans Narrow",["Regular","Bold"]],["PT Sans",["Regular","Bold","Italic","Bold Italic"]],["PT Serif Caption",["Regular","Italic"]],["PT Serif",["Regular","Bold","Italic","Bold Italic"]],["Puralecka Narrow",["Regular","Bold"]],["Puritan",["Regular","Bold","Italic","BoldItalic"]],["Purple Purse",["Regular"]],["Quando",["Regular"]],["Quantico",["Regular","Bold","Italic","Bold Italic"]],["Quattrocento Roman",["Regular"]],["Quattrocento Sans",["Regular","Bold","Italic","Bold Italic"]],["Quattrocento",["Bold"]],["Questrial",["Regular"]],["Quicksand",["Light","Regular","Bold","LightItalic","Italic","BoldItalic"]],["Quintessential",["Regular"]],["Qwigley",["Regular"]],["Racing Sans One",["Regular"]],["Radley",["Regular","Italic"]],["Rajdhani",["Light","Regular","Medium","Semibold","Bold"]],["Raleway Dots ",["Regular"]],["Raleway",["Thin","ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold","Black","Thin Italic","ExtraLight Italic","Light Italic","Italic","Medium Italic","SemiBold Italic","Bold Italic","ExtraBold Italic","Black Italic"]],["Ramabhadra",["Regular"]],["Ramaraja",["Regular"]],["Rambla",["Regular","Bold","Italic","Bold Italic"]],["Rammetto One",["Regular"]],["Ranchers",["Regular"]],["Rancho",["Regular"]],["Ranga",["Regular","Bold"]],["Rationale",["Regular"]],["Ravi Prakash",["Regular"]],["Redacted Script",["Regular","Bold"]],["Redacted",["Regular"]],["Redressed",["Regular"]],["Reenie Beanie",["Regular"]],["Revalia",["Regular"]],["Ribeye Marrow",["Regular"]],["Ribeye",["Regular"]],["Righteous",["Regular"]],["Risque",["Regular"]],["Roboto Condensed",["Light","Regular","Bold","Light Italic","Italic","Bold Italic"]],["Roboto",["Thin","Light","Regular","Medium","Bold","Black","Thin Italic","Light Italic","Italic","Medium Italic","Bold Italic","Black Italic"]],["Roboto Slab",["Thin","Light","Regular","Bold"]],["Rochester",["Regular"]],["Rock Salt",["Regular"]],["Rokkitt",["Light","Regular","Bold"]],["Romanesco",["Regular"]],["Ropa Sans",["Regular","Italic"]],["Rosario",["Regular","Bold","Italic","Bold Italic"]],["Rosarivo",["Regular","Italic"]],["Rouge Script",["Regular"]],["Rozha One",["Regular"]],["Rubik Mono One",["Regular"]],["Rubik One",["Regular"]],["Ruda",["Regular","Bold","Black"]],["Rufina",["Regular","Bold"]],["Ruge Boogie",["Regular"]],["Ruluko",["Regular"]],["Rum Raisin",["Regular"]],["Ruslan Display",["Regular"]],["Russo One",["Regular"]],["Ruthie",["Regular"]],["Rye",["Regular"]],["Sacramento",["Regular"]],["Sail",["Regular"]],["Salsa",["Regular"]],["Sanchez",["Regular","Italic"]],["Sancreek",["Regular"]],["Sansation Light",["Light","Light Italic"]],["Sansation",["Regular","Bold","Italic","Bold Italic"]],["Sansita One",["Regular"]],["Sarabun",["Regular","Bold","Italic","Bold Italic"]],["Sarala",["Regular","Bold"]],["Sarina",["Regular"]],["Sarpanch",["Regular","Medium","SemiBold","Bold","ExtraBold","Black"]],["Satisfy",["Regular"]],["Scada",["Regular","Bold","Italic","Bold Italic"]],["Scheherazade",["Regular"]],["Schoolbell",["Regular"]],["Seaweed Script",["Regular"]],["Sedan SC",["Regular"]],["Sedan",["Regular","Italic"]],["SeoulHangang CB",["Regular"]],["SeoulHangang CBL",["Regular"]],["SeoulHangang CEB",["Regular"]],["SeoulHangang CL",["Regular"]],["SeoulHangang CM",["Regular"]],["SeoulHangang Smart B",["Regular"]],["SeoulNamsan CB",["Regular"]],["SeoulNamsan CBL",["Regular"]],["SeoulNamsan CEB",["Regular"]],["SeoulNamsan CL",["Regular"]],["SeoulNamsan CM",["Regular"]],["SeoulNamsan Smart B",["Regular"]],["Sevillana",["Regular"]],["Seymour One",["Book"]],["Shadows Into Light",["Regular"]],["Shadows Into Light Two",["Regular"]],["Shanti",["Regular"]],["Share",["Regular","Bold","Italic","Bold Italic"]],["Share Tech Mono",["Regular"]],["Share Tech",["Regular"]],["Shojumaru",["Regular"]],["Short Stack",["Regular"]],["Siemreap",["Regular"]],["Sigmar One",["Regular"]],["Signika Negative",["Light","Regular","Semibold","Bold"]],["Signika",["Light","Regular","Semibold","Bold"]],["Simonetta",["Regular","Black","Italic","Black Italic"]],["Sintony",["Regular","Bold"]],["SirinStencil",["Regular"]],["Six Caps",["Regular"]],["Skranji",["Regular","Bold"]],["Slabo 13px",["Regular"]],["Slabo 27px",["Regular"]],["Slackey",["Regular"]],["Smokum",["Regular"]],["Smythe",["Regular"]],["Sniglet",["Regular","ExtraBold"]],["Snippet",["Regular"]],["Snowburst One",["Regular"]],["Sofadi One",["Regular"]],["Sofia",["Regular"]],["Sonsie One",["Regular"]],["Sorts Mill Goudy",["Regular","Italic"]],["Souliyo Unicode",["Regular"]],["Source Code Pro",["ExtraLight","Light","Regular","Medium","Semibold","Bold","Black"]],["Source Sans Pro",["ExtraLight","Light","Regular","Semibold","Bold","Black","ExtraLight Italic","Light Italic","Italic","Semibold Italic","Bold Italic","Black Italic"]],["Source Serif Pro",["Regular","Semibold","Bold"]],["Special Elite",["Regular"]],["Spicy Rice",["Regular"]],["Spinnaker",["Regular"]],["Spirax",["Regular"]],["Squada One",["Regular"]],["Sree Krushnadevaraya",["Regular"]],["Stalemate",["Regular"]],["Stalinist One",["Regular"]],["Stalin One",["Regular"]],["Standard Symbols L",["Regular"]],["Stardos Stencil",["Regular","Bold"]],["Stint Ultra Condensed",["Regular"]],["Stint Ultra Expanded",["Regular"]],["Stoke",["Light","Regular"]],["Strait",["Regular"]],["Strong",["Regular"]],["Sue Ellen Francisco ",["Regular"]],["Sunshiney",["Regular"]],["Supermercado",["Regular"]],["Suranna",["Regular"]],["Suravaram",["Regular"]],["Swanky and Moo Moo",["Regular"]],["Syncopate",["Regular","Bold"]],["Tangerine",["Regular","Bold"]],["Taprom",["Regular"]],["Tauri",["Regular"]],["Teko",["Light","Regular","Medium","Semibold","Bold"]],["Telex",["Regular"]],["Tenali Ramakrishna",["Regular"]],["Tenor Sans",["Regular"]],["Terminal Dosis",["ExtraLight","Light","Regular","Medium","SemiBold","Bold","ExtraBold"]],["Text Me One",["Regular"]],["Thabit",["Regular","Bold","Oblique","Bold Oblique"]],["TharLon",["Regular"]],["The Girl Next Door",["Regular"]],["Tienne",["Regular","Bold","Heavy"]],["Tillana",["Regular","Medium","SemiBold","Bold","ExtraBold"]],["Timmana",["Regular"]],["Tinos",["Regular","Bold","Italic","Bold Italic"]],["Titan One",["Regular"]],["Titillium Web",["Thin","Light","Regular","SemiBold","Bold","Black","Thin Italic","Light Italic","Italic","SemiBold Italic","Bold Italic"]],["Trade Winds",["Regular"]],["Trocchi",["Regular"]],["Trochut",["Regular","Bold","Italic"]],["Trykker",["Regular"]],["Tuffy",["Regular","Bold","Italic","BoldItalic"]],["Tulpen One",["Regular"]],["Tulpen",["Light"]],["Ubuntu Condensed",["Regular"]],["Ubuntu Monospaced",["Regular","Bold","Italic","Bold Italic"]],["Ubuntu Mono",["Regular","Bold","Italic","Bold Italic"]],["Ubuntu",["Light","Regular","Medium","Bold","Light Italic","Italic","Medium Italic","Bold Italic"]],["Ultra",["Regular"]],["Uncial Antiqua",["Regular"]],["Underdog",["Regular"]],["Unica One",["Regular"]],["UnifrakturCook",["Bold"]],["UnifrakturMaguntia",["Book"]],["Unkempt",["Regular","Bold"]],["Unlock",["Regular"]],["Unna",["Regular"]],["URW Bookman L",["Light","Demi Bold","Light Italic","Demi Bold Italic"]],["URW Chancery L",["Medium Italic"]],["URW Gothic L",["Book","Demi","Book Oblique","Demi Oblique"]],["URW Palladio L",["Roman","Bold","Italic","Bold Italic"]],["Vampiro One",["Regular"]],["Varela Round",["Regular"]],["Varela",["Regular"]],["Vast Shadow",["Regular"]],["Vesper Libre",["Regular","Medium","Bold","Heavy"]],["Vibur",["Medium"]],["Vidaloka ",["Regular"]],["Viga",["Regular"]],["Voces",["Regular"]],["Volkhov",["Regular","Bold","Italic","Bold Italic"]],["Vollkorn",["Regular","Bold","Italic","Bold Italic"]],["Voltaire",["Regular"]],["VT323",["Regular"]],["Waiting for the Sunrise",["Regular"]],["Wallpoet",["Regular"]],["Walter Turncoat",["Regular"]],["Warnes",["Regular"]],["Wellfleet",["Regular"]],["Wendy One",["Regular"]],["Wire One",["Regular"]],["Yanone Kaffeesatz",["Extra Light","Light","Regular","Bold"]],["Yellowtail",["Regular"]],["Yeseva One",["Regular"]],["Yesteryear",["Regular"]],["Zeyada",["Regular"]]];
font_styles=fonts[search([font_name],fonts,1)[0]][1];
font_styles_index=get_first_value_in_list(search(["Bold","Extra Bold","ExtraBold","Extrabold","Bold Condensed","Medium","Semi Bold","SemiBold","Semibold","Demi","Demi Bold","DemiBold","Regular","Book"],font_styles),0);
default_font_style=font_styles_index!=undef?font_styles[font_styles_index]:"Auto";
fonts_with_style=[["", font_name, default_font_style],["ABeeZee (Regular)", "ABeeZee", "Regular"],["ABeeZee (Italic)", "ABeeZee", "Italic"],["Abel (Regular)", "Abel", "Regular"],["Abril Fatface (Regular)", "Abril Fatface", "Regular"],["Aclonica (Regular)", "Aclonica", "Regular"],["Acme (Regular)", "Acme", "Regular"],["Actor (Regular)", "Actor", "Regular"],["Adamina (Regular)", "Adamina", "Regular"],["Advent Pro (Thin)", "Advent Pro", "Thin"],["Advent Pro (ExtraLight)", "Advent Pro", "ExtraLight"],["Advent Pro (Light)", "Advent Pro", "Light"],["Advent Pro (Regular)", "Advent Pro", "Regular"],["Advent Pro (Medium)", "Advent Pro", "Medium"],["Advent Pro (SemiBold)", "Advent Pro", "SemiBold"],["Advent Pro (Bold)", "Advent Pro", "Bold"],["Aguafina Script (Regular)", "Aguafina Script", "Regular"],["Akronim (Regular)", "Akronim", "Regular"],["Aksara Bali Galang (Regular)", "Aksara Bali Galang", "Regular"],["Aladin (Regular)", "Aladin", "Regular"],["Aldrich (Regular)", "Aldrich", "Regular"],["Alef (Regular)", "Alef", "Regular"],["Alef (Bold)", "Alef", "Bold"],["Alegreya Sans (Thin)", "Alegreya Sans", "Thin"],["Alegreya Sans (Light)", "Alegreya Sans", "Light"],["Alegreya Sans (Regular)", "Alegreya Sans", "Regular"],["Alegreya Sans (Medium)", "Alegreya Sans", "Medium"],["Alegreya Sans (Bold)", "Alegreya Sans", "Bold"],["Alegreya Sans (ExtraBold)", "Alegreya Sans", "ExtraBold"],["Alegreya Sans (Black)", "Alegreya Sans", "Black"],["Alegreya Sans (Thin Italic)", "Alegreya Sans", "Thin Italic"],["Alegreya Sans (Light Italic)", "Alegreya Sans", "Light Italic"],["Alegreya Sans (Italic)", "Alegreya Sans", "Italic"],["Alegreya Sans (Medium Italic)", "Alegreya Sans", "Medium Italic"],["Alegreya Sans (Bold Italic)", "Alegreya Sans", "Bold Italic"],["Alegreya Sans (ExtraBold Italic)", "Alegreya Sans", "ExtraBold Italic"],["Alegreya Sans (Black Italic)", "Alegreya Sans", "Black Italic"],["Alegreya Sans SC (Thin)", "Alegreya Sans SC", "Thin"],["Alegreya Sans SC (Light)", "Alegreya Sans SC", "Light"],["Alegreya Sans SC (Regular)", "Alegreya Sans SC", "Regular"],["Alegreya Sans SC (Medium)", "Alegreya Sans SC", "Medium"],["Alegreya Sans SC (Bold)", "Alegreya Sans SC", "Bold"],["Alegreya Sans SC (ExtraBold)", "Alegreya Sans SC", "ExtraBold"],["Alegreya Sans SC (Black)", "Alegreya Sans SC", "Black"],["Alegreya Sans SC (Thin Italic)", "Alegreya Sans SC", "Thin Italic"],["Alegreya Sans SC (Light Italic)", "Alegreya Sans SC", "Light Italic"],["Alegreya Sans SC (Italic)", "Alegreya Sans SC", "Italic"],["Alegreya Sans SC (Medium Italic)", "Alegreya Sans SC", "Medium Italic"],["Alegreya Sans SC (Bold Italic)", "Alegreya Sans SC", "Bold Italic"],["Alegreya Sans SC (ExtraBold Italic)", "Alegreya Sans SC", "ExtraBold Italic"],["Alegreya Sans SC (Black Italic)", "Alegreya Sans SC", "Black Italic"],["Alegreya SC (Regular)", "Alegreya SC", "Regular"],["Alegreya SC (Bold)", "Alegreya SC", "Bold"],["Alegreya SC (Black)", "Alegreya SC", "Black"],["Alegreya SC (Italic)", "Alegreya SC", "Italic"],["Alegreya SC (Bold Italic)", "Alegreya SC", "Bold Italic"],["Alegreya SC (Black Italic)", "Alegreya SC", "Black Italic"],["Alegreya (Regular)", "Alegreya", "Regular"],["Alegreya (Bold)", "Alegreya", "Bold"],["Alegreya (Black)", "Alegreya", "Black"],["Alegreya (Italic)", "Alegreya", "Italic"],["Alegreya (Bold Italic)", "Alegreya", "Bold Italic"],["Alegreya (Black Italic)", "Alegreya", "Black Italic"],["Alex Brush (Regular)", "Alex Brush", "Regular"],["Alfa Slab One (Regular)", "Alfa Slab One", "Regular"],["Alice (Regular)", "Alice", "Regular"],["Alike Angular (Regular)", "Alike Angular", "Regular"],["Alike (Regular)", "Alike", "Regular"],["Allan (Regular)", "Allan", "Regular"],["Allan (Bold)", "Allan", "Bold"],["Allerta Stencil (Regular)", "Allerta Stencil", "Regular"],["Allerta (Stencil)", "Allerta", "Stencil"],["Allerta (Regular)", "Allerta", "Regular"],["Allerta (Medium)", "Allerta", "Medium"],["Allura (Regular)", "Allura", "Regular"],["Almendra Display (Regular)", "Almendra Display", "Regular"],["Almendra SC (Regular)", "Almendra SC", "Regular"],["Almendra SC (Bold)", "Almendra SC", "Bold"],["Almendra SC (Italic)", "Almendra SC", "Italic"],["Almendra SC (Bold Italic)", "Almendra SC", "Bold Italic"],["Almendra (Regular)", "Almendra", "Regular"],["Almendra (Bold)", "Almendra", "Bold"],["Almendra (Italic)", "Almendra", "Italic"],["Almendra (Bold Italic)", "Almendra", "Bold Italic"],["Amarante (Regular)", "Amarante", "Regular"],["Amaranth (Regular)", "Amaranth", "Regular"],["Amaranth (Bold)", "Amaranth", "Bold"],["Amaranth (Italic)", "Amaranth", "Italic"],["Amaranth (Bold Italic)", "Amaranth", "Bold Italic"],["Amatic SC (Regular)", "Amatic SC", "Regular"],["Amatic SC (Bold)", "Amatic SC", "Bold"],["Amethysta (Regular)", "Amethysta", "Regular"],["Amiri (Regular)", "Amiri", "Regular"],["Amiri (Bold)", "Amiri", "Bold"],["Amiri (Slanted)", "Amiri", "Slanted"],["Amiri (Bold Slanted)", "Amiri", "Bold Slanted"],["Anaheim (Regular)", "Anaheim", "Regular"],["Andada SC (Regular)", "Andada SC", "Regular"],["Andada SC (Bold)", "Andada SC", "Bold"],["Andada SC (Italic)", "Andada SC", "Italic"],["Andada SC (Bold Italic)", "Andada SC", "Bold Italic"],["Andada (Regular)", "Andada", "Regular"],["Andada (Bold)", "Andada", "Bold"],["Andada (Italic)", "Andada", "Italic"],["Andada (Bold Italic)", "Andada", "Bold Italic"],["Andika (Regular)", "Andika", "Regular"],["Angkor (Regular)", "Angkor", "Regular"],["Annie Use Your Telescope (Regular)", "Annie Use Your Telescope", "Regular"],["Anonymous Pro (Regular)", "Anonymous Pro", "Regular"],["Anonymous Pro (Bold)", "Anonymous Pro", "Bold"],["Anonymous Pro (Italic)", "Anonymous Pro", "Italic"],["Anonymous Pro (Bold Italic)", "Anonymous Pro", "Bold Italic"],["Antic Didone (Regular)", "Antic Didone", "Regular"],["Antic Slab (Regular)", "Antic Slab", "Regular"],["Antic (Regular)", "Antic", "Regular"],["Antonio (Light)", "Antonio", "Light"],["Antonio (Regular)", "Antonio", "Regular"],["Antonio (Bold)", "Antonio", "Bold"],["Anton (Regular)", "Anton", "Regular"],["Arapey (Regular)", "Arapey", "Regular"],["Arapey (Italic)", "Arapey", "Italic"],["Arbutus Slab (Regular)", "Arbutus Slab", "Regular"],["Arbutus (Regular)", "Arbutus", "Regular"],["Architects Daughter (Regular)", "Architects Daughter", "Regular"],["Archivo Black (Regular)", "Archivo Black", "Regular"],["Archivo Narrow (Regular)", "Archivo Narrow", "Regular"],["Archivo Narrow (Bold)", "Archivo Narrow", "Bold"],["Archivo Narrow (Italic)", "Archivo Narrow", "Italic"],["Archivo Narrow (Bold Italic)", "Archivo Narrow", "Bold Italic"],["Arimo (Regular)", "Arimo", "Regular"],["Arimo (Bold)", "Arimo", "Bold"],["Arimo (Italic)", "Arimo", "Italic"],["Arimo (Bold Italic)", "Arimo", "Bold Italic"],["Arizonia (Regular)", "Arizonia", "Regular"],["Armata (Regular)", "Armata", "Regular"],["Artifika (Medium)", "Artifika", "Medium"],["Arvo (Regular)", "Arvo", "Regular"],["Arvo (Bold)", "Arvo", "Bold"],["Arvo (Italic)", "Arvo", "Italic"],["Arvo (Bold Italic)", "Arvo", "Bold Italic"],["Asap (Regular)", "Asap", "Regular"],["Asap (Bold)", "Asap", "Bold"],["Asap (Italic)", "Asap", "Italic"],["Asap (Bold Italic)", "Asap", "Bold Italic"],["Asset (Regular)", "Asset", "Regular"],["Astloch (Regular)", "Astloch", "Regular"],["Astloch (Bold)", "Astloch", "Bold"],["Asul (Regular)", "Asul", "Regular"],["Asul (Bold)", "Asul", "Bold"],["Atomic Age (Regular)", "Atomic Age", "Regular"],["Aubrey (Regular)", "Aubrey", "Regular"],["Audiowide (Regular)", "Audiowide", "Regular"],["Autour One (Regular)", "Autour One", "Regular"],["Average Sans (Regular)", "Average Sans", "Regular"],["Average (Regular)", "Average", "Regular"],["Averia Gruesa Libre (Regular)", "Averia Gruesa Libre", "Regular"],["Averia Libre (Light)", "Averia Libre", "Light"],["Averia Libre (Regular)", "Averia Libre", "Regular"],["Averia Libre (Bold)", "Averia Libre", "Bold"],["Averia Libre (Light Italic)", "Averia Libre", "Light Italic"],["Averia Libre (Italic)", "Averia Libre", "Italic"],["Averia Libre (Bold Italic)", "Averia Libre", "Bold Italic"],["Averia Sans Libre (Light)", "Averia Sans Libre", "Light"],["Averia Sans Libre (Regular)", "Averia Sans Libre", "Regular"],["Averia Sans Libre (Bold)", "Averia Sans Libre", "Bold"],["Averia Sans Libre (Light Italic)", "Averia Sans Libre", "Light Italic"],["Averia Sans Libre (Italic)", "Averia Sans Libre", "Italic"],["Averia Sans Libre (Bold Italic)", "Averia Sans Libre", "Bold Italic"],["Averia Serif Libre (Light)", "Averia Serif Libre", "Light"],["Averia Serif Libre (Regular)", "Averia Serif Libre", "Regular"],["Averia Serif Libre (Bold)", "Averia Serif Libre", "Bold"],["Averia Serif Libre (Light Italic)", "Averia Serif Libre", "Light Italic"],["Averia Serif Libre (Italic)", "Averia Serif Libre", "Italic"],["Averia Serif Libre (Bold Italic)", "Averia Serif Libre", "Bold Italic"],["Bad Script (Regular)", "Bad Script", "Regular"],["Bali Galang (Regular)", "Bali Galang", "Regular"],["Balthazar (Regular)", "Balthazar", "Regular"],["Bangers (Regular)", "Bangers", "Regular"],["Basic (Regular)", "Basic", "Regular"],["Battambang (Regular)", "Battambang", "Regular"],["Battambang (Bold)", "Battambang", "Bold"],["Baumans (Regular)", "Baumans", "Regular"],["Bayon (Regular)", "Bayon", "Regular"],["Belgrano (Regular)", "Belgrano", "Regular"],["Belleza (Regular)", "Belleza", "Regular"],["BenchNine (Light)", "BenchNine", "Light"],["BenchNine (Regular)", "BenchNine", "Regular"],["BenchNine (Bold)", "BenchNine", "Bold"],["Bentham (Regular)", "Bentham", "Regular"],["Berkshire Swash (Regular)", "Berkshire Swash", "Regular"],["Bevan (Regular)", "Bevan", "Regular"],["Bhavuka (Regular)", "Bhavuka", "Regular"],["Bigelow Rules (Regular)", "Bigelow Rules", "Regular"],["Bigshot One (Regular)", "Bigshot One", "Regular"],["Bilbo (Regular)", "Bilbo", "Regular"],["Bilbo Swash Caps (Regular)", "Bilbo Swash Caps", "Regular"],["Bitter (Regular)", "Bitter", "Regular"],["Bitter (Bold)", "Bitter", "Bold"],["Bitter (Italic)", "Bitter", "Italic"],["Black Ops One (Regular)", "Black Ops One", "Regular"],["BM HANNA_TTF (Regular)", "BM HANNA_TTF", "Regular"],["Bokor (Regular)", "Bokor", "Regular"],["Bonbon (Regular)", "Bonbon", "Regular"],["Boogaloo (Regular)", "Boogaloo", "Regular"],["Bowlby One SC (Regular)", "Bowlby One SC", "Regular"],["Bowlby One (Regular)", "Bowlby One", "Regular"],["Brawler (Regular)", "Brawler", "Regular"],["Bree Serif (Regular)", "Bree Serif", "Regular"],["Bruno Ace SC (Regular)", "Bruno Ace SC", "Regular"],["Bruno Ace (Regular)", "Bruno Ace", "Regular"],["Bubblegum Sans (Regular)", "Bubblegum Sans", "Regular"],["Bubbler One (Regular)", "Bubbler One", "Regular"],["Buda (light)", "Buda", "light"],["Buenard (Regular)", "Buenard", "Regular"],["Buenard (Bold)", "Buenard", "Bold"],["Butcherman Caps (Regular)", "Butcherman Caps", "Regular"],["Butcherman (Regular)", "Butcherman", "Regular"],["Butterfly Kids (Regular)", "Butterfly Kids", "Regular"],["Cabin Condensed (Regular)", "Cabin Condensed", "Regular"],["Cabin Condensed (Medium)", "Cabin Condensed", "Medium"],["Cabin Condensed (SemiBold)", "Cabin Condensed", "SemiBold"],["Cabin Condensed (Bold)", "Cabin Condensed", "Bold"],["CabinSketch (Bold)", "CabinSketch", "Bold"],["Cabin Sketch (Regular)", "Cabin Sketch", "Regular"],["Cabin (Regular)", "Cabin", "Regular"],["Cabin (Medium)", "Cabin", "Medium"],["Cabin (SemiBold)", "Cabin", "SemiBold"],["Cabin (Bold)", "Cabin", "Bold"],["Cabin (Italic)", "Cabin", "Italic"],["Cabin (Medium Italic)", "Cabin", "Medium Italic"],["Cabin (SemiBold Italic)", "Cabin", "SemiBold Italic"],["Cabin (Bold Italic)", "Cabin", "Bold Italic"],["Caesar Dressing (Regular)", "Caesar Dressing", "Regular"],["Cagliostro (Regular)", "Cagliostro", "Regular"],["Calligraffitti (Regular)", "Calligraffitti", "Regular"],["Cambay (Regular)", "Cambay", "Regular"],["Cambay (Bold)", "Cambay", "Bold"],["Cambay (Italic)", "Cambay", "Italic"],["Cambay (Bold Italic)", "Cambay", "Bold Italic"],["Cambo (Regular)", "Cambo", "Regular"],["Candal (Regular)", "Candal", "Regular"],["Cantarell (Regular)", "Cantarell", "Regular"],["Cantarell (Bold)", "Cantarell", "Bold"],["Cantarell (Oblique)", "Cantarell", "Oblique"],["Cantarell (BoldOblique)", "Cantarell", "BoldOblique"],["Cantata One (Regular)", "Cantata One", "Regular"],["CantoraOne (Regular)", "CantoraOne", "Regular"],["Capriola (Regular)", "Capriola", "Regular"],["Cardo (Regular)", "Cardo", "Regular"],["Cardo (Bold)", "Cardo", "Bold"],["Cardo (Italic)", "Cardo", "Italic"],["Carme (Regular)", "Carme", "Regular"],["CarroisGothic Caps (Regular)", "CarroisGothic Caps", "Regular"],["CarroisGothic (Regular)", "CarroisGothic", "Regular"],["Carter One (Regular)", "Carter One", "Regular"],["Caudex (Regular)", "Caudex", "Regular"],["Caudex (Bold)", "Caudex", "Bold"],["Caudex (Italic)", "Caudex", "Italic"],["Caudex (Bold Italic)", "Caudex", "Bold Italic"],["Cedarville Cursive (Regular)", "Cedarville Cursive", "Regular"],["Century Schoolbook L (Roman)", "Century Schoolbook L", "Roman"],["Century Schoolbook L (Bold)", "Century Schoolbook L", "Bold"],["Century Schoolbook L (Italic)", "Century Schoolbook L", "Italic"],["Century Schoolbook L (Bold Italic)", "Century Schoolbook L", "Bold Italic"],["Ceviche One (Regular)", "Ceviche One", "Regular"],["Changa One (Regular)", "Changa One", "Regular"],["Changa One (Italic)", "Changa One", "Italic"],["Changa (Regular)", "Changa", "Regular"],["Chango (Regular)", "Chango", "Regular"],["Chau Philomene One (Regular)", "Chau Philomene One", "Regular"],["Chau Philomene One (Italic)", "Chau Philomene One", "Italic"],["Chela One (Regular)", "Chela One", "Regular"],["Chelsea Market (Regular)", "Chelsea Market", "Regular"],["Chenla (Regular)", "Chenla", "Regular"],["Cherry Cream Soda (Regular)", "Cherry Cream Soda", "Regular"],["Cherry Swash (Regular)", "Cherry Swash", "Regular"],["Cherry Swash (Bold)", "Cherry Swash", "Bold"],["Chewy (Regular)", "Chewy", "Regular"],["Chicle (Regular)", "Chicle", "Regular"],["Chivo (Regular)", "Chivo", "Regular"],["Chivo (Black)", "Chivo", "Black"],["Chivo (Italic)", "Chivo", "Italic"],["Chivo (Black Italic)", "Chivo", "Black Italic"],["Cinzel (Regular)", "Cinzel", "Regular"],["Cinzel (Bold)", "Cinzel", "Bold"],["Cinzel (Black)", "Cinzel", "Black"],["Cinzel Decorative (Regular)", "Cinzel Decorative", "Regular"],["Cinzel Decorative (Bold)", "Cinzel Decorative", "Bold"],["Cinzel Decorative (Black)", "Cinzel Decorative", "Black"],["Clara (Regular)", "Clara", "Regular"],["Clicker Script (Regular)", "Clicker Script", "Regular"],["Coda Caption (Heavy)", "Coda Caption", "Heavy"],["Coda (Regular)", "Coda", "Regular"],["Coda (Heavy)", "Coda", "Heavy"],["Codystar (Light)", "Codystar", "Light"],["Codystar (Regular)", "Codystar", "Regular"],["Combo (Regular)", "Combo", "Regular"],["Comfortaa (Light)", "Comfortaa", "Light"],["Comfortaa (Regular)", "Comfortaa", "Regular"],["Comfortaa (Bold)", "Comfortaa", "Bold"],["Coming Soon (Regular)", "Coming Soon", "Regular"],["Concert One (Regular)", "Concert One", "Regular"],["Condiment (Regular)", "Condiment", "Regular"],["Content (Regular)", "Content", "Regular"],["Content (Bold)", "Content", "Bold"],["Contrail One (Regular)", "Contrail One", "Regular"],["Convergence (Regular)", "Convergence", "Regular"],["Cookie (Regular)", "Cookie", "Regular"],["Copse (Regular)", "Copse", "Regular"],["Corben (Regular)", "Corben", "Regular"],["Corben (Bold)", "Corben", "Bold"],["Courgette (Regular)", "Courgette", "Regular"],["Cousine (Regular)", "Cousine", "Regular"],["Cousine (Bold)", "Cousine", "Bold"],["Cousine (Italic)", "Cousine", "Italic"],["Cousine (Bold Italic)", "Cousine", "Bold Italic"],["Coustard (Regular)", "Coustard", "Regular"],["Coustard (Bold)", "Coustard", "Bold"],["Covered By Your Grace (Regular)", "Covered By Your Grace", "Regular"],["Crafty Girls (Regular)", "Crafty Girls", "Regular"],["Creepster Caps (Regular)", "Creepster Caps", "Regular"],["Creepster (Regular)", "Creepster", "Regular"],["Crete Round (Regular)", "Crete Round", "Regular"],["Crete Round (Italic)", "Crete Round", "Italic"],["Crimson Text (Roman)", "Crimson Text", "Roman"],["Crimson Text (Semibold)", "Crimson Text", "Semibold"],["Crimson Text (Bold)", "Crimson Text", "Bold"],["Crimson Text (Italic)", "Crimson Text", "Italic"],["Crimson Text (SemiboldItalic)", "Crimson Text", "SemiboldItalic"],["Crimson Text (BoldItalic)", "Crimson Text", "BoldItalic"],["Croissant One (Regular)", "Croissant One", "Regular"],["Crushed (Regular)", "Crushed", "Regular"],["Cuprum (Regular)", "Cuprum", "Regular"],["Cuprum (Bold)", "Cuprum", "Bold"],["Cuprum (Italic)", "Cuprum", "Italic"],["Cuprum (Bold Italic)", "Cuprum", "Bold Italic"],["Cutive Mono (Regular)", "Cutive Mono", "Regular"],["Cutive (Regular)", "Cutive", "Regular"],["Damion (Regular)", "Damion", "Regular"],["Dancing Script (Regular)", "Dancing Script", "Regular"],["Dancing Script (Bold)", "Dancing Script", "Bold"],["Dangrek (Regular)", "Dangrek", "Regular"],["Dawning of a New Day (Regular)", "Dawning of a New Day", "Regular"],["Days One (Regular)", "Days One", "Regular"],["DejaVu Sans Mono (Book)", "DejaVu Sans Mono", "Book"],["DejaVu Sans Mono (Bold)", "DejaVu Sans Mono", "Bold"],["DejaVu Sans (Book)", "DejaVu Sans", "Book"],["DejaVu Sans (Bold)", "DejaVu Sans", "Bold"],["DejaVu Serif (Book)", "DejaVu Serif", "Book"],["DejaVu Serif (Bold)", "DejaVu Serif", "Bold"],["Dekko (Regular)", "Dekko", "Regular"],["Delius (Regular)", "Delius", "Regular"],["Delius Swash Caps (Regular)", "Delius Swash Caps", "Regular"],["Delius Unicase (Regular)", "Delius Unicase", "Regular"],["Delius Unicase (Bold)", "Delius Unicase", "Bold"],["Della Respira (Regular)", "Della Respira", "Regular"],["Denk One (Regular)", "Denk One", "Regular"],["Devonshire (Regular)", "Devonshire", "Regular"],["Dhurjati (Regular)", "Dhurjati", "Regular"],["Dhyana (Regular)", "Dhyana", "Regular"],["Dhyana (Bold)", "Dhyana", "Bold"],["Didact Gothic (Regular)", "Didact Gothic", "Regular"],["Dinah (Regular)", "Dinah", "Regular"],["DinahVAA (Bold)", "DinahVAA", "Bold"],["Dingbats (Regular)", "Dingbats", "Regular"],["Diplomata SC (Regular)", "Diplomata SC", "Regular"],["Diplomata (Regular)", "Diplomata", "Regular"],["Domine (Regular)", "Domine", "Regular"],["Domine (Bold)", "Domine", "Bold"],["Donegal One (Regular)", "Donegal One", "Regular"],["Doppio One (Regular)", "Doppio One", "Regular"],["Dorsa (Regular)", "Dorsa", "Regular"],["Dosis (ExtraLight)", "Dosis", "ExtraLight"],["Dosis (Light)", "Dosis", "Light"],["Dosis (Regular)", "Dosis", "Regular"],["Dosis (Medium)", "Dosis", "Medium"],["Dosis (SemiBold)", "Dosis", "SemiBold"],["Dosis (Bold)", "Dosis", "Bold"],["Dosis (ExtraBold)", "Dosis", "ExtraBold"],["Droid Arabic Kufi (Regular)", "Droid Arabic Kufi", "Regular"],["Droid Arabic Kufi (Bold)", "Droid Arabic Kufi", "Bold"],["Droid Arabic Naskh (Regular)", "Droid Arabic Naskh", "Regular"],["Droid Arabic Naskh (Bold)", "Droid Arabic Naskh", "Bold"],["Droid Sans Ethiopic (Regular)", "Droid Sans Ethiopic", "Regular"],["Droid Sans Ethiopic (Bold)", "Droid Sans Ethiopic", "Bold"],["Droid Sans Japanese (Regular)", "Droid Sans Japanese", "Regular"],["Droid Sans Mono (Regular)", "Droid Sans Mono", "Regular"],["Droid Sans (Regular)", "Droid Sans", "Regular"],["Droid Sans (Bold)", "Droid Sans", "Bold"],["Droid Sans Tamil (Regular)", "Droid Sans Tamil", "Regular"],["Droid Sans Tamil (Bold)", "Droid Sans Tamil", "Bold"],["Droid Sans Thai (Regular)", "Droid Sans Thai", "Regular"],["Droid Sans Thai (Bold)", "Droid Sans Thai", "Bold"],["Droid Serif (Regular)", "Droid Serif", "Regular"],["Droid Serif (Bold)", "Droid Serif", "Bold"],["Droid Serif (Italic)", "Droid Serif", "Italic"],["Droid Serif (Bold Italic)", "Droid Serif", "Bold Italic"],["DroidSerifThai (Bold)", "DroidSerifThai", "Bold"],["Droid Serif Thai (Regular)", "Droid Serif Thai", "Regular"],["Dr Sugiyama (Regular)", "Dr Sugiyama", "Regular"],["Duru Sans (Regular)", "Duru Sans", "Regular"],["Dynalight (Regular)", "Dynalight", "Regular"],["Eagle Lake (Regular)", "Eagle Lake", "Regular"],["Eater Caps (Regular)", "Eater Caps", "Regular"],["Eater (Regular)", "Eater", "Regular"],["EB Garamond (Regular)", "EB Garamond", "Regular"],["Economica (Regular)", "Economica", "Regular"],["Economica (Bold)", "Economica", "Bold"],["Economica (Italic)", "Economica", "Italic"],["Economica (Bold Italic)", "Economica", "Bold Italic"],["Eczar (Regular)", "Eczar", "Regular"],["Eczar (Medium)", "Eczar", "Medium"],["Eczar (Semibold)", "Eczar", "Semibold"],["Eczar (Bold)", "Eczar", "Bold"],["Eczar (Extrabold)", "Eczar", "Extrabold"],["Ek Mukta (ExtraLight)", "Ek Mukta", "ExtraLight"],["Ek Mukta (Light)", "Ek Mukta", "Light"],["Ek Mukta (Regular)", "Ek Mukta", "Regular"],["Ek Mukta (Medium)", "Ek Mukta", "Medium"],["Ek Mukta (SemiBold)", "Ek Mukta", "SemiBold"],["Ek Mukta (Bold)", "Ek Mukta", "Bold"],["Ek Mukta (ExtraBold)", "Ek Mukta", "ExtraBold"],["Electrolize (Regular)", "Electrolize", "Regular"],["Elsie (Regular)", "Elsie", "Regular"],["Elsie (Black)", "Elsie", "Black"],["Elsie Swash Caps (Regular)", "Elsie Swash Caps", "Regular"],["Elsie Swash Caps (Black)", "Elsie Swash Caps", "Black"],["Emblema One (Regular)", "Emblema One", "Regular"],["Emilys Candy (Regular)", "Emilys Candy", "Regular"],["Encode Sans Compressed (Thin)", "Encode Sans Compressed", "Thin"],["Encode Sans Compressed (ExtraLight)", "Encode Sans Compressed", "ExtraLight"],["Encode Sans Compressed (Light)", "Encode Sans Compressed", "Light"],["Encode Sans Compressed (Regular)", "Encode Sans Compressed", "Regular"],["Encode Sans Compressed (Medium)", "Encode Sans Compressed", "Medium"],["Encode Sans Compressed (SemiBold)", "Encode Sans Compressed", "SemiBold"],["Encode Sans Compressed (Bold)", "Encode Sans Compressed", "Bold"],["Encode Sans Compressed (ExtraBold)", "Encode Sans Compressed", "ExtraBold"],["Encode Sans Compressed (Black)", "Encode Sans Compressed", "Black"],["Encode Sans Condensed (Thin)", "Encode Sans Condensed", "Thin"],["Encode Sans Condensed (ExtraLight)", "Encode Sans Condensed", "ExtraLight"],["Encode Sans Condensed (Light)", "Encode Sans Condensed", "Light"],["Encode Sans Condensed (Regular)", "Encode Sans Condensed", "Regular"],["Encode Sans Condensed (Medium)", "Encode Sans Condensed", "Medium"],["Encode Sans Condensed (SemiBold)", "Encode Sans Condensed", "SemiBold"],["Encode Sans Condensed (Bold)", "Encode Sans Condensed", "Bold"],["Encode Sans Condensed (ExtraBold)", "Encode Sans Condensed", "ExtraBold"],["Encode Sans Condensed (Black)", "Encode Sans Condensed", "Black"],["Encode Sans Narrow (Thin)", "Encode Sans Narrow", "Thin"],["Encode Sans Narrow (ExtraLight)", "Encode Sans Narrow", "ExtraLight"],["Encode Sans Narrow (Light)", "Encode Sans Narrow", "Light"],["Encode Sans Narrow (Regular)", "Encode Sans Narrow", "Regular"],["Encode Sans Narrow (Medium)", "Encode Sans Narrow", "Medium"],["Encode Sans Narrow (SemiBold)", "Encode Sans Narrow", "SemiBold"],["Encode Sans Narrow (Bold)", "Encode Sans Narrow", "Bold"],["Encode Sans Narrow (ExtraBold)", "Encode Sans Narrow", "ExtraBold"],["Encode Sans Narrow (Black)", "Encode Sans Narrow", "Black"],["Encode Sans (Thin)", "Encode Sans", "Thin"],["Encode Sans (ExtraLight)", "Encode Sans", "ExtraLight"],["Encode Sans (Light)", "Encode Sans", "Light"],["Encode Sans (Regular)", "Encode Sans", "Regular"],["Encode Sans (Medium)", "Encode Sans", "Medium"],["Encode Sans (SemiBold)", "Encode Sans", "SemiBold"],["Encode Sans (Bold)", "Encode Sans", "Bold"],["Encode Sans (ExtraBold)", "Encode Sans", "ExtraBold"],["Encode Sans (Black)", "Encode Sans", "Black"],["Encode Sans Wide (Thin)", "Encode Sans Wide", "Thin"],["Encode Sans Wide (ExtraLight)", "Encode Sans Wide", "ExtraLight"],["Encode Sans Wide (Light)", "Encode Sans Wide", "Light"],["Encode Sans Wide (Regular)", "Encode Sans Wide", "Regular"],["Encode Sans Wide (Medium)", "Encode Sans Wide", "Medium"],["Encode Sans Wide (SemiBold)", "Encode Sans Wide", "SemiBold"],["Encode Sans Wide (Bold)", "Encode Sans Wide", "Bold"],["Encode Sans Wide (ExtraBold)", "Encode Sans Wide", "ExtraBold"],["Encode Sans Wide (Black)", "Encode Sans Wide", "Black"],["Engagement (Regular)", "Engagement", "Regular"],["Englebert (Regular)", "Englebert", "Regular"],["Enriqueta (Regular)", "Enriqueta", "Regular"],["Enriqueta (Bold)", "Enriqueta", "Bold"],["Erica One (Regular)", "Erica One", "Regular"],["Esteban (Regular)", "Esteban", "Regular"],["Euphoria Script (Regular)", "Euphoria Script", "Regular"],["Ewert (Regular)", "Ewert", "Regular"],["Exo 2 (Thin)", "Exo 2", "Thin"],["Exo 2 (Extra Light)", "Exo 2", "Extra Light"],["Exo 2 (Light)", "Exo 2", "Light"],["Exo 2 (Regular)", "Exo 2", "Regular"],["Exo 2 (Medium)", "Exo 2", "Medium"],["Exo 2 (Semi Bold)", "Exo 2", "Semi Bold"],["Exo 2 (Bold)", "Exo 2", "Bold"],["Exo 2 (Extra Bold)", "Exo 2", "Extra Bold"],["Exo 2 (Black)", "Exo 2", "Black"],["Exo 2 (Thin Italic)", "Exo 2", "Thin Italic"],["Exo 2 (Extra Light Italic)", "Exo 2", "Extra Light Italic"],["Exo 2 (Light Italic)", "Exo 2", "Light Italic"],["Exo 2 (Regular Italic)", "Exo 2", "Regular Italic"],["Exo 2 (Medium Italic)", "Exo 2", "Medium Italic"],["Exo 2 (Semi Bold Italic)", "Exo 2", "Semi Bold Italic"],["Exo 2 (Bold Italic)", "Exo 2", "Bold Italic"],["Exo 2 (Extra Bold Italic)", "Exo 2", "Extra Bold Italic"],["Exo 2 (Black Italic)", "Exo 2", "Black Italic"],["Exo (Thin)", "Exo", "Thin"],["Exo (ExtraLight)", "Exo", "ExtraLight"],["Exo (Light)", "Exo", "Light"],["Exo (Regular)", "Exo", "Regular"],["Exo (Medium)", "Exo", "Medium"],["Exo (DemiBold)", "Exo", "DemiBold"],["Exo (Bold)", "Exo", "Bold"],["Exo (ExtraBold)", "Exo", "ExtraBold"],["Exo (Black)", "Exo", "Black"],["Exo (ThinItalic)", "Exo", "ThinItalic"],["Exo (ExtraLightItalic)", "Exo", "ExtraLightItalic"],["Exo (LightItalic)", "Exo", "LightItalic"],["Exo (Italic)", "Exo", "Italic"],["Exo (MediumItalic)", "Exo", "MediumItalic"],["Exo (DemiBoldItalic)", "Exo", "DemiBoldItalic"],["Exo (BoldItalic)", "Exo", "BoldItalic"],["Exo (ExtraBoldItalic)", "Exo", "ExtraBoldItalic"],["Exo (BlackItalic)", "Exo", "BlackItalic"],["Expletus Sans (Regular)", "Expletus Sans", "Regular"],["Expletus Sans (Medium)", "Expletus Sans", "Medium"],["Expletus Sans (SemiBold)", "Expletus Sans", "SemiBold"],["Expletus Sans (Bold)", "Expletus Sans", "Bold"],["Expletus Sans (Italic)", "Expletus Sans", "Italic"],["Expletus Sans (Medium Italic)", "Expletus Sans", "Medium Italic"],["Expletus Sans (SemiBold Italic)", "Expletus Sans", "SemiBold Italic"],["Expletus Sans (Bold Italic)", "Expletus Sans", "Bold Italic"],["Fanwood Text (Regular)", "Fanwood Text", "Regular"],["Fanwood Text (Italic)", "Fanwood Text", "Italic"],["Fascinate Inline (Regular)", "Fascinate Inline", "Regular"],["Fascinate (Regular)", "Fascinate", "Regular"],["Faster One (Regular)", "Faster One", "Regular"],["Fasthand (Regular)", "Fasthand", "Regular"],["Fauna One (Regular)", "Fauna One", "Regular"],["Federant (Medium)", "Federant", "Medium"],["Federo (Regular)", "Federo", "Regular"],["Felipa (Regular)", "Felipa", "Regular"],["Fenix (Regular)", "Fenix", "Regular"],["FingerPaint (Regular)", "FingerPaint", "Regular"],["Fira Mono (Regular)", "Fira Mono", "Regular"],["Fira Mono (Medium)", "Fira Mono", "Medium"],["Fira Mono (Bold)", "Fira Mono", "Bold"],["Fira Sans (Light)", "Fira Sans", "Light"],["Fira Sans (Regular)", "Fira Sans", "Regular"],["Fira Sans (Medium)", "Fira Sans", "Medium"],["Fira Sans (Bold)", "Fira Sans", "Bold"],["Fira Sans (Light Italic)", "Fira Sans", "Light Italic"],["Fira Sans (Italic)", "Fira Sans", "Italic"],["Fira Sans (Medium Italic)", "Fira Sans", "Medium Italic"],["Fira Sans (Bold Italic)", "Fira Sans", "Bold Italic"],["Fjalla One (Regular)", "Fjalla One", "Regular"],["Fjord (One)", "Fjord", "One"],["Flamenco (Light)", "Flamenco", "Light"],["Flamenco (Regular)", "Flamenco", "Regular"],["Flavors (Regular)", "Flavors", "Regular"],["Fondamento (Regular)", "Fondamento", "Regular"],["Fondamento (Italic)", "Fondamento", "Italic"],["Fontdiner Swanky (Regular)", "Fontdiner Swanky", "Regular"],["Forum (Regular)", "Forum", "Regular"],["Francois One (Regular)", "Francois One", "Regular"],["Freckle Face (Regular)", "Freckle Face", "Regular"],["Fredericka the Great (Regular)", "Fredericka the Great", "Regular"],["Fredoka One (Regular)", "Fredoka One", "Regular"],["Freehand (Regular)", "Freehand", "Regular"],["Fresca (Regular)", "Fresca", "Regular"],["Frijole (Regular)", "Frijole", "Regular"],["Fruktur (Regular)", "Fruktur", "Regular"],["Fugaz One (Regular)", "Fugaz One", "Regular"],["Gabriela (Regular)", "Gabriela", "Regular"],["Gafata (Regular)", "Gafata", "Regular"],["Galdeano (Regular)", "Galdeano", "Regular"],["Galindo (Regular)", "Galindo", "Regular"],["Gentium Basic (Regular)", "Gentium Basic", "Regular"],["Gentium Basic (Bold)", "Gentium Basic", "Bold"],["Gentium Basic (Italic)", "Gentium Basic", "Italic"],["Gentium Basic (Bold Italic)", "Gentium Basic", "Bold Italic"],["Gentium Book Basic (Regular)", "Gentium Book Basic", "Regular"],["Gentium Book Basic (Bold)", "Gentium Book Basic", "Bold"],["Gentium Book Basic (Italic)", "Gentium Book Basic", "Italic"],["Gentium Book Basic (Bold Italic)", "Gentium Book Basic", "Bold Italic"],["Geostar Fill (Regular)", "Geostar Fill", "Regular"],["Geostar (Regular)", "Geostar", "Regular"],["Geo (Regular)", "Geo", "Regular"],["Geo (Oblique)", "Geo", "Oblique"],["Germania One (Regular)", "Germania One", "Regular"],["GFS Didot (Regular)", "GFS Didot", "Regular"],["GFS Didot (Bold)", "GFS Didot", "Bold"],["GFS Didot (Italic)", "GFS Didot", "Italic"],["GFS Didot (Bold Italic)", "GFS Didot", "Bold Italic"],["GFS Neohellenic (Regular)", "GFS Neohellenic", "Regular"],["GFS Neohellenic (Bold)", "GFS Neohellenic", "Bold"],["GFS Neohellenic (Italic)", "GFS Neohellenic", "Italic"],["GFS Neohellenic (Bold Italic)", "GFS Neohellenic", "Bold Italic"],["Gidugu (Regular)", "Gidugu", "Regular"],["Gilda Display (Regular)", "Gilda Display", "Regular"],["Give You Glory (Regular)", "Give You Glory", "Regular"],["Glass Antiqua (Regular)", "Glass Antiqua", "Regular"],["Glegoo (Regular)", "Glegoo", "Regular"],["Glegoo (Bold)", "Glegoo", "Bold"],["Gloria Hallelujah (Regular)", "Gloria Hallelujah", "Regular"],["Goblin One (Regular)", "Goblin One", "Regular"],["Gochi Hand (Regular)", "Gochi Hand", "Regular"],["Gorditas (Regular)", "Gorditas", "Regular"],["Gorditas (Bold)", "Gorditas", "Bold"],["Goudy Bookletter 1911 (Regular)", "Goudy Bookletter 1911", "Regular"],["Graduate (Regular)", "Graduate", "Regular"],["Grand Hotel (Regular)", "Grand Hotel", "Regular"],["Gravitas One (Regular)", "Gravitas One", "Regular"],["Great Vibes (Regular)", "Great Vibes", "Regular"],["Griffy (Regular)", "Griffy", "Regular"],["Gruppo (Regular)", "Gruppo", "Regular"],["Gudea (Regular)", "Gudea", "Regular"],["Gudea (Bold)", "Gudea", "Bold"],["Gudea (Italic)", "Gudea", "Italic"],["Gurajada (Regular)", "Gurajada", "Regular"],["Habibi (Regular)", "Habibi", "Regular"],["Halant (Light)", "Halant", "Light"],["Halant (Regular)", "Halant", "Regular"],["Halant (Medium)", "Halant", "Medium"],["Halant (Semibold)", "Halant", "Semibold"],["Halant (Bold)", "Halant", "Bold"],["HammersmithOne (Regular)", "HammersmithOne", "Regular"],["Hanalei Fill (Regular)", "Hanalei Fill", "Regular"],["Hanalei (Regular)", "Hanalei", "Regular"],["Handlee (Regular)", "Handlee", "Regular"],["Hanuman (Regular)", "Hanuman", "Regular"],["Hanuman (Bold)", "Hanuman", "Bold"],["Happy Monkey (Regular)", "Happy Monkey", "Regular"],["HeadlandOne (Regular)", "HeadlandOne", "Regular"],["Henny Penny (Regular)", "Henny Penny", "Regular"],["Hermeneus One (Regular)", "Hermeneus One", "Regular"],["Herr Von Muellerhoff (Regular)", "Herr Von Muellerhoff", "Regular"],["Hind (Light)", "Hind", "Light"],["Hind (Regular)", "Hind", "Regular"],["Hind (Medium)", "Hind", "Medium"],["Hind (Semibold)", "Hind", "Semibold"],["Hind (Bold)", "Hind", "Bold"],["HintTest (Regular)", "HintTest", "Regular"],["Holtwood One SC (Regular)", "Holtwood One SC", "Regular"],["Homemade Apple (Regular)", "Homemade Apple", "Regular"],["Homenaje (Regular)", "Homenaje", "Regular"],["Iceberg (Regular)", "Iceberg", "Regular"],["Iceland (Regular)", "Iceland", "Regular"],["IM FELL Double Pica SC (Regular)", "IM FELL Double Pica SC", "Regular"],["IM FELL Double Pica (Regular)", "IM FELL Double Pica", "Regular"],["IM FELL Double Pica (Italic)", "IM FELL Double Pica", "Italic"],["IM FELL DW Pica SC (Regular)", "IM FELL DW Pica SC", "Regular"],["IM FELL DW Pica (Regular)", "IM FELL DW Pica", "Regular"],["IM FELL DW Pica (Italic)", "IM FELL DW Pica", "Italic"],["IM FELL English SC (Regular)", "IM FELL English SC", "Regular"],["IM FELL English (Regular)", "IM FELL English", "Regular"],["IM FELL English (Italic)", "IM FELL English", "Italic"],["IM FELL French Canon SC (Regular)", "IM FELL French Canon SC", "Regular"],["IM FELL French Canon (Regular)", "IM FELL French Canon", "Regular"],["IM FELL French Canon (Italic)", "IM FELL French Canon", "Italic"],["IM FELL Great Primer SC (Regular)", "IM FELL Great Primer SC", "Regular"],["IM FELL Great Primer (Regular)", "IM FELL Great Primer", "Regular"],["IM FELL Great Primer (Italic)", "IM FELL Great Primer", "Italic"],["Imprima (Regular)", "Imprima", "Regular"],["Inconsolata (Regular)", "Inconsolata", "Regular"],["Inconsolata (Bold)", "Inconsolata", "Bold"],["Inder (Regular)", "Inder", "Regular"],["Indie Flower (Regular)", "Indie Flower", "Regular"],["Inika (Regular)", "Inika", "Regular"],["Inika (Bold)", "Inika", "Bold"],["InknutAntiqua (Regular)", "InknutAntiqua", "Regular"],["InknutAntiqua (Bold)", "InknutAntiqua", "Bold"],["Irish Grover (Regular)", "Irish Grover", "Regular"],["Irish Growler (Regular)", "Irish Growler", "Regular"],["Istok Web (Regular)", "Istok Web", "Regular"],["Istok Web (Bold)", "Istok Web", "Bold"],["Istok Web (Italic)", "Istok Web", "Italic"],["Istok Web (BoldItalic)", "Istok Web", "BoldItalic"],["Italiana (Regular)", "Italiana", "Regular"],["Italianno (Regular)", "Italianno", "Regular"],["Jacques Francois Shadow (Regular)", "Jacques Francois Shadow", "Regular"],["Jacques Francois (Regular)", "Jacques Francois", "Regular"],["Jaldi (Regular)", "Jaldi", "Regular"],["Jaldi (Bold)", "Jaldi", "Bold"],["JejuGothic (Regular)", "JejuGothic", "Regular"],["JejuHallasan (Regular)", "JejuHallasan", "Regular"],["JejuMyeongjo (Regular)", "JejuMyeongjo", "Regular"],["Jim Nightshade (Regular)", "Jim Nightshade", "Regular"],["Jockey One (Regular)", "Jockey One", "Regular"],["Jolly Lodger (Regular)", "Jolly Lodger", "Regular"],["Jomolhari (Regular)", "Jomolhari", "Regular"],["Josefin Sans Std (Regular)", "Josefin Sans Std", "Regular"],["Josefin Sans (Thin)", "Josefin Sans", "Thin"],["Josefin Sans (Light)", "Josefin Sans", "Light"],["Josefin Sans (Regular)", "Josefin Sans", "Regular"],["Josefin Sans (SemiBold)", "Josefin Sans", "SemiBold"],["Josefin Sans (Bold)", "Josefin Sans", "Bold"],["Josefin Sans (Light Italic)", "Josefin Sans", "Light Italic"],["Josefin Sans (Bold Italic)", "Josefin Sans", "Bold Italic"],["Josefin Slab (Thin)", "Josefin Slab", "Thin"],["Josefin Slab (Light)", "Josefin Slab", "Light"],["Josefin Slab (Regular)", "Josefin Slab", "Regular"],["Josefin Slab (SemiBold)", "Josefin Slab", "SemiBold"],["Josefin Slab (Bold)", "Josefin Slab", "Bold"],["Josefin Slab (Thin Italic)", "Josefin Slab", "Thin Italic"],["Josefin Slab (Light Italic)", "Josefin Slab", "Light Italic"],["Josefin Slab (Italic)", "Josefin Slab", "Italic"],["Josefin Slab (SemiBold Italic)", "Josefin Slab", "SemiBold Italic"],["Josefin Slab (Bold Italic)", "Josefin Slab", "Bold Italic"],["Joti One (Regular)", "Joti One", "Regular"],["Judson (Regular)", "Judson", "Regular"],["Judson (Bold)", "Judson", "Bold"],["Judson (Italic)", "Judson", "Italic"],["Julee (Regular)", "Julee", "Regular"],["Julius Sans One (Regular)", "Julius Sans One", "Regular"],["Junge (Regular)", "Junge", "Regular"],["Jura (Light)", "Jura", "Light"],["Jura (Book)", "Jura", "Book"],["Jura (Medium)", "Jura", "Medium"],["Jura (DemiBold)", "Jura", "DemiBold"],["Just Another Hand (Regular)", "Just Another Hand", "Regular"],["Just Me Again Down Here (Regular)", "Just Me Again Down Here", "Regular"],["Kadwa (Regular)", "Kadwa", "Regular"],["Kadwa (Bold)", "Kadwa", "Bold"],["Kalam (Regular)", "Kalam", "Regular"],["Kalam (Regular)", "Kalam", "Regular"],["Kalam (Bold)", "Kalam", "Bold"],["Kameron (Regular)", "Kameron", "Regular"],["Kameron (Bold)", "Kameron", "Bold"],["Kantumruy Regular (Regular)", "Kantumruy Regular", "Regular"],["Kantumruy Regular (Regular)", "Kantumruy Regular", "Regular"],["Kantumruy Regular (Bold)", "Kantumruy Regular", "Bold"],["Karla (Regular)", "Karla", "Regular"],["Karla (Bold)", "Karla", "Bold"],["Karla (Italic)", "Karla", "Italic"],["Karla (BoldItalic)", "Karla", "BoldItalic"],["Karla Tamil (Regular)", "Karla Tamil", "Regular"],["Karla Tamil (Bold)", "Karla Tamil", "Bold"],["Karla Tamil (Inclined)", "Karla Tamil", "Inclined"],["Karla Tamil (Bold Inclined)", "Karla Tamil", "Bold Inclined"],["Karma (Light)", "Karma", "Light"],["Karma (Regular)", "Karma", "Regular"],["Karma (Medium)", "Karma", "Medium"],["Karma (Semibold)", "Karma", "Semibold"],["Karma (Bold)", "Karma", "Bold"],["Kaushan Script (Regular)", "Kaushan Script", "Regular"],["Kavoon (Regular)", "Kavoon", "Regular"],["Kdam Thmor (Regular)", "Kdam Thmor", "Regular"],["Keania One (Regular)", "Keania One", "Regular"],["Kelly Slab (Regular)", "Kelly Slab", "Regular"],["Kenia (Regular)", "Kenia", "Regular"],["Khand (Regular)", "Khand", "Regular"],["Khand (Medium)", "Khand", "Medium"],["Khmer (Regular)", "Khmer", "Regular"],["Khula (Light)", "Khula", "Light"],["Khula (Regular)", "Khula", "Regular"],["Khula (Semibold)", "Khula", "Semibold"],["Khula (Bold)", "Khula", "Bold"],["Khula (ExtraBold)", "Khula", "ExtraBold"],["Kite One (Regular)", "Kite One", "Regular"],["Knewave (Regular)", "Knewave", "Regular"],["KoPub Batang (Light)", "KoPub Batang", "Light"],["KoPub Batang (Regular)", "KoPub Batang", "Regular"],["KoPub Batang (Bold)", "KoPub Batang", "Bold"],["Kotta One (Regular)", "Kotta One", "Regular"],["Koulen (Regular)", "Koulen", "Regular"],["Kranky (Regular)", "Kranky", "Regular"],["Kreon (Light)", "Kreon", "Light"],["Kreon (Regular)", "Kreon", "Regular"],["Kreon (Bold)", "Kreon", "Bold"],["Kristi (Medium)", "Kristi", "Medium"],["Krona One (Regular)", "Krona One", "Regular"],["La Belle Aurore (Regular)", "La Belle Aurore", "Regular"],["Laila (Light)", "Laila", "Light"],["Laila (Regular)", "Laila", "Regular"],["Laila (Medium)", "Laila", "Medium"],["Laila (Semibold)", "Laila", "Semibold"],["Laila (Bold)", "Laila", "Bold"],["Lakki Reddy (Regular)", "Lakki Reddy", "Regular"],["Lancelot (Regular)", "Lancelot", "Regular"],["Lao Muang Don (Regular)", "Lao Muang Don", "Regular"],["Lao Muang Khong (Regular)", "Lao Muang Khong", "Regular"],["Lao Sans Pro (Regular)", "Lao Sans Pro", "Regular"],["Lateef (Regular)", "Lateef", "Regular"],["Lato (Hairline)", "Lato", "Hairline"],["Lato (Light)", "Lato", "Light"],["Lato (Regular)", "Lato", "Regular"],["Lato (Bold)", "Lato", "Bold"],["Lato (Black)", "Lato", "Black"],["Lato (Hairline Italic)", "Lato", "Hairline Italic"],["Lato (Light Italic)", "Lato", "Light Italic"],["Lato (Italic)", "Lato", "Italic"],["Lato (Bold Italic)", "Lato", "Bold Italic"],["Lato (Black Italic)", "Lato", "Black Italic"],["League Script (League Script)", "League Script", "League Script"],["Leckerli One (Regular)", "Leckerli One", "Regular"],["Ledger (Regular)", "Ledger", "Regular"],["Lekton (Regular)", "Lekton", "Regular"],["Lekton (Bold)", "Lekton", "Bold"],["Lekton (Italic)", "Lekton", "Italic"],["Lemon One (Regular)", "Lemon One", "Regular"],["Lemon (Regular)", "Lemon", "Regular"],["Libre Baskerville (Regular)", "Libre Baskerville", "Regular"],["Libre Baskerville (Bold)", "Libre Baskerville", "Bold"],["Libre Baskerville (Italic)", "Libre Baskerville", "Italic"],["Libre Caslon Text (Regular)", "Libre Caslon Text", "Regular"],["Libre Caslon Text (Bold)", "Libre Caslon Text", "Bold"],["Libre Caslon Text (Italic)", "Libre Caslon Text", "Italic"],["Life Savers (Regular)", "Life Savers", "Regular"],["Life Savers (Bold)", "Life Savers", "Bold"],["Life Savers (ExtraBold)", "Life Savers", "ExtraBold"],["Lilita One (Regular)", "Lilita One", "Regular"],["Lily Script One (Regular)", "Lily Script One", "Regular"],["Limelight (Regular)", "Limelight", "Regular"],["Linden Hill (Regular)", "Linden Hill", "Regular"],["Linden Hill (Italic)", "Linden Hill", "Italic"],["Lobster (Regular)", "Lobster", "Regular"],["Lobster Two (Regular)", "Lobster Two", "Regular"],["Lobster Two (Bold)", "Lobster Two", "Bold"],["Lobster Two (Italic)", "Lobster Two", "Italic"],["Lobster Two (Bold Italic)", "Lobster Two", "Bold Italic"],["Lohit Bengali (Regular)", "Lohit Bengali", "Regular"],["Lohit Devanagari (Regular)", "Lohit Devanagari", "Regular"],["Lohit Tamil (Regular)", "Lohit Tamil", "Regular"],["Londrina Outline (Regular)", "Londrina Outline", "Regular"],["Londrina Shadow (Regular)", "Londrina Shadow", "Regular"],["Londrina Sketch (Regular)", "Londrina Sketch", "Regular"],["Londrina Solid (Regular)", "Londrina Solid", "Regular"],["Lora (Regular)", "Lora", "Regular"],["Lora (Bold)", "Lora", "Bold"],["Lora (Italic)", "Lora", "Italic"],["Lora (Bold Italic)", "Lora", "Bold Italic"],["Loved by the King (Regular)", "Loved by the King", "Regular"],["Lovers Quarrel (Regular)", "Lovers Quarrel", "Regular"],["Love Ya Like A Sister (Regular)", "Love Ya Like A Sister", "Regular"],["Luckiest Guy (Regular)", "Luckiest Guy", "Regular"],["Lusitana (Regular)", "Lusitana", "Regular"],["Lusitana (Bold)", "Lusitana", "Bold"],["Lustria (Regular)", "Lustria", "Regular"],["Macondo (Regular)", "Macondo", "Regular"],["Macondo Swash Caps (Regular)", "Macondo Swash Caps", "Regular"],["Magra (Regular)", "Magra", "Regular"],["Magra (Bold)", "Magra", "Bold"],["Maiden Orange (Regular)", "Maiden Orange", "Regular"],["Mako (Regular)", "Mako", "Regular"],["Mallanna (Regular)", "Mallanna", "Regular"],["Mandali (Regular)", "Mandali", "Regular"],["Marcellus SC (Regular)", "Marcellus SC", "Regular"],["Marcellus (Regular)", "Marcellus", "Regular"],["Marck Script (Regular)", "Marck Script", "Regular"],["Margarine (Regular)", "Margarine", "Regular"],["Marko One (Regular)", "Marko One", "Regular"],["Marmelad (Regular)", "Marmelad", "Regular"],["Martel Sans (ExtraLight)", "Martel Sans", "ExtraLight"],["Martel Sans (Light)", "Martel Sans", "Light"],["Martel Sans (Regular)", "Martel Sans", "Regular"],["Martel Sans (SemiBold)", "Martel Sans", "SemiBold"],["Martel Sans (Bold)", "Martel Sans", "Bold"],["Martel Sans (ExtraBold)", "Martel Sans", "ExtraBold"],["Martel Sans (Black)", "Martel Sans", "Black"],["Marvel (Regular)", "Marvel", "Regular"],["Marvel (Bold)", "Marvel", "Bold"],["Marvel (Italic)", "Marvel", "Italic"],["Marvel (Bold Italic)", "Marvel", "Bold Italic"],["Mate SC (Regular)", "Mate SC", "Regular"],["Mate (Regular)", "Mate", "Regular"],["Mate (Italic)", "Mate", "Italic"],["Maven Pro (Regular)", "Maven Pro", "Regular"],["Maven Pro (Medium)", "Maven Pro", "Medium"],["Maven Pro (Bold)", "Maven Pro", "Bold"],["Maven Pro (Black)", "Maven Pro", "Black"],["McLaren (Regular)", "McLaren", "Regular"],["Meddon (Regular)", "Meddon", "Regular"],["MedievalSharp (Regular)", "MedievalSharp", "Regular"],["Medula One (Regular)", "Medula One", "Regular"],["Megrim (Medium)", "Megrim", "Medium"],["Meie Script (Regular)", "Meie Script", "Regular"],["Merge One (Regular)", "Merge One", "Regular"],["Merienda One (Regular)", "Merienda One", "Regular"],["Merienda (Regular)", "Merienda", "Regular"],["Merienda (Bold)", "Merienda", "Bold"],["Merriweather Sans (Light)", "Merriweather Sans", "Light"],["Merriweather Sans (Regular)", "Merriweather Sans", "Regular"],["Merriweather Sans (Bold)", "Merriweather Sans", "Bold"],["Merriweather Sans (ExtraBold)", "Merriweather Sans", "ExtraBold"],["Merriweather Sans (Light Italic)", "Merriweather Sans", "Light Italic"],["Merriweather Sans (Italic)", "Merriweather Sans", "Italic"],["Merriweather Sans (Bold Italic)", "Merriweather Sans", "Bold Italic"],["Merriweather Sans (ExtraBold Italic)", "Merriweather Sans", "ExtraBold Italic"],["Merriweather (Light)", "Merriweather", "Light"],["Merriweather (Regular)", "Merriweather", "Regular"],["Merriweather (Bold)", "Merriweather", "Bold"],["Merriweather (Heavy)", "Merriweather", "Heavy"],["Merriweather (Light Italic)", "Merriweather", "Light Italic"],["Merriweather (Italic)", "Merriweather", "Italic"],["Merriweather (Bold Italic)", "Merriweather", "Bold Italic"],["Merriweather (Heavy Italic)", "Merriweather", "Heavy Italic"],["Mervale Script (Regular)", "Mervale Script", "Regular"],["Metal Mania (Regular)", "Metal Mania", "Regular"],["Metal (Regular)", "Metal", "Regular"],["Metamorphous (Regular)", "Metamorphous", "Regular"],["Metrophobic (Regular)", "Metrophobic", "Regular"],["Miama (Regular)", "Miama", "Regular"],["Michroma (Regular)", "Michroma", "Regular"],["Milonga (Regular)", "Milonga", "Regular"],["Miltonian (Regular)", "Miltonian", "Regular"],["Miltonian Tattoo (Regular)", "Miltonian Tattoo", "Regular"],["Miniver (Regular)", "Miniver", "Regular"],["Miss Fajardose (Regular)", "Miss Fajardose", "Regular"],["Modak (Regular)", "Modak", "Regular"],["Modern Antiqua (Regular)", "Modern Antiqua", "Regular"],["Molengo (Regular)", "Molengo", "Regular"],["Molle (Regular)", "Molle", "Regular"],["Monda (Regular)", "Monda", "Regular"],["Monda (Bold)", "Monda", "Bold"],["Monda (Bold)", "Monda", "Bold"],["Monofett (Regular)", "Monofett", "Regular"],["Monoton (Regular)", "Monoton", "Regular"],["Monsieur La Doulaise (Regular)", "Monsieur La Doulaise", "Regular"],["Montaga (Regular)", "Montaga", "Regular"],["Montez (Regular)", "Montez", "Regular"],["Montserrat Alternates (Regular)", "Montserrat Alternates", "Regular"],["Montserrat Alternates (Bold)", "Montserrat Alternates", "Bold"],["Montserrat (Thin)", "Montserrat", "Thin"],["Montserrat (Light)", "Montserrat", "Light"],["Montserrat (Regular)", "Montserrat", "Regular"],["Montserrat (Bold)", "Montserrat", "Bold"],["Montserrat (Black)", "Montserrat", "Black"],["Montserrat Subrayada (Regular)", "Montserrat Subrayada", "Regular"],["Montserrat Subrayada (Bold)", "Montserrat Subrayada", "Bold"],["Moul Pali (Regular)", "Moul Pali", "Regular"],["Moul (Regular)", "Moul", "Regular"],["Mountains of Christmas (Regular)", "Mountains of Christmas", "Regular"],["Mountains of Christmas (Bold)", "Mountains of Christmas", "Bold"],["Mouse Memoirs (Regular)", "Mouse Memoirs", "Regular"],["Mr Bedfort (Regular)", "Mr Bedfort", "Regular"],["Mr Dafoe (Regular)", "Mr Dafoe", "Regular"],["Mr De Haviland (Regular)", "Mr De Haviland", "Regular"],["Mrs Saint Delafield (Regular)", "Mrs Saint Delafield", "Regular"],["Mrs Sheppards (Regular)", "Mrs Sheppards", "Regular"],["Muli (Light)", "Muli", "Light"],["Muli (Regular)", "Muli", "Regular"],["Muli (Light Italic)", "Muli", "Light Italic"],["Muli (Italic)", "Muli", "Italic"],["Myanmar Sans Pro (Regular)", "Myanmar Sans Pro", "Regular"],["Mystery Quest (Regular)", "Mystery Quest", "Regular"],["Nanum Brush Script (Regular)", "Nanum Brush Script", "Regular"],["NanumGothicCoding (Regular)", "NanumGothicCoding", "Regular"],["NanumGothicCoding (Bold)", "NanumGothicCoding", "Bold"],["NanumGothic (Regular)", "NanumGothic", "Regular"],["NanumGothic (Bold)", "NanumGothic", "Bold"],["NanumGothic (ExtraBold)", "NanumGothic", "ExtraBold"],["NanumMyeongjo (Regular)", "NanumMyeongjo", "Regular"],["NanumMyeongjo (Bold)", "NanumMyeongjo", "Bold"],["NanumMyeongjo (ExtraBold)", "NanumMyeongjo", "ExtraBold"],["Nanum Pen (Regular)", "Nanum Pen", "Regular"],["NATS (Regular)", "NATS", "Regular"],["Neucha (Regular)", "Neucha", "Regular"],["Neuton (ExtraLight)", "Neuton", "ExtraLight"],["Neuton (Light)", "Neuton", "Light"],["Neuton (Regular)", "Neuton", "Regular"],["Neuton (Bold)", "Neuton", "Bold"],["Neuton (ExtraBold)", "Neuton", "ExtraBold"],["Neuton (Italic)", "Neuton", "Italic"],["New Rocker (Regular)", "New Rocker", "Regular"],["News Cycle (Regular)", "News Cycle", "Regular"],["News Cycle (Bold)", "News Cycle", "Bold"],["Niconne (Regular)", "Niconne", "Regular"],["Nimbus Mono L (Regular)", "Nimbus Mono L", "Regular"],["Nimbus Mono L (Bold)", "Nimbus Mono L", "Bold"],["Nimbus Mono L (Regular Oblique)", "Nimbus Mono L", "Regular Oblique"],["Nimbus Mono L (Bold Oblique)", "Nimbus Mono L", "Bold Oblique"],["Nimbus Roman No9 L (Regular)", "Nimbus Roman No9 L", "Regular"],["Nimbus Roman No9 L (Medium)", "Nimbus Roman No9 L", "Medium"],["Nimbus Roman No9 L (Regular Italic)", "Nimbus Roman No9 L", "Regular Italic"],["Nimbus Roman No9 L (Medium Italic)", "Nimbus Roman No9 L", "Medium Italic"],["Nimbus Sans L (Regular Condensed)", "Nimbus Sans L", "Regular Condensed"],["Nimbus Sans L (Regular)", "Nimbus Sans L", "Regular"],["Nimbus Sans L (Bold Condensed)", "Nimbus Sans L", "Bold Condensed"],["Nimbus Sans L (Bold)", "Nimbus Sans L", "Bold"],["Nimbus Sans L (Regular Condensed Italic)", "Nimbus Sans L", "Regular Condensed Italic"],["Nimbus Sans L (Regular Italic)", "Nimbus Sans L", "Regular Italic"],["Nimbus Sans L (Bold Condensed Italic)", "Nimbus Sans L", "Bold Condensed Italic"],["Nimbus Sans L (Bold Italic)", "Nimbus Sans L", "Bold Italic"],["Nixie One (Regular)", "Nixie One", "Regular"],["Nobile (Regular)", "Nobile", "Regular"],["Nobile (Regular)", "Nobile", "Regular"],["Nobile (Medium)", "Nobile", "Medium"],["Nobile (Italic)", "Nobile", "Italic"],["Nobile (Medium Italic)", "Nobile", "Medium Italic"],["Nobile (Bold Italic)", "Nobile", "Bold Italic"],["Nokora Regular (Nokora Regular)", "Nokora Regular", "Nokora Regular"],["Nokora Regular (Regular)", "Nokora Regular", "Regular"],["Norican (Regular)", "Norican", "Regular"],["Nosifer Caps (Regular)", "Nosifer Caps", "Regular"],["Nosifer (Regular)", "Nosifer", "Regular"],["Nothing You Could Do (Regular)", "Nothing You Could Do", "Regular"],["Noticia Text (Regular)", "Noticia Text", "Regular"],["Noticia Text (Bold)", "Noticia Text", "Bold"],["Noticia Text (Italic)", "Noticia Text", "Italic"],["Noticia Text (Bold Italic)", "Noticia Text", "Bold Italic"],["Noto Kufi Arabic (Regular)", "Noto Kufi Arabic", "Regular"],["Noto Kufi Arabic (Bold)", "Noto Kufi Arabic", "Bold"],["Noto Naskh Arabic (Regular)", "Noto Naskh Arabic", "Regular"],["Noto Naskh Arabic (Bold)", "Noto Naskh Arabic", "Bold"],["Noto Nastaliq Urdu Draft (Regular)", "Noto Nastaliq Urdu Draft", "Regular"],["Noto Sans Armenian (Regular)", "Noto Sans Armenian", "Regular"],["Noto Sans Armenian (Bold)", "Noto Sans Armenian", "Bold"],["Noto Sans Avestan (Regular)", "Noto Sans Avestan", "Regular"],["Noto Sans Bengali (Regular)", "Noto Sans Bengali", "Regular"],["Noto Sans Bengali (Bold)", "Noto Sans Bengali", "Bold"],["Noto Sans Brahmi (Regular)", "Noto Sans Brahmi", "Regular"],["Noto Sans Carian (Regular)", "Noto Sans Carian", "Regular"],["Noto Sans Cherokee (Regular)", "Noto Sans Cherokee", "Regular"],["Noto Sans Coptic (Regular)", "Noto Sans Coptic", "Regular"],["Noto Sans Cypriot Syllabary (Regular)", "Noto Sans Cypriot Syllabary", "Regular"],["Noto Sans Deseret (Regular)", "Noto Sans Deseret", "Regular"],["Noto Sans Devanagari (Regular)", "Noto Sans Devanagari", "Regular"],["Noto Sans Devanagari (Bold)", "Noto Sans Devanagari", "Bold"],["Noto Sans Devanagari UI (Regular)", "Noto Sans Devanagari UI", "Regular"],["Noto Sans Devanagari UI (Bold)", "Noto Sans Devanagari UI", "Bold"],["Noto Sans Egyptian Hieroglyphs (Regular)", "Noto Sans Egyptian Hieroglyphs", "Regular"],["Noto Sans Ethiopic (Regular)", "Noto Sans Ethiopic", "Regular"],["Noto Sans Ethiopic (Bold)", "Noto Sans Ethiopic", "Bold"],["Noto Sans Georgian (Regular)", "Noto Sans Georgian", "Regular"],["Noto Sans Georgian (Bold)", "Noto Sans Georgian", "Bold"],["Noto Sans Glagolitic (Regular)", "Noto Sans Glagolitic", "Regular"],["Noto Sans Gujarati (Regular)", "Noto Sans Gujarati", "Regular"],["Noto Sans Gujarati (Bold)", "Noto Sans Gujarati", "Bold"],["Noto Sans Gujarati UI (Regular)", "Noto Sans Gujarati UI", "Regular"],["Noto Sans Gujarati UI (Bold)", "Noto Sans Gujarati UI", "Bold"],["Noto Sans Gurmukhi (Regular)", "Noto Sans Gurmukhi", "Regular"],["Noto Sans Gurmukhi (Bold)", "Noto Sans Gurmukhi", "Bold"],["Noto Sans Hanunoo (Regular)", "Noto Sans Hanunoo", "Regular"],["Noto Sans Hebrew (Regular)", "Noto Sans Hebrew", "Regular"],["Noto Sans Hebrew (Bold)", "Noto Sans Hebrew", "Bold"],["Noto Sans Imperial Aramaic (Regular)", "Noto Sans Imperial Aramaic", "Regular"],["Noto Sans Kaithi (Regular)", "Noto Sans Kaithi", "Regular"],["Noto Sans Kannada (Regular)", "Noto Sans Kannada", "Regular"],["Noto Sans Kannada (Bold)", "Noto Sans Kannada", "Bold"],["Noto Sans Kayah Li (Regular)", "Noto Sans Kayah Li", "Regular"],["Noto Sans Kharoshthi (Regular)", "Noto Sans Kharoshthi", "Regular"],["Noto Sans Kufi Arabic (Regular)", "Noto Sans Kufi Arabic", "Regular"],["Noto Sans Kufi Arabic (Bold)", "Noto Sans Kufi Arabic", "Bold"],["Noto Sans Lao (Regular)", "Noto Sans Lao", "Regular"],["Noto Sans Lao (Bold)", "Noto Sans Lao", "Bold"],["Noto Sans Lao UI (Regular)", "Noto Sans Lao UI", "Regular"],["Noto Sans Lao UI (Bold)", "Noto Sans Lao UI", "Bold"],["Noto Sans Lisu (Regular)", "Noto Sans Lisu", "Regular"],["Noto Sans Lycian (Regular)", "Noto Sans Lycian", "Regular"],["Noto Sans Lydian (Regular)", "Noto Sans Lydian", "Regular"],["Noto Sans Malayalam (Regular)", "Noto Sans Malayalam", "Regular"],["Noto Sans Malayalam (Bold)", "Noto Sans Malayalam", "Bold"],["Noto Sans Mandaic (Regular)", "Noto Sans Mandaic", "Regular"],["Noto Sans Meetei Mayek (Regular)", "Noto Sans Meetei Mayek", "Regular"],["Noto Sans Myanmar (Regular)", "Noto Sans Myanmar", "Regular"],["Noto Sans Myanmar (Bold)", "Noto Sans Myanmar", "Bold"],["Noto Sans NKo (Regular)", "Noto Sans NKo", "Regular"],["Noto Sans (Regular)", "Noto Sans", "Regular"],["Noto Sans (Regular)", "Noto Sans", "Regular"],["Noto Sans (Regular)", "Noto Sans", "Regular"],["Noto Sans (Bold)", "Noto Sans", "Bold"],["Noto Sans (Bold)", "Noto Sans", "Bold"],["Noto Sans (Bold)", "Noto Sans", "Bold"],["Noto Sans (Italic)", "Noto Sans", "Italic"],["Noto Sans (Bold Italic)", "Noto Sans", "Bold Italic"],["Noto Sans Old South Arabian (Regular)", "Noto Sans Old South Arabian", "Regular"],["Noto Sans Old Turkic (Regular)", "Noto Sans Old Turkic", "Regular"],["Noto Sans Osmanya (Regular)", "Noto Sans Osmanya", "Regular"],["Noto Sans Phoenician (Regular)", "Noto Sans Phoenician", "Regular"],["Noto Sans Shavian (Regular)", "Noto Sans Shavian", "Regular"],["Noto Sans Sinhala (Regular)", "Noto Sans Sinhala", "Regular"],["Noto Sans Sinhala (Bold)", "Noto Sans Sinhala", "Bold"],["Noto Sans Symbols (Regular)", "Noto Sans Symbols", "Regular"],["Noto Sans Tagalog (Regular)", "Noto Sans Tagalog", "Regular"],["Noto Sans Tai Tham (Regular)", "Noto Sans Tai Tham", "Regular"],["Noto Sans Tai Viet (Regular)", "Noto Sans Tai Viet", "Regular"],["Noto Sans Tamil (Regular)", "Noto Sans Tamil", "Regular"],["Noto Sans Tamil (Bold)", "Noto Sans Tamil", "Bold"],["Noto Sans Tamil UI (Regular)", "Noto Sans Tamil UI", "Regular"],["Noto Sans Tamil UI (Bold)", "Noto Sans Tamil UI", "Bold"],["Noto Sans Telugu (Regular)", "Noto Sans Telugu", "Regular"],["Noto Sans Telugu (Bold)", "Noto Sans Telugu", "Bold"],["Noto Sans Thai (Regular)", "Noto Sans Thai", "Regular"],["Noto Sans Thai (Bold)", "Noto Sans Thai", "Bold"],["Noto Sans Thai UI (Regular)", "Noto Sans Thai UI", "Regular"],["Noto Sans Thai UI (Bold)", "Noto Sans Thai UI", "Bold"],["Noto Sans Ugaritic (Regular)", "Noto Sans Ugaritic", "Regular"],["Noto Sans UI (Regular)", "Noto Sans UI", "Regular"],["Noto Sans UI (Bold)", "Noto Sans UI", "Bold"],["Noto Sans UI (Italic)", "Noto Sans UI", "Italic"],["Noto Sans UI (Bold Italic)", "Noto Sans UI", "Bold Italic"],["Noto Sans Vai (Regular)", "Noto Sans Vai", "Regular"],["Noto Serif Armenian (Regular)", "Noto Serif Armenian", "Regular"],["Noto Serif Armenian (Bold)", "Noto Serif Armenian", "Bold"],["Noto Serif Georgian (Regular)", "Noto Serif Georgian", "Regular"],["Noto Serif Georgian (Bold)", "Noto Serif Georgian", "Bold"],["Noto Serif Khmer (Regular)", "Noto Serif Khmer", "Regular"],["Noto Serif Khmer (Bold)", "Noto Serif Khmer", "Bold"],["Noto Serif Lao (Regular)", "Noto Serif Lao", "Regular"],["Noto Serif Lao (Bold)", "Noto Serif Lao", "Bold"],["Noto Serif (Regular)", "Noto Serif", "Regular"],["Noto Serif (Bold)", "Noto Serif", "Bold"],["Noto Serif (Italic)", "Noto Serif", "Italic"],["Noto Serif (Bold Italic)", "Noto Serif", "Bold Italic"],["Noto Serif Thai (Regular)", "Noto Serif Thai", "Regular"],["Noto Serif Thai (Bold)", "Noto Serif Thai", "Bold"],["Nova Cut (Book)", "Nova Cut", "Book"],["Nova Flat (Book)", "Nova Flat", "Book"],["NovaMono (Regular)", "NovaMono", "Regular"],["Nova Oval (Book)", "Nova Oval", "Book"],["Nova Round (Book)", "Nova Round", "Book"],["Nova Script (Book)", "Nova Script", "Book"],["Nova Slim (Book)", "Nova Slim", "Book"],["Nova Square (Book)", "Nova Square", "Book"],["NTR (Regular)", "NTR", "Regular"],["Numans (Regular)", "Numans", "Regular"],["Nunito (Light)", "Nunito", "Light"],["Nunito (Regular)", "Nunito", "Regular"],["Nunito (Bold)", "Nunito", "Bold"],["OdorMeanChey (Regular)", "OdorMeanChey", "Regular"],["Offside (Regular)", "Offside", "Regular"],["OFL Sorts Mill Goudy TT (Regular)", "OFL Sorts Mill Goudy TT", "Regular"],["OFL Sorts Mill Goudy TT (Italic)", "OFL Sorts Mill Goudy TT", "Italic"],["Oldenburg (Regular)", "Oldenburg", "Regular"],["Old Standard TT (Regular)", "Old Standard TT", "Regular"],["Old Standard TT (Bold)", "Old Standard TT", "Bold"],["Old Standard TT (Italic)", "Old Standard TT", "Italic"],["Oleo Script (Regular)", "Oleo Script", "Regular"],["Oleo Script (Bold)", "Oleo Script", "Bold"],["Oleo Script Swash Caps (Regular)", "Oleo Script Swash Caps", "Regular"],["Oleo Script Swash Caps (Bold)", "Oleo Script Swash Caps", "Bold"],["Open Sans Condensed (Bold)", "Open Sans Condensed", "Bold"],["Open Sans Hebrew Condensed (Light)", "Open Sans Hebrew Condensed", "Light"],["Open Sans Hebrew Condensed (Regular)", "Open Sans Hebrew Condensed", "Regular"],["Open Sans Hebrew Condensed (Bold)", "Open Sans Hebrew Condensed", "Bold"],["Open Sans Hebrew Condensed (Extra Bold)", "Open Sans Hebrew Condensed", "Extra Bold"],["Open Sans Hebrew Condensed (Light Italic)", "Open Sans Hebrew Condensed", "Light Italic"],["Open Sans Hebrew Condensed (Italic)", "Open Sans Hebrew Condensed", "Italic"],["Open Sans Hebrew Condensed (Bold Italic)", "Open Sans Hebrew Condensed", "Bold Italic"],["Open Sans Hebrew Condensed (Extra Bold Italic)", "Open Sans Hebrew Condensed", "Extra Bold Italic"],["Open Sans Hebrew (Light)", "Open Sans Hebrew", "Light"],["Open Sans Hebrew (Regular)", "Open Sans Hebrew", "Regular"],["Open Sans Hebrew (Bold)", "Open Sans Hebrew", "Bold"],["Open Sans Hebrew (Extra Bold)", "Open Sans Hebrew", "Extra Bold"],["Open Sans Hebrew (Light Italic)", "Open Sans Hebrew", "Light Italic"],["Open Sans Hebrew (Italic)", "Open Sans Hebrew", "Italic"],["Open Sans Hebrew (Bold Italic)", "Open Sans Hebrew", "Bold Italic"],["Open Sans Hebrew (Extra Bold Italic)", "Open Sans Hebrew", "Extra Bold Italic"],["Open Sans (Condensed Light)", "Open Sans", "Condensed Light"],["Open Sans (Light)", "Open Sans", "Light"],["Open Sans (Regular)", "Open Sans", "Regular"],["Open Sans (Semibold)", "Open Sans", "Semibold"],["Open Sans (Bold)", "Open Sans", "Bold"],["Open Sans (Extrabold)", "Open Sans", "Extrabold"],["Open Sans (Condensed Light Italic)", "Open Sans", "Condensed Light Italic"],["Open Sans (Light Italic)", "Open Sans", "Light Italic"],["Open Sans (Italic)", "Open Sans", "Italic"],["Open Sans (Semibold Italic)", "Open Sans", "Semibold Italic"],["Open Sans (Bold Italic)", "Open Sans", "Bold Italic"],["Open Sans (Extrabold Italic)", "Open Sans", "Extrabold Italic"],["Oranienbaum (Regular)", "Oranienbaum", "Regular"],["Orbitron (Regular)", "Orbitron", "Regular"],["Orbitron (Medium)", "Orbitron", "Medium"],["Orbitron (Bold)", "Orbitron", "Bold"],["Orbitron (Black)", "Orbitron", "Black"],["Oregano (Regular)", "Oregano", "Regular"],["Oregano (Italic)", "Oregano", "Italic"],["Orienta (Regular)", "Orienta", "Regular"],["Original Surfer (Regular)", "Original Surfer", "Regular"],["Oswald (Light)", "Oswald", "Light"],["Oswald (Regular)", "Oswald", "Regular"],["Oswald (Bold)", "Oswald", "Bold"],["Overlock SC (Regular)", "Overlock SC", "Regular"],["Overlock (Regular)", "Overlock", "Regular"],["Overlock (Bold)", "Overlock", "Bold"],["Overlock (Black)", "Overlock", "Black"],["Overlock (Italic)", "Overlock", "Italic"],["Overlock (Bold Italic)", "Overlock", "Bold Italic"],["Overlock (Black Italic)", "Overlock", "Black Italic"],["Over the Rainbow (Regular)", "Over the Rainbow", "Regular"],["Ovo (Regular)", "Ovo", "Regular"],["Oxygen Mono (Regular)", "Oxygen Mono", "Regular"],["Oxygen (Light)", "Oxygen", "Light"],["Oxygen (Regular)", "Oxygen", "Regular"],["Oxygen (Bold)", "Oxygen", "Bold"],["Pacifico (Regular)", "Pacifico", "Regular"],["Padauk (Regular)", "Padauk", "Regular"],["Padauk (Regular)", "Padauk", "Regular"],["Padauk (Bold)", "Padauk", "Bold"],["Padauk (Bold)", "Padauk", "Bold"],["Palanquin Dark (Regular)", "Palanquin Dark", "Regular"],["Palanquin Dark (Medium)", "Palanquin Dark", "Medium"],["Palanquin Dark (SemiBold)", "Palanquin Dark", "SemiBold"],["Palanquin Dark (Bold)", "Palanquin Dark", "Bold"],["Palanquin (Thin)", "Palanquin", "Thin"],["Palanquin (ExtraLight)", "Palanquin", "ExtraLight"],["Palanquin (Light)", "Palanquin", "Light"],["Palanquin (Regular)", "Palanquin", "Regular"],["Palanquin (Medium)", "Palanquin", "Medium"],["Palanquin (SemiBold)", "Palanquin", "SemiBold"],["Palanquin (Bold)", "Palanquin", "Bold"],["Paprika (Regular)", "Paprika", "Regular"],["Parisienne (Regular)", "Parisienne", "Regular"],["Passero One (Regular)", "Passero One", "Regular"],["Passion One (Bold)", "Passion One", "Bold"],["Passion One (Black)", "Passion One", "Black"],["Passion (Bold)", "Passion", "Bold"],["Pathway Gothic One (Regular)", "Pathway Gothic One", "Regular"],["Patrick Hand SC (Regular)", "Patrick Hand SC", "Regular"],["Patrick Hand (Regular)", "Patrick Hand", "Regular"],["Patua One (Regular)", "Patua One", "Regular"],["Paytone One (Regular)", "Paytone One", "Regular"],["Pecita (Book)", "Pecita", "Book"],["Peddana (Regular)", "Peddana", "Regular"],["Peralta (Regular)", "Peralta", "Regular"],["Permanent Marker (Regular)", "Permanent Marker", "Regular"],["Petit Formal Script (Regular)", "Petit Formal Script", "Regular"],["Petrona (Regular)", "Petrona", "Regular"],["Phetsarath (Regular)", "Phetsarath", "Regular"],["Phetsarath (Bold)", "Phetsarath", "Bold"],["Philosopher (Regular)", "Philosopher", "Regular"],["Philosopher (Bold)", "Philosopher", "Bold"],["Philosopher (Italic)", "Philosopher", "Italic"],["Philosopher (Bold Italic)", "Philosopher", "Bold Italic"],["Piedra (Regular)", "Piedra", "Regular"],["Pinyon Script (Regular)", "Pinyon Script", "Regular"],["Pirata One (Regular)", "Pirata One", "Regular"],["Plaster (Regular)", "Plaster", "Regular"],["Playball (Regular)", "Playball", "Regular"],["Playfair Display (Regular)", "Playfair Display", "Regular"],["Playfair Display (Bold)", "Playfair Display", "Bold"],["Playfair Display (Black)", "Playfair Display", "Black"],["Playfair Display (Italic)", "Playfair Display", "Italic"],["Playfair Display (Bold Italic)", "Playfair Display", "Bold Italic"],["Playfair Display (Black Italic)", "Playfair Display", "Black Italic"],["Playfair Display SC (Regular)", "Playfair Display SC", "Regular"],["Playfair Display SC (Bold)", "Playfair Display SC", "Bold"],["Playfair Display SC (Black)", "Playfair Display SC", "Black"],["Playfair Display SC (Italic)", "Playfair Display SC", "Italic"],["Playfair Display SC (Bold Italic)", "Playfair Display SC", "Bold Italic"],["Playfair Display SC (Black Italic)", "Playfair Display SC", "Black Italic"],["Play (Regular)", "Play", "Regular"],["Play (Bold)", "Play", "Bold"],["Podkova (Regular)", "Podkova", "Regular"],["Podkova (Bold)", "Podkova", "Bold"],["PoetsenOne (Regular)", "PoetsenOne", "Regular"],["Poiret One (Regular)", "Poiret One", "Regular"],["Poller One (Regular)", "Poller One", "Regular"],["Poly (Regular)", "Poly", "Regular"],["Poly (Italic)", "Poly", "Italic"],["Pompiere  (Regular)", "Pompiere ", "Regular"],["Ponnala (Regular)", "Ponnala", "Regular"],["Pontano Sans (Regular)", "Pontano Sans", "Regular"],["Poppins (Light)", "Poppins", "Light"],["Poppins (Regular)", "Poppins", "Regular"],["Poppins (Medium)", "Poppins", "Medium"],["Poppins (SemiBold)", "Poppins", "SemiBold"],["Poppins (Bold)", "Poppins", "Bold"],["Porter Sans Block (Block)", "Porter Sans Block", "Block"],["Port Lligat Sans (Regular)", "Port Lligat Sans", "Regular"],["Port Lligat Slab (Regular)", "Port Lligat Slab", "Regular"],["Prata (Regular)", "Prata", "Regular"],["Preah Vihear (Regular)", "Preah Vihear", "Regular"],["Press Start 2P (Regular)", "Press Start 2P", "Regular"],["Princess Sofia (Regular)", "Princess Sofia", "Regular"],["Prociono (Regular)", "Prociono", "Regular"],["Prosto One (Regular)", "Prosto One", "Regular"],["PT Mono (Regular)", "PT Mono", "Regular"],["PT Sans Caption (Regular)", "PT Sans Caption", "Regular"],["PT Sans Caption (Bold)", "PT Sans Caption", "Bold"],["PT Sans Narrow (Regular)", "PT Sans Narrow", "Regular"],["PT Sans Narrow (Bold)", "PT Sans Narrow", "Bold"],["PT Sans (Regular)", "PT Sans", "Regular"],["PT Sans (Bold)", "PT Sans", "Bold"],["PT Sans (Italic)", "PT Sans", "Italic"],["PT Sans (Bold Italic)", "PT Sans", "Bold Italic"],["PT Serif Caption (Regular)", "PT Serif Caption", "Regular"],["PT Serif Caption (Italic)", "PT Serif Caption", "Italic"],["PT Serif (Regular)", "PT Serif", "Regular"],["PT Serif (Bold)", "PT Serif", "Bold"],["PT Serif (Italic)", "PT Serif", "Italic"],["PT Serif (Bold Italic)", "PT Serif", "Bold Italic"],["Puralecka Narrow (Regular)", "Puralecka Narrow", "Regular"],["Puralecka Narrow (Bold)", "Puralecka Narrow", "Bold"],["Puritan (Regular)", "Puritan", "Regular"],["Puritan (Bold)", "Puritan", "Bold"],["Puritan (Italic)", "Puritan", "Italic"],["Puritan (BoldItalic)", "Puritan", "BoldItalic"],["Purple Purse (Regular)", "Purple Purse", "Regular"],["Quando (Regular)", "Quando", "Regular"],["Quantico (Regular)", "Quantico", "Regular"],["Quantico (Bold)", "Quantico", "Bold"],["Quantico (Italic)", "Quantico", "Italic"],["Quantico (Bold Italic)", "Quantico", "Bold Italic"],["Quattrocento Roman (Regular)", "Quattrocento Roman", "Regular"],["Quattrocento Sans (Regular)", "Quattrocento Sans", "Regular"],["Quattrocento Sans (Bold)", "Quattrocento Sans", "Bold"],["Quattrocento Sans (Italic)", "Quattrocento Sans", "Italic"],["Quattrocento Sans (Bold Italic)", "Quattrocento Sans", "Bold Italic"],["Quattrocento (Bold)", "Quattrocento", "Bold"],["Questrial (Regular)", "Questrial", "Regular"],["Quicksand (Light)", "Quicksand", "Light"],["Quicksand (Regular)", "Quicksand", "Regular"],["Quicksand (Bold)", "Quicksand", "Bold"],["Quicksand (LightItalic)", "Quicksand", "LightItalic"],["Quicksand (Italic)", "Quicksand", "Italic"],["Quicksand (BoldItalic)", "Quicksand", "BoldItalic"],["Quintessential (Regular)", "Quintessential", "Regular"],["Qwigley (Regular)", "Qwigley", "Regular"],["Racing Sans One (Regular)", "Racing Sans One", "Regular"],["Radley (Regular)", "Radley", "Regular"],["Radley (Italic)", "Radley", "Italic"],["Rajdhani (Light)", "Rajdhani", "Light"],["Rajdhani (Regular)", "Rajdhani", "Regular"],["Rajdhani (Medium)", "Rajdhani", "Medium"],["Rajdhani (Semibold)", "Rajdhani", "Semibold"],["Rajdhani (Bold)", "Rajdhani", "Bold"],["Raleway Dots  (Regular)", "Raleway Dots ", "Regular"],["Raleway (Thin)", "Raleway", "Thin"],["Raleway (ExtraLight)", "Raleway", "ExtraLight"],["Raleway (Light)", "Raleway", "Light"],["Raleway (Regular)", "Raleway", "Regular"],["Raleway (Medium)", "Raleway", "Medium"],["Raleway (SemiBold)", "Raleway", "SemiBold"],["Raleway (Bold)", "Raleway", "Bold"],["Raleway (ExtraBold)", "Raleway", "ExtraBold"],["Raleway (Black)", "Raleway", "Black"],["Raleway (Thin Italic)", "Raleway", "Thin Italic"],["Raleway (ExtraLight Italic)", "Raleway", "ExtraLight Italic"],["Raleway (Light Italic)", "Raleway", "Light Italic"],["Raleway (Italic)", "Raleway", "Italic"],["Raleway (Medium Italic)", "Raleway", "Medium Italic"],["Raleway (SemiBold Italic)", "Raleway", "SemiBold Italic"],["Raleway (Bold Italic)", "Raleway", "Bold Italic"],["Raleway (ExtraBold Italic)", "Raleway", "ExtraBold Italic"],["Raleway (Black Italic)", "Raleway", "Black Italic"],["Ramabhadra (Regular)", "Ramabhadra", "Regular"],["Ramaraja (Regular)", "Ramaraja", "Regular"],["Rambla (Regular)", "Rambla", "Regular"],["Rambla (Bold)", "Rambla", "Bold"],["Rambla (Italic)", "Rambla", "Italic"],["Rambla (Bold Italic)", "Rambla", "Bold Italic"],["Rammetto One (Regular)", "Rammetto One", "Regular"],["Ranchers (Regular)", "Ranchers", "Regular"],["Rancho (Regular)", "Rancho", "Regular"],["Ranga (Regular)", "Ranga", "Regular"],["Ranga (Bold)", "Ranga", "Bold"],["Rationale (Regular)", "Rationale", "Regular"],["Ravi Prakash (Regular)", "Ravi Prakash", "Regular"],["Redacted Script (Regular)", "Redacted Script", "Regular"],["Redacted Script (Bold)", "Redacted Script", "Bold"],["Redacted (Regular)", "Redacted", "Regular"],["Redressed (Regular)", "Redressed", "Regular"],["Reenie Beanie (Regular)", "Reenie Beanie", "Regular"],["Revalia (Regular)", "Revalia", "Regular"],["Ribeye Marrow (Regular)", "Ribeye Marrow", "Regular"],["Ribeye (Regular)", "Ribeye", "Regular"],["Righteous (Regular)", "Righteous", "Regular"],["Risque (Regular)", "Risque", "Regular"],["Roboto Condensed (Light)", "Roboto Condensed", "Light"],["Roboto Condensed (Regular)", "Roboto Condensed", "Regular"],["Roboto Condensed (Bold)", "Roboto Condensed", "Bold"],["Roboto Condensed (Light Italic)", "Roboto Condensed", "Light Italic"],["Roboto Condensed (Italic)", "Roboto Condensed", "Italic"],["Roboto Condensed (Bold Italic)", "Roboto Condensed", "Bold Italic"],["Roboto (Thin)", "Roboto", "Thin"],["Roboto (Light)", "Roboto", "Light"],["Roboto (Regular)", "Roboto", "Regular"],["Roboto (Medium)", "Roboto", "Medium"],["Roboto (Bold)", "Roboto", "Bold"],["Roboto (Black)", "Roboto", "Black"],["Roboto (Thin Italic)", "Roboto", "Thin Italic"],["Roboto (Light Italic)", "Roboto", "Light Italic"],["Roboto (Italic)", "Roboto", "Italic"],["Roboto (Medium Italic)", "Roboto", "Medium Italic"],["Roboto (Bold Italic)", "Roboto", "Bold Italic"],["Roboto (Black Italic)", "Roboto", "Black Italic"],["Roboto Slab (Thin)", "Roboto Slab", "Thin"],["Roboto Slab (Light)", "Roboto Slab", "Light"],["Roboto Slab (Regular)", "Roboto Slab", "Regular"],["Roboto Slab (Bold)", "Roboto Slab", "Bold"],["Rochester (Regular)", "Rochester", "Regular"],["Rock Salt (Regular)", "Rock Salt", "Regular"],["Rokkitt (Light)", "Rokkitt", "Light"],["Rokkitt (Regular)", "Rokkitt", "Regular"],["Rokkitt (Bold)", "Rokkitt", "Bold"],["Romanesco (Regular)", "Romanesco", "Regular"],["Ropa Sans (Regular)", "Ropa Sans", "Regular"],["Ropa Sans (Italic)", "Ropa Sans", "Italic"],["Rosario (Regular)", "Rosario", "Regular"],["Rosario (Bold)", "Rosario", "Bold"],["Rosario (Italic)", "Rosario", "Italic"],["Rosario (Bold Italic)", "Rosario", "Bold Italic"],["Rosarivo (Regular)", "Rosarivo", "Regular"],["Rosarivo (Italic)", "Rosarivo", "Italic"],["Rouge Script (Regular)", "Rouge Script", "Regular"],["Rozha One (Regular)", "Rozha One", "Regular"],["Rubik Mono One (Regular)", "Rubik Mono One", "Regular"],["Rubik One (Regular)", "Rubik One", "Regular"],["Ruda (Regular)", "Ruda", "Regular"],["Ruda (Bold)", "Ruda", "Bold"],["Ruda (Black)", "Ruda", "Black"],["Rufina (Regular)", "Rufina", "Regular"],["Rufina (Bold)", "Rufina", "Bold"],["Ruge Boogie (Regular)", "Ruge Boogie", "Regular"],["Ruluko (Regular)", "Ruluko", "Regular"],["Rum Raisin (Regular)", "Rum Raisin", "Regular"],["Ruslan Display (Regular)", "Ruslan Display", "Regular"],["Russo One (Regular)", "Russo One", "Regular"],["Ruthie (Regular)", "Ruthie", "Regular"],["Rye (Regular)", "Rye", "Regular"],["Sacramento (Regular)", "Sacramento", "Regular"],["Sail (Regular)", "Sail", "Regular"],["Salsa (Regular)", "Salsa", "Regular"],["Sanchez (Regular)", "Sanchez", "Regular"],["Sanchez (Italic)", "Sanchez", "Italic"],["Sancreek (Regular)", "Sancreek", "Regular"],["Sansation Light (Light)", "Sansation Light", "Light"],["Sansation Light (Light Italic)", "Sansation Light", "Light Italic"],["Sansation (Regular)", "Sansation", "Regular"],["Sansation (Bold)", "Sansation", "Bold"],["Sansation (Italic)", "Sansation", "Italic"],["Sansation (Bold Italic)", "Sansation", "Bold Italic"],["Sansita One (Regular)", "Sansita One", "Regular"],["Sarabun (Regular)", "Sarabun", "Regular"],["Sarabun (Bold)", "Sarabun", "Bold"],["Sarabun (Italic)", "Sarabun", "Italic"],["Sarabun (Bold Italic)", "Sarabun", "Bold Italic"],["Sarala (Regular)", "Sarala", "Regular"],["Sarala (Bold)", "Sarala", "Bold"],["Sarina (Regular)", "Sarina", "Regular"],["Sarpanch (Regular)", "Sarpanch", "Regular"],["Sarpanch (Medium)", "Sarpanch", "Medium"],["Sarpanch (SemiBold)", "Sarpanch", "SemiBold"],["Sarpanch (Bold)", "Sarpanch", "Bold"],["Sarpanch (ExtraBold)", "Sarpanch", "ExtraBold"],["Sarpanch (Black)", "Sarpanch", "Black"],["Satisfy (Regular)", "Satisfy", "Regular"],["Scada (Regular)", "Scada", "Regular"],["Scada (Bold)", "Scada", "Bold"],["Scada (Italic)", "Scada", "Italic"],["Scada (Bold Italic)", "Scada", "Bold Italic"],["Scheherazade (Regular)", "Scheherazade", "Regular"],["Schoolbell (Regular)", "Schoolbell", "Regular"],["Seaweed Script (Regular)", "Seaweed Script", "Regular"],["Sedan SC (Regular)", "Sedan SC", "Regular"],["Sedan (Regular)", "Sedan", "Regular"],["Sedan (Italic)", "Sedan", "Italic"],["SeoulHangang CB (Regular)", "SeoulHangang CB", "Regular"],["SeoulHangang CBL (Regular)", "SeoulHangang CBL", "Regular"],["SeoulHangang CEB (Regular)", "SeoulHangang CEB", "Regular"],["SeoulHangang CL (Regular)", "SeoulHangang CL", "Regular"],["SeoulHangang CM (Regular)", "SeoulHangang CM", "Regular"],["SeoulHangang Smart B (Regular)", "SeoulHangang Smart B", "Regular"],["SeoulNamsan CB (Regular)", "SeoulNamsan CB", "Regular"],["SeoulNamsan CBL (Regular)", "SeoulNamsan CBL", "Regular"],["SeoulNamsan CEB (Regular)", "SeoulNamsan CEB", "Regular"],["SeoulNamsan CL (Regular)", "SeoulNamsan CL", "Regular"],["SeoulNamsan CM (Regular)", "SeoulNamsan CM", "Regular"],["SeoulNamsan Smart B (Regular)", "SeoulNamsan Smart B", "Regular"],["Sevillana (Regular)", "Sevillana", "Regular"],["Seymour One (Book)", "Seymour One", "Book"],["Shadows Into Light (Regular)", "Shadows Into Light", "Regular"],["Shadows Into Light Two (Regular)", "Shadows Into Light Two", "Regular"],["Shanti (Regular)", "Shanti", "Regular"],["Share (Regular)", "Share", "Regular"],["Share (Bold)", "Share", "Bold"],["Share (Italic)", "Share", "Italic"],["Share (Bold Italic)", "Share", "Bold Italic"],["Share Tech Mono (Regular)", "Share Tech Mono", "Regular"],["Share Tech (Regular)", "Share Tech", "Regular"],["Shojumaru (Regular)", "Shojumaru", "Regular"],["Short Stack (Regular)", "Short Stack", "Regular"],["Siemreap (Regular)", "Siemreap", "Regular"],["Sigmar One (Regular)", "Sigmar One", "Regular"],["Signika Negative (Light)", "Signika Negative", "Light"],["Signika Negative (Regular)", "Signika Negative", "Regular"],["Signika Negative (Semibold)", "Signika Negative", "Semibold"],["Signika Negative (Bold)", "Signika Negative", "Bold"],["Signika (Light)", "Signika", "Light"],["Signika (Regular)", "Signika", "Regular"],["Signika (Semibold)", "Signika", "Semibold"],["Signika (Bold)", "Signika", "Bold"],["Simonetta (Regular)", "Simonetta", "Regular"],["Simonetta (Black)", "Simonetta", "Black"],["Simonetta (Italic)", "Simonetta", "Italic"],["Simonetta (Black Italic)", "Simonetta", "Black Italic"],["Sintony (Regular)", "Sintony", "Regular"],["Sintony (Bold)", "Sintony", "Bold"],["SirinStencil (Regular)", "SirinStencil", "Regular"],["Six Caps (Regular)", "Six Caps", "Regular"],["Skranji (Regular)", "Skranji", "Regular"],["Skranji (Bold)", "Skranji", "Bold"],["Slabo 13px (Regular)", "Slabo 13px", "Regular"],["Slabo 27px (Regular)", "Slabo 27px", "Regular"],["Slackey (Regular)", "Slackey", "Regular"],["Smokum (Regular)", "Smokum", "Regular"],["Smythe (Regular)", "Smythe", "Regular"],["Sniglet (Regular)", "Sniglet", "Regular"],["Sniglet (ExtraBold)", "Sniglet", "ExtraBold"],["Snippet (Regular)", "Snippet", "Regular"],["Snowburst One (Regular)", "Snowburst One", "Regular"],["Sofadi One (Regular)", "Sofadi One", "Regular"],["Sofia (Regular)", "Sofia", "Regular"],["Sonsie One (Regular)", "Sonsie One", "Regular"],["Sorts Mill Goudy (Regular)", "Sorts Mill Goudy", "Regular"],["Sorts Mill Goudy (Italic)", "Sorts Mill Goudy", "Italic"],["Souliyo Unicode (Regular)", "Souliyo Unicode", "Regular"],["Source Code Pro (ExtraLight)", "Source Code Pro", "ExtraLight"],["Source Code Pro (Light)", "Source Code Pro", "Light"],["Source Code Pro (Regular)", "Source Code Pro", "Regular"],["Source Code Pro (Medium)", "Source Code Pro", "Medium"],["Source Code Pro (Semibold)", "Source Code Pro", "Semibold"],["Source Code Pro (Bold)", "Source Code Pro", "Bold"],["Source Code Pro (Black)", "Source Code Pro", "Black"],["Source Sans Pro (ExtraLight)", "Source Sans Pro", "ExtraLight"],["Source Sans Pro (Light)", "Source Sans Pro", "Light"],["Source Sans Pro (Regular)", "Source Sans Pro", "Regular"],["Source Sans Pro (Semibold)", "Source Sans Pro", "Semibold"],["Source Sans Pro (Bold)", "Source Sans Pro", "Bold"],["Source Sans Pro (Black)", "Source Sans Pro", "Black"],["Source Sans Pro (ExtraLight Italic)", "Source Sans Pro", "ExtraLight Italic"],["Source Sans Pro (Light Italic)", "Source Sans Pro", "Light Italic"],["Source Sans Pro (Italic)", "Source Sans Pro", "Italic"],["Source Sans Pro (Semibold Italic)", "Source Sans Pro", "Semibold Italic"],["Source Sans Pro (Bold Italic)", "Source Sans Pro", "Bold Italic"],["Source Sans Pro (Black Italic)", "Source Sans Pro", "Black Italic"],["Source Serif Pro (Regular)", "Source Serif Pro", "Regular"],["Source Serif Pro (Semibold)", "Source Serif Pro", "Semibold"],["Source Serif Pro (Bold)", "Source Serif Pro", "Bold"],["Special Elite (Regular)", "Special Elite", "Regular"],["Spicy Rice (Regular)", "Spicy Rice", "Regular"],["Spinnaker (Regular)", "Spinnaker", "Regular"],["Spirax (Regular)", "Spirax", "Regular"],["Squada One (Regular)", "Squada One", "Regular"],["Sree Krushnadevaraya (Regular)", "Sree Krushnadevaraya", "Regular"],["Stalemate (Regular)", "Stalemate", "Regular"],["Stalinist One (Regular)", "Stalinist One", "Regular"],["Stalin One (Regular)", "Stalin One", "Regular"],["Standard Symbols L (Regular)", "Standard Symbols L", "Regular"],["Stardos Stencil (Regular)", "Stardos Stencil", "Regular"],["Stardos Stencil (Bold)", "Stardos Stencil", "Bold"],["Stint Ultra Condensed (Regular)", "Stint Ultra Condensed", "Regular"],["Stint Ultra Expanded (Regular)", "Stint Ultra Expanded", "Regular"],["Stoke (Light)", "Stoke", "Light"],["Stoke (Regular)", "Stoke", "Regular"],["Strait (Regular)", "Strait", "Regular"],["Strong (Regular)", "Strong", "Regular"],["Sue Ellen Francisco  (Regular)", "Sue Ellen Francisco ", "Regular"],["Sunshiney (Regular)", "Sunshiney", "Regular"],["Supermercado (Regular)", "Supermercado", "Regular"],["Suranna (Regular)", "Suranna", "Regular"],["Suravaram (Regular)", "Suravaram", "Regular"],["Swanky and Moo Moo (Regular)", "Swanky and Moo Moo", "Regular"],["Syncopate (Regular)", "Syncopate", "Regular"],["Syncopate (Bold)", "Syncopate", "Bold"],["Tangerine (Regular)", "Tangerine", "Regular"],["Tangerine (Bold)", "Tangerine", "Bold"],["Taprom (Regular)", "Taprom", "Regular"],["Tauri (Regular)", "Tauri", "Regular"],["Teko (Light)", "Teko", "Light"],["Teko (Regular)", "Teko", "Regular"],["Teko (Medium)", "Teko", "Medium"],["Teko (Semibold)", "Teko", "Semibold"],["Teko (Bold)", "Teko", "Bold"],["Telex (Regular)", "Telex", "Regular"],["Tenali Ramakrishna (Regular)", "Tenali Ramakrishna", "Regular"],["Tenor Sans (Regular)", "Tenor Sans", "Regular"],["Terminal Dosis (ExtraLight)", "Terminal Dosis", "ExtraLight"],["Terminal Dosis (Light)", "Terminal Dosis", "Light"],["Terminal Dosis (Regular)", "Terminal Dosis", "Regular"],["Terminal Dosis (Medium)", "Terminal Dosis", "Medium"],["Terminal Dosis (SemiBold)", "Terminal Dosis", "SemiBold"],["Terminal Dosis (Bold)", "Terminal Dosis", "Bold"],["Terminal Dosis (ExtraBold)", "Terminal Dosis", "ExtraBold"],["Text Me One (Regular)", "Text Me One", "Regular"],["Thabit (Regular)", "Thabit", "Regular"],["Thabit (Bold)", "Thabit", "Bold"],["Thabit (Oblique)", "Thabit", "Oblique"],["Thabit (Bold Oblique)", "Thabit", "Bold Oblique"],["TharLon (Regular)", "TharLon", "Regular"],["The Girl Next Door (Regular)", "The Girl Next Door", "Regular"],["Tienne (Regular)", "Tienne", "Regular"],["Tienne (Bold)", "Tienne", "Bold"],["Tienne (Heavy)", "Tienne", "Heavy"],["Tillana (Regular)", "Tillana", "Regular"],["Tillana (Medium)", "Tillana", "Medium"],["Tillana (SemiBold)", "Tillana", "SemiBold"],["Tillana (Bold)", "Tillana", "Bold"],["Tillana (ExtraBold)", "Tillana", "ExtraBold"],["Timmana (Regular)", "Timmana", "Regular"],["Tinos (Regular)", "Tinos", "Regular"],["Tinos (Bold)", "Tinos", "Bold"],["Tinos (Italic)", "Tinos", "Italic"],["Tinos (Bold Italic)", "Tinos", "Bold Italic"],["Titan One (Regular)", "Titan One", "Regular"],["Titillium Web (Thin)", "Titillium Web", "Thin"],["Titillium Web (Light)", "Titillium Web", "Light"],["Titillium Web (Regular)", "Titillium Web", "Regular"],["Titillium Web (SemiBold)", "Titillium Web", "SemiBold"],["Titillium Web (Bold)", "Titillium Web", "Bold"],["Titillium Web (Black)", "Titillium Web", "Black"],["Titillium Web (Thin Italic)", "Titillium Web", "Thin Italic"],["Titillium Web (Light Italic)", "Titillium Web", "Light Italic"],["Titillium Web (Italic)", "Titillium Web", "Italic"],["Titillium Web (SemiBold Italic)", "Titillium Web", "SemiBold Italic"],["Titillium Web (Bold Italic)", "Titillium Web", "Bold Italic"],["Trade Winds (Regular)", "Trade Winds", "Regular"],["Trocchi (Regular)", "Trocchi", "Regular"],["Trochut (Regular)", "Trochut", "Regular"],["Trochut (Bold)", "Trochut", "Bold"],["Trochut (Italic)", "Trochut", "Italic"],["Trykker (Regular)", "Trykker", "Regular"],["Tuffy (Regular)", "Tuffy", "Regular"],["Tuffy (Bold)", "Tuffy", "Bold"],["Tuffy (Italic)", "Tuffy", "Italic"],["Tuffy (BoldItalic)", "Tuffy", "BoldItalic"],["Tulpen One (Regular)", "Tulpen One", "Regular"],["Tulpen (Light)", "Tulpen", "Light"],["Ubuntu Condensed (Regular)", "Ubuntu Condensed", "Regular"],["Ubuntu Monospaced (Regular)", "Ubuntu Monospaced", "Regular"],["Ubuntu Monospaced (Bold)", "Ubuntu Monospaced", "Bold"],["Ubuntu Monospaced (Italic)", "Ubuntu Monospaced", "Italic"],["Ubuntu Monospaced (Bold Italic)", "Ubuntu Monospaced", "Bold Italic"],["Ubuntu Mono (Regular)", "Ubuntu Mono", "Regular"],["Ubuntu Mono (Bold)", "Ubuntu Mono", "Bold"],["Ubuntu Mono (Italic)", "Ubuntu Mono", "Italic"],["Ubuntu Mono (Bold Italic)", "Ubuntu Mono", "Bold Italic"],["Ubuntu (Light)", "Ubuntu", "Light"],["Ubuntu (Regular)", "Ubuntu", "Regular"],["Ubuntu (Medium)", "Ubuntu", "Medium"],["Ubuntu (Bold)", "Ubuntu", "Bold"],["Ubuntu (Light Italic)", "Ubuntu", "Light Italic"],["Ubuntu (Italic)", "Ubuntu", "Italic"],["Ubuntu (Medium Italic)", "Ubuntu", "Medium Italic"],["Ubuntu (Bold Italic)", "Ubuntu", "Bold Italic"],["Ultra (Regular)", "Ultra", "Regular"],["Uncial Antiqua (Regular)", "Uncial Antiqua", "Regular"],["Underdog (Regular)", "Underdog", "Regular"],["Unica One (Regular)", "Unica One", "Regular"],["UnifrakturCook (Bold)", "UnifrakturCook", "Bold"],["UnifrakturMaguntia (Book)", "UnifrakturMaguntia", "Book"],["Unkempt (Regular)", "Unkempt", "Regular"],["Unkempt (Bold)", "Unkempt", "Bold"],["Unlock (Regular)", "Unlock", "Regular"],["Unna (Regular)", "Unna", "Regular"],["URW Bookman L (Light)", "URW Bookman L", "Light"],["URW Bookman L (Demi Bold)", "URW Bookman L", "Demi Bold"],["URW Bookman L (Light Italic)", "URW Bookman L", "Light Italic"],["URW Bookman L (Demi Bold Italic)", "URW Bookman L", "Demi Bold Italic"],["URW Chancery L (Medium Italic)", "URW Chancery L", "Medium Italic"],["URW Gothic L (Book)", "URW Gothic L", "Book"],["URW Gothic L (Demi)", "URW Gothic L", "Demi"],["URW Gothic L (Book Oblique)", "URW Gothic L", "Book Oblique"],["URW Gothic L (Demi Oblique)", "URW Gothic L", "Demi Oblique"],["URW Palladio L (Roman)", "URW Palladio L", "Roman"],["URW Palladio L (Bold)", "URW Palladio L", "Bold"],["URW Palladio L (Italic)", "URW Palladio L", "Italic"],["URW Palladio L (Bold Italic)", "URW Palladio L", "Bold Italic"],["Vampiro One (Regular)", "Vampiro One", "Regular"],["Varela Round (Regular)", "Varela Round", "Regular"],["Varela (Regular)", "Varela", "Regular"],["Vast Shadow (Regular)", "Vast Shadow", "Regular"],["Vesper Libre (Regular)", "Vesper Libre", "Regular"],["Vesper Libre (Medium)", "Vesper Libre", "Medium"],["Vesper Libre (Bold)", "Vesper Libre", "Bold"],["Vesper Libre (Heavy)", "Vesper Libre", "Heavy"],["Vibur (Medium)", "Vibur", "Medium"],["Vidaloka  (Regular)", "Vidaloka ", "Regular"],["Viga (Regular)", "Viga", "Regular"],["Voces (Regular)", "Voces", "Regular"],["Volkhov (Regular)", "Volkhov", "Regular"],["Volkhov (Bold)", "Volkhov", "Bold"],["Volkhov (Italic)", "Volkhov", "Italic"],["Volkhov (Bold Italic)", "Volkhov", "Bold Italic"],["Vollkorn (Regular)", "Vollkorn", "Regular"],["Vollkorn (Bold)", "Vollkorn", "Bold"],["Vollkorn (Italic)", "Vollkorn", "Italic"],["Vollkorn (Bold Italic)", "Vollkorn", "Bold Italic"],["Voltaire (Regular)", "Voltaire", "Regular"],["VT323 (Regular)", "VT323", "Regular"],["Waiting for the Sunrise (Regular)", "Waiting for the Sunrise", "Regular"],["Wallpoet (Regular)", "Wallpoet", "Regular"],["Walter Turncoat (Regular)", "Walter Turncoat", "Regular"],["Warnes (Regular)", "Warnes", "Regular"],["Wellfleet (Regular)", "Wellfleet", "Regular"],["Wendy One (Regular)", "Wendy One", "Regular"],["Wire One (Regular)", "Wire One", "Regular"],["Yanone Kaffeesatz (Extra Light)", "Yanone Kaffeesatz", "Extra Light"],["Yanone Kaffeesatz (Light)", "Yanone Kaffeesatz", "Light"],["Yanone Kaffeesatz (Regular)", "Yanone Kaffeesatz", "Regular"],["Yanone Kaffeesatz (Bold)", "Yanone Kaffeesatz", "Bold"],["Yellowtail (Regular)", "Yellowtail", "Regular"],["Yeseva One (Regular)", "Yeseva One", "Regular"],["Yesteryear (Regular)", "Yesteryear", "Regular"],["Zeyada (Regular)", "Zeyada", "Regular"]];
fonts_with_style_index=search([font_with_style],fonts_with_style,1)[0];

font_string=str(fonts_with_style[fonts_with_style_index][1], ":style=", fonts_with_style[fonts_with_style_index][2]);
echo(fonts_with_style[fonts_with_style_index]);
function get_first_value_in_list(list,default=[],index=0)=(
    list[index] != [] ? list[index] : (
    index == len(list) ? default : (
    get_first_value_in_list(list,default,index+1)
)));

// is there really a hole?
has_lug=(lug_length > 0 && lug_width > 0 && bar_width > 0);
lug_length2=has_lug?lug_length:0;
lug_width2=has_lug?lug_width:0;

// offset work around
offset_delta_workaround_fonts=["Butcherman", "Cabin Sketch", "Condiment", "Eater", "Finger Paint","Fredericka the Great", "Irish Grover", "Jim Nightshade", "Julee", "Londrina Outline", "Londrina Sketch", "MedievalSharp", "Merienda", "Merienda One", "Modern Antiqua", "Mr De Haviland", "Permanent Marker", "Rye", "Salsa", "Skranji", "Spicy Rice", "The Girl Next Door"];
offset_delta_workaround = search([font_name],offset_delta_workaround_fonts)[0]!=[];

extra_weight_fonts=[["Abril Fatface", 0],["Aclonica", 0],["Acme", 0],["Advent Pro", 0],["Akronim", 0],["Aladin", 0],
["Alegreya", 0],["Alegreya Sans", 0],["Alegreya SC", 0],["Alex Brush", 20],["Alfa Slab One", 0],
["Alice", 0],["Amaranth", 0],["Architects Daughter", 15],["Archivo Black", 0],["Archivo Narrow", 0],
["Arimo", 0],["Arvo", 0],["Asap", 0],["Astloch", 0],["Asul", 0],["Averia Libre", 0],["Averia Serif Libre", 0],
["Bangers", 0],["Basic", 0],["Belgrano", 10],["Berkshire Swash", 0],["Bigshot One", 0],["Bilbo Swash Caps", 20],
["Black Ops One", 0],["Bonbon", 15],["Bowlby One SC", 0],["Brawler", 0],["Bubblegum Sans", 0],
["Butterfly Kids", 15],["Cabin Condensed", 0],["Caesar Dressing", 0],["Cagliostro", 0],["Calligraffitti", 20],
["Capriola", 0],["Carter One", 0],["Changa One", 0],["Chango", 0],["Chelsea Market", 0],
["Cherry Cream Soda", 0],["Chewy", 0],["Chicle", 0],["Chivo", 0],["Clicker Script", 15],["Coming Soon", 20],
["Concert One", 0],["Condiment", 0],["Cookie", 0],["Courgette", 0],["Covered By Your Grace", 0],
["Crimson Text", 0],["Dancing Script", 0],["Dhurjati", 0],["Doppio One", 0],["Dosis", 0],["Droid Sans", 0],
["Eagle Lake", 0],["Electrolize", 0],["Emilys Candy", 0],["Encode Sans", 0],["Encode Sans Compressed", 0],
["Euphoria Script", 0],["Exo", 0],["Exo 2", 0],["Faster One", 0],["Federo", 0],["Finger Paint", 0],
["Fjord One", 0],["Fontdiner Swanky", 0],["Freckle Face", 0],["Fruktur", 0],["Gabriela", 0],["Geo", 0],
["Germania One", 0],["Give You Glory", 20],["Gloria Hallelujah", 0],["Goudy Bookletter 1911", 0],
["Graduate", 0],["Grand Hotel", 0],["Great Vibes", 15],["Griffy", 15],["Hanalei Fill", 0],["Happy Monkey", 15],
["Henny Penny", 0],["Hind", 0],["IM Fell English SC", 0],["Indie Flower", 15],["Irish Grover", 0],["Italianno", 10],
["Jacques Francois Shadow", 0],["Jolly Lodger", 0],["Josefin Slab", 0],["Joti One", 0],["Judson", 0],
["Just Another Hand", 0],["Kalam", 0],["Kameron", 0],["Karma", 0],["Kavoon", 0],
["Knewave", 0],["Kranky", 10],["Kristi", 10],["Laila", 0],["Lakki Reddy", 0],
["Lato", 0],["Leckerli One", 0],["Ledger", 0],["Lekton", 0],["Lemon One", 0],["Liberation Sans", 0],
["Libre Caslon Text", 0],["Life Savers", 0],["Lilita One", 0],["Lily Script One", 0],["Limelight", 0],["Lobster", 0],
["Lobster Two", 0],["Londrina Outline", 20],["Londrina Shadow", 20],["Londrina Solid", 0],["Lora", 0],
["Love Ya Like A Sister", 0],["Loved by the King", 15],["Lovers Quarrel", 10],["Luckiest Guy", 0],["Lusitana", 0],
["Macondo", 0],["Macondo Swash Caps", 0],["Mako", 0],["Marck Script", 0],["Margarine", 0],["Marko One", 0],
["Maven Pro", 0],["McLaren", 0],["MedievalSharp", 0],["Merienda One", 0],["Merriweather", 0],["Mervale Script", 0],
["Metal Mania", 0],["Metrophobic", 0],["Milonga", 0],["Miltonian Tattoo", 0],["Miss Fajardose", 15],["Molle", 0],
["Montez", 0],["Montserrat", 0],["Mr De Haviland", 12],["Mystery Quest", 0],["Neucha", 0],["New Rocker", 0],
["Niconne", 0],["Nosifer", 0],["Nothing You Could Do", 15],["Noto Sans Oriya", 0],["Noto Serif", 0],
["Nova Square", 0],["Nunito", 0],["Old Standard TT", 0],["Oleo Script", 0],["Oleo Script Swash Caps", 0],
["Orbitron", 0],["Oregano", 0],["Orienta", 0],["Original Surfer", 0],["Oswald", 0],["Over the Rainbow", 10],
["Overlock", 0],["Oxygen", 0],["Pacifico", 0],["Paprika", 0],["Parisienne", 10],["Patrick Hand SC", 0],
["Paytone One", 0],["Peralta", 0],["Permanent Marker", 0],["Piedra", 0],["Pirata One", 0],["Play", 0],
["Playball", 0],["Poetsen One", 0],["Poppins", 0],["Press Start 2P", 0],["Princess Sofia", 0],["PT Mono", 0],
["Qwigley", 0],["Racing Sans One", 0],["Raleway", 0],["Rancho", 0],["Ranga", 0],["Ribeye", 0],["Roboto", 0],
["Roboto Condensed", 0],["Roboto Slab", 0],["Rochester", 0],["Rock Salt", 0],["Rubik One", 0],["Sail", 0],
["Salsa", 0],["Sansita One", 0],["Sarina", 0],["Satisfy", 0],["Schoolbell", 0],["Seaweed Script", 0],
["Sevillana", 10],["Shadows Into Light", 10],["Shadows Into Light Two", 10],["Share", 0],["Six Caps", 0],
["Skranji", 0],["Source Sans Pro", 0],["Spicy Rice", 0],["Stardos Stencil", 0],["Stoke", 0],["Syncopate", 0],
["Teko", 0],["Terminal Dosis", 0],["The Girl Next Door", 15],["Tillana", 0],["Timmana", 0],["Titillium Web", 0],
["Ubuntu", 0],["Ultra", 0],["Underdog", 10],["UnifrakturCook", 0],["UnifrakturMaguntia", 0],["Vampiro One", 0],
["Vidaloka", 0],["Viga", 0],["Voces", 0],["Volkhov", 0],["Vollkorn", 0],["Voltaire", 0],["Waiting for the Sunrise", 10],
["Wallpoet", 0],["Wellfleet", 0],["Wendy One", 0],["Yellowtail", 0],["Yesteryear", 0],["Zeyada", 15]];

extra_weight_font_index = search([font_name],extra_weight_fonts)[0];
extra_weight=extra_weight_font_index!=[]?extra_weight_fonts[extra_weight_font_index][1]:0;


// baseline work around
font_vshifts=[["Qwigley", 1]];
font_vshifts_index=search([font_name],font_vshifts)[0];
text_vshift=(font_vshifts_index!=[]?font_vshifts[font_vshifts_index][1]:3)*(is_vertical()?1:-1);

lug_radius=min(lug_length2,lug_width2)/2;
lug_x_offset=lug_length2/2-lug_radius;
lug_y_offset=lug_width2/2-lug_radius;
text_to_write=(
    line5!="" ? [line1,line2,line3,line4,line5] : (
    line4!="" ? [line1,line2,line3,line4] : (
    line3!="" ? [line1,line2,line3] : (
    line2!="" ? [line1,line2] : (
    line1!="" ? [line1] : 
    [str(fonts_with_style[fonts_with_style_index][1], " (", fonts_with_style[fonts_with_style_index][2], ")")]
)))));
    
final_bar_width=bar_width > 0 ? max(bar_width,lug_width2+2*outer_margin_width+2*inner_margin_width+2*border_width+font_size*line_spacing*(len(text_to_write)-1)) : 0;

echo(font_string=font_string);
font_narrow_widen_factor=1+font_narrow_widen/100;
core_bar_width=max(0.01,final_bar_width-2*lug_radius-2*inner_margin_width-2*outer_margin_width-2*border_width);

max_thickness=max(bar_thickness,outline_thickness,text_thickness);

final_bar_shift=bar_shift*font_size/60;
final_bar_length_trim=bar_length_trim*font_size/60-(bar_style=="surround_text"?hole_extra_margin:0);

// start building keychain 
rotate([0,0,is_vertical()?-90:0])
make_keychain();

echo(get_extruders());

function get_extruders()=[
    bar_extruder,
    border_extruder,
    outline_extruder,
    text_extruder
];
function get_colors()=[bar_color,border_color,outline_color,text_color];
function get_heights()=[
    [0,bar_thickness],
    [bar_thickness,border_thickness],
    [bar_thickness,outline_thickness],
    [outline_thickness,text_thickness]
];

module make_keychain()
extrude_layers(heights=get_heights(), extruders=get_extruders(), colors=get_colors())
{
    // 1. outer margin (height: 3, color: khaki)
    plate(inner_margin_width+outer_margin_width+border_width);

    // 2. border (height: 4.2, color: black)
    border();

    // 3. outline (height: 3.6, color: lightgray)
    outline_write_text(font_outline_width);
    
    // 4. text (height: 4.2, color: darkred)
    write_text();
}

module plate(surrounding)
union()
{
    glyph_coalesce(surrounding);    
    
    bar_plate(surrounding);
    
    outline_write_text((bar_style=="surround_text"?surrounding:0)+font_outline_width);
}

module border()
if(bar_style=="surround_text")
difference()
{
    plate(inner_margin_width+border_width);
    plate(inner_margin_width);
}
else
difference()
{
    bar_plate(inner_margin_width+border_width);
    bar_plate(inner_margin_width);
}
    
module glyph_coalesce(surrounding)
if(glyph_coalesce > 0 && glyph_coalesce_strategy != "off")
assign(delta=font_size/300*glyph_coalesce)
offset(delta=-delta-.01)
offset(delta=delta,chamfer=true)
union()
{
    if(glyph_coalesce_strategy=="bar and glyphs")
    bar_plate(surrounding);
     
    outline_write_text(font_outline_width);
}

module outline_write_text(delta)
if(font_outline_width>0)
offset(r=font_outline_style=="rounded"?delta:[],delta=font_outline_style!="rounded"?delta:[],chamfer=font_outline_style=="chamfer",$fn=15)
write_text();
else
write_text();

module write_text()
offset(delta=font_size/500*(font_weight+extra_weight),chamfer=offset_delta_workaround)
translate([lug_text_distance,font_size/8*text_vshift])
rotate([0,0,is_vertical()?90:0])
scale([font_narrow_widen_factor,1,1])
for(n=[0:len(text_to_write)-1])

translate([0,font_size*line_spacing*((len(text_to_write)-1)/2-n)])
text(str(text_to_write[n]), font_size, font_string, direction=writing_direction, halign=is_vertical()?"center":"left", valign="baseline", spacing=character_spacing, $fn=50);

module bar_plate(surrounding)
if(final_bar_width>0)
translate([0,final_bar_shift])
offset(r=lug_radius+surrounding+hole_extra_margin,$fn=30)
core_bar();

module core_bar()
hull()
union()
{
    union()
    {
        intersection()
        {
            translate([0,-core_bar_width/2,0])
            square([1000, core_bar_width]);

            hull()
            for(y=[-1000,1000])
            translate([final_bar_length_trim-font_size/6,y,0])
            write_text();
        }
    
        if(!has_lug||lug_style=="plate")
        translate([0,-core_bar_width/2])
        square([.1,core_bar_width]); 
    }
    
    if(has_lug)
    {
        if(lug_style=="pointy")
        translate([-lug_length2/2-lug_x_offset,-lug_y_offset])
        square([lug_length2/2+lug_radius+.1,2*lug_y_offset]); 
        else // if(lug_style=="plate")
        translate([-lug_length2/2-lug_x_offset,-core_bar_width/2])
        square([lug_length2/2+lug_radius+.1,core_bar_width]); 
    }
}

module make_lug()
translate([-lug_length2/2,final_bar_shift,0])
hull()
for(x=[-lug_x_offset,lug_x_offset])
for(y=[-lug_y_offset,lug_y_offset])
translate([x,y])
circle(r=lug_radius,$fn=30);

module make_delete_this_part()
assign(text_to_write=["This extruder is not used!", "Please delete this part", "after creating!"])
translate([0,0,-1])
linear_extrude(0.6, convexity=10)
for(n=[0:len(text_to_write)-1])
translate([0,-font_size*1.5*n,0])

text(text_to_write[n], size=font_size, halign="center");

module check_to_create_layer(extruders, num_layers)
difference()
{
    union()
    {
        translate([-lug_length2-lug_x_offset-lug_radius-inner_margin_width-outer_margin_width-border_width,max(final_bar_width/2+final_bar_shift,font_size*.7)+font_outline_width+inner_margin_width+outer_margin_width+border_width,0])
        make_ruler(50,ruler_unit)
        write_text();

        children();
    }
    
    make_delete_this_part();
    
    translate([0,0,-.1])
    linear_extrude(1000, convexity=10)
    make_lug();
}


module make_ruler(max_length, unit, font)
%union()
{
    %color("white")
    translate([0,0,-.005])
    linear_extrude(.01,convexity=20)
    ruler_intersection(10)
    translate([-10/3,0,0])
    square([max_length*unit+10*.85,10]);   
    
    %color("black")
    translate([0,0,-.015])
    linear_extrude(.03,convexity=20)
    ruler_intersection(10)
    for(mark=[0:10*max_length])
    translate([mark*unit/10-0.1/2,0,0])
    if(mark%10==0)
    {
        square([10/50,10/2]);
        
        translate([10/50,10*.6,0])
		
        text(str(mark/10),size=10/3,halign="center");
    }
    else if(mark%5==0)
    square([10/50,10/3]);
    else
    square([10/50,10/5]);
}

module ruler_intersection(width)
intersection()
{
    hull()
    union()
    {
        for(y=[-font_size,font_size])
        translate([1.2*width+final_bar_length_trim+2*inner_margin_width+2*border_width+2*outer_margin_width,y,0])
        write_text();
        
        translate([-.5*width,0,0])
        square(width+5);
    }

    children();   
}

// adjust layer for multi extrusion
// create entry [start_height, end_height]
function color_layers(heights)=[
    for(layer=heights) 
    [max(0,min(layer[0],layer[1]-minimal_color_layer_thickness)), layer[1]]
]; 

function is_part(extruder)=search([which_extruder], [extruder, monochrome_part()])[0]!=[];
function used_layers(extruders, num_layers)=[for(n=[0:num_layers-1]) extruders[n] ];

module extrude_layers(heights,extruders,colors)



assign(layers=color_layers(heights))
assign(num_layers=min(len(layers),len(extruders),len(colors),$children))
check_to_create_layer(extruders, num_layers)
for(i=[0:num_layers-1])
assign(h0=layers[i][0],h1=layers[i][1])
assign(thickness=h1-h0)
if(thickness>0.001 && is_part(extruders[i]))
difference()
{
    union()
    {
		randomnum = rands(5,1,4);
        color(colors[i])
        translate([0,0,h0])
        linear_extrude(height = thickness, convexity = 10)
        children(i);    
    
        // difference workaround
        translate([0,0,-10])
        cube(1,center=true);
    }
    
    for(j=[i+1:1:num_layers-1])
    assign(h2=layers[j][0])
    {
        if(h2-h1<-0.001)
        translate([0,0,h2==h0?h2-.1:h2])
        color(colors[i])
        linear_extrude(height = h1-h2 + .2, convexity = 10)
        children(j);
    }
    
    // difference workaround
    translate([0,0,-10])
    cube(2,center=true);
}

// preview[view:south, tilt:top]
