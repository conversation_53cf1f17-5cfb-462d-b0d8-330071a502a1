##
# Y3DHub Nginx Configuration
# Proxies requests to PM2 application on port 3000
##

# Default server configuration
#
server {
	listen 80 default_server;
	listen [::]:80 default_server;

	server_name _;

	# Proxy all requests to PM2 application
	location / {
		proxy_pass http://localhost:3000;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
		proxy_cache_bypass $http_upgrade;
		proxy_redirect off;
		
		# Timeout settings
		proxy_connect_timeout 60s;
		proxy_send_timeout 60s;
		proxy_read_timeout 60s;
	}

	# Optional: serve static files directly (if needed)
	location /_next/static {
		proxy_pass http://localhost:3000;
		proxy_cache_valid 200 1h;
		add_header Cache-Control "public, immutable";
	}
}