# Azure File Share Setup Guide

This guide will help you mount your Azure File Share (`y3dshare/shared`) to your WSL Linux server.

## Quick Setup

Run the automated setup script:

```bash
./scripts/azure/setup-azure-fileshare.sh
```

## Manual Setup

### 1. Get Your Storage Account Key

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Storage Accounts** > **y3dshare**
3. Go to **Security + networking** > **Access keys**
4. Copy **key1** or **key2**

### 2. Create Mount Point

```bash
sudo mkdir -p /mnt/azure-fileshare
sudo chown $USER:$USER /mnt/azure-fileshare
```

### 3. Create Credentials File

```bash
sudo tee /etc/azure-fileshare-credentials > /dev/null << EOF
username=y3dshare
password=YOUR_STORAGE_ACCOUNT_KEY_HERE
EOF

sudo chmod 600 /etc/azure-fileshare-credentials
sudo chown root:root /etc/azure-fileshare-credentials
```

### 4. Mount the File Share

```bash
sudo mount -t cifs \
    //y3dshare.file.core.windows.net/shared \
    /mnt/azure-fileshare \
    -o credentials=/etc/azure-fileshare-credentials,dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8
```

### 5. Test the Mount

```bash
# Check if mounted
mountpoint /mnt/azure-fileshare

# Test write access
echo "Test file" > /mnt/azure-fileshare/test.txt
cat /mnt/azure-fileshare/test.txt
rm /mnt/azure-fileshare/test.txt

# Check available space
df -h /mnt/azure-fileshare
```

### 6. Add to /etc/fstab for Automatic Mounting

```bash
echo "//y3dshare.file.core.windows.net/shared /mnt/azure-fileshare cifs credentials=/etc/azure-fileshare-credentials,dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8 0 0" | sudo tee -a /etc/fstab
```

## Usage Examples

### Backup Y3DHub Data

```bash
# Create backup directory
mkdir -p /mnt/azure-fileshare/y3dhub-backups

# Backup database
mysqldump -u root -p y3dhub_prod > /mnt/azure-fileshare/y3dhub-backups/y3dhub-$(date +%Y%m%d).sql

# Backup application files
tar -czf /mnt/azure-fileshare/y3dhub-backups/y3dhub-app-$(date +%Y%m%d).tar.gz /home/<USER>/Y3DHub_production
```

### Share STL Files

```bash
# Create STL sharing directory
mkdir -p /mnt/azure-fileshare/stl-files

# Copy STL files to share
cp /home/<USER>/Y3DHub_production/output_stl/*.stl /mnt/azure-fileshare/stl-files/
```

### Log Storage

```bash
# Create logs directory
mkdir -p /mnt/azure-fileshare/logs

# Archive old logs
cp /var/log/y3dhub/*.log /mnt/azure-fileshare/logs/
```

## Troubleshooting

### Mount Failed

```bash
# Check if cifs-utils is installed
sudo apt install cifs-utils

# Check credentials file permissions
ls -la /etc/azure-fileshare-credentials

# Check mount with verbose output
sudo mount -t cifs //y3dshare.file.core.windows.net/shared /mnt/azure-fileshare -o credentials=/etc/azure-fileshare-credentials,dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8,vers=3.0 -v
```

### Permission Issues

```bash
# Fix ownership
sudo chown -R $USER:$USER /mnt/azure-fileshare

# Check mount options
mount | grep azure-fileshare
```

### Unmount

```bash
# Unmount the file share
sudo umount /mnt/azure-fileshare

# Force unmount if busy
sudo umount -f /mnt/azure-fileshare
```

## Useful Commands

```bash
# Check mount status
mountpoint /mnt/azure-fileshare

# View mount details
mount | grep azure-fileshare

# Check disk usage
df -h /mnt/azure-fileshare

# List contents
ls -la /mnt/azure-fileshare

# Remount all fstab entries
sudo mount -a
```

## Security Notes

- The credentials file contains sensitive information and should have 600 permissions
- Only root should own the credentials file
- Consider using Azure AD authentication for enhanced security in production
- Regularly rotate storage account keys

## Integration with Y3DHub

You can integrate the Azure File Share with your Y3DHub workflows:

1. **Backup automation**: Schedule regular backups to Azure
2. **STL file sharing**: Store generated STL files for external access
3. **Log archival**: Archive application logs to Azure
4. **Configuration sync**: Share configuration files between environments
