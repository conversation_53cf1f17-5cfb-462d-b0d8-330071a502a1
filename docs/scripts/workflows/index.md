# Workflow Scripts

Detailed documentation for scripts that drive the primary order‑to‑print pipeline.

- [cleanup-shipped-tasks.ts](cleanup-shipped-tasks.ts.md) – clean up tasks based on order status.
- [complete-shipped-print-tasks.ts](complete-shipped-print-tasks.ts.md) – mark tasks completed once orders ship.
- [handle-merged-orders.ts](handle-merged-orders.ts.md) – update orders merged in ShipStation.
- [order-sync.ts](order-sync.ts.md) – comprehensive CLI for syncing orders.
- [populate-print-queue.ts](populate-print-queue.ts.md) – generate print tasks from orders.
- [populate-print-queue-v2.ts](populate-print-queue-v2.ts.md) – newer variant of queue generation.
- [print-tasks.ts](print-tasks.ts.md) – manage lifecycle of print tasks.
- [sync-historical-orders.ts](sync-historical-orders.ts.md) – fetch all historical orders.
- [sync-orders.ts](sync-orders.ts.md) – primary ShipStation sync.
