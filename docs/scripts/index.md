# Y3DHub Operational Scripts

This area documents the many scripts used to run and maintain Y3DHub. Each subfolder contains documentation for a related group of scripts.

- [workflows](workflows/index.md) – core order and print‑queue processing tools.
- [main_workflow](main_workflow/index.md) – shell wrappers orchestrating daily jobs.
- [ai](ai/index.md) – AI related helpers.
- [git](git/index.md) – scripts that assist with branch management.
- [orders](orders/index.md) – order synchronization helpers.
- [db](db/index.md) – database utilities and backups.
- [database_migrations](database_migrations/index.md) – SQL migrations.
- [migrations](migrations/index.md) – additional migration helpers.
- [maintenance](maintenance/index.md) – utilities for keeping data consistent.
- [seeding](seeding/index.md) – populate the database with sample data.
- [diagnostics](diagnostics/index.md) – scripts for inspecting system state.
- [utils](utils/index.md) – assorted utility scripts.

A few scripts have standalone pages in this folder:

- [workflow.sh](workflow.sh.md) – main orchestrator called by cron.
- [utils.ts](utils.ts.md) – placeholder CLI entry for future utilities.
