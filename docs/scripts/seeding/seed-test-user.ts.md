# seed-test-user.ts Documentation

## Overview

The `seed-test-user.ts` script is a utility designed to create a test user in the Y3DHub application's database. This is useful for development and testing environments to quickly set up a user account without manual registration.

## Execution

The script is executed using `tsx` (a TypeScript runner) and requires environment variables to be loaded, typically via `dotenv/config`.

```bash
# Ensure .env file is present or environment variables are set
npx tsx src/scripts/seed-test-user.ts
```

Or, using the `run-with-env.sh` utility script:

```bash
./scripts/utils/run-with-env.sh tsx src/scripts/seed-test-user.ts
```

## Command-Line Options

This script does not accept direct command-line arguments. Its behavior is configured through environment variables.

## Environment Variables

- `SEED_TEST_EMAIL`: (Optional) The email address for the test user. Defaults to `<EMAIL>` if not set.
- `SEED_TEST_PASSWORD`: (Optional) The raw password for the test user. Defaults to `Test1234` if not set.
- Database connection variables (e.g., `DATABASE_URL`) must be set for Prisma Client to connect to the database.

## Key Features

- **Creates a Test User**: Inserts a new user record into the `User` table.
- **Password Hashing**: Uses the `hashPassword` utility (from `src/lib/server-only/auth-password`) to securely hash the password before storing it.
- **Configurable Credentials**: Allows overriding default email and password via environment variables.
- **Idempotency**: If a user with the specified email already exists, the Prisma `create` operation will likely fail due to unique constraints on the email field. The script does not explicitly check for existing users before attempting creation.
- **Error Handling**: Catches and logs database errors, exits with non-zero status on failure
- **Security**: Does not expose sensitive information in error messages

## Output

- Logs the email of the user being seeded.
- On successful creation, logs a confirmation message with the new user's ID and email (e.g., `✅ Created user: { id: 'some-uuid', email: '<EMAIL>' }`).
- On failure, logs an error message (e.g., `❌ Error seeding user: ...`) and exits with a non-zero status code.
- Includes suggestions for common error scenarios (e.g., database connection issues, duplicate email errors)

## Prerequisites

- Node.js and npm/yarn installed.
- `tsx` installed (`npm install -g tsx` or as a project dependency).
- Project dependencies installed (`npm install` or `yarn install`).
- A configured `.env` file with database connection details and optionally `SEED_TEST_EMAIL` and `SEED_TEST_PASSWORD`.
- The database schema must be up-to-date (Prisma migrations applied).

## Use Cases

- Setting up a fresh development environment.
- Populating a database for automated testing scenarios.
- Quickly creating a user for manual testing or demonstration purposes.

## Notes

- Ensure that the provided password meets any password complexity requirements enforced by the application or database, although the script itself does not validate this.
- This script is intended for development/testing and should not be run in a production environment without careful consideration, as it might create unintended user accounts.
- Consider adding a check for existing users before attempting creation in future versions
- For production-like environments, use proper user management interfaces rather than this seeding script
