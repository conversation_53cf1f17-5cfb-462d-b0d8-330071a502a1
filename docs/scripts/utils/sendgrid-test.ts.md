# Script: sendgrid-test.ts

## Table of Contents

- [Purpose](#purpose)
- [Location](#location)
- [Dependencies](#dependencies)
- [Environment Variables Required](#environment-variables-required)
- [Execution](#execution)
- [Functionality](#functionality)
- [Output](#output)

## Purpose

The `sendgrid-test.ts` script is a utility designed to test the SendGrid email integration for the Y3DHub application. It sends a predefined test email to a specified recipient to confirm that the email sending mechanism via SendGrid is configured and functioning correctly.

## Location

`src/scripts/sendgrid-test.ts`

## Dependencies

- **`dotenv/config`**: To load environment variables from a `.env` file.
- **`../lib/email/send-email`**: Specifically, the `sendEmail` function which encapsulates the logic for sending emails using SendGrid.

## Environment Variables Required

For the script to run successfully, the following environment variables must be set:

- `SENDGRID_API_KEY`: Your SendGrid API key (used by the underlying `sendEmail` function).
- `SENDGRID_TEST_TO` **or** `SENDGRID_TO_EMAIL`:
  - `SENDGRID_TEST_TO`: The primary email address to which the test email will be sent.
  - `SENDGRID_TO_EMAIL`: A fallback email address if `SENDGRID_TEST_TO` is not defined. The script will throw an error if neither is set.
- `SENDGRID_FROM_EMAIL`: The email address that will appear as the sender of the test email.

## Execution

The script is run using `tsx` or a similar Node.js TypeScript runner. Ensure the necessary environment variables are available in your shell or `.env` file.

```bash
# Ensure required SendGrid environment variables are set
npm run script -- src/scripts/sendgrid-test.ts
# or
yarn script src/scripts/sendgrid-test.ts
```

## Functionality

1. Loads environment variables.
2. Determines the recipient email address (`to`) by checking `process.env.SENDGRID_TEST_TO` first, then `process.env.SENDGRID_TO_EMAIL`.
3. Retrieves the sender email address (`from`) from `process.env.SENDGRID_FROM_EMAIL`.
4. If no recipient email address is found, it throws an error and exits.
5. Defines a static subject: `"SendGrid integration test"`.
6. Defines static plain text content: `"Hello from y3dhub via SendGrid!"`.
7. Logs a message indicating the email address it's attempting to send to.
8. Calls the `sendEmail({ to, from, subject, text })` function from `@lib/email/send-email`.
9. If successful, it logs the status code returned by SendGrid (e.g., `202` for accepted).
10. If an error occurs during sending, it logs the error message and exits the process with code 1.

## Output

- **Console Logs**:
  - `Sending email to [<EMAIL>]...`
  - On success: `Email sent, status code: [statusCode]` (e.g., `Email sent, status code: 202`)
  - On failure: `Failed to send email: [errorDetails]`
- **Email**: If successful, the specified recipient will receive an email with the predefined subject and text.

This script provides a quick and straightforward way to validate the SendGrid setup within the project.
