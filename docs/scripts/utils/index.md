# Utility Scripts

Assorted helpers used during development and operations.

- [ai-usage-stats.ts](ai-usage-stats.ts.md) – generate daily AI usage report.
- [backup-print-tasks.ts](backup-print-tasks.ts.md) – create JSON backup of tasks.
- [check-ai-logs.ts](check-ai-logs.ts.md) – summarize AI log entries.
- [check-amazon-urls.ts](check-amazon-urls.ts.md) – reconcile Amazon URLs.
- [check-logging.ts](check-logging.ts.md) – verify log output configuration.
- [clean.ts](clean.ts.md) – remove caches and legacy files.
- [cleanup-codebase.ts](cleanup-codebase.ts.md) – pre-defined file cleanup routine.
- [clean-unused-files.ts](clean-unused-files.ts.md) – remove unused files via list.
- [find-amazon-orders-with-missing-colors.ts](find-amazon-orders-with-missing-colors.ts.md) – detect missing color data.
- [find-orders-without-print-tasks.ts](find-orders-without-print-tasks.ts.md) – locate orders lacking tasks.
- [fix-order-3672783640-colors.ts](fix-order-3672783640-colors.ts.md) – one-off color fix.
- [fix-status-mismatch.ts](fix-status-mismatch.ts.md) – reconcile order status between DB and ShipStation.
- [fix-stl-render-status.sh](fix-stl-render-status.sh.md) – SQL migration and Prisma client regen.
- [manual-shipstation-update.ts](manual-shipstation-update.ts.md) – manually push personalization data to ShipStation.
- [reprocess-all-orders.ts](reprocess-all-orders.ts.md) – reprocess all pending orders.
- [run-with-env.sh](run-with-env.sh.md) – run a command with `.env` loaded.
- [send-notification-email.ts](send-notification-email.ts.md) – send a plain text email from CLI.
- [sendgrid-test.ts](sendgrid-test.ts.md) – verify SendGrid configuration.
- [start-litellm.sh](start-litellm.sh.md) – run LiteLLM services via Docker Compose.
