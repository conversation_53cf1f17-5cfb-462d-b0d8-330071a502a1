# Script: send-notification-email.ts

## Table of Contents

- [Purpose](#purpose)
- [Location](#location)
- [Key Features](#key-features)
- [Dependencies](#dependencies)
- [Environment Variables Required](#environment-variables-required)
- [Execution](#execution)
- [Command-Line Arguments](#command-line-arguments)
- [Output](#output)

## Purpose

The `send-notification-email.ts` script is a general-purpose command-line utility for sending plain text emails. It allows a user to specify the recipient, subject, and body of an email through command-line arguments, and then uses the application's standard email sending library (`@lib/email/send-email`) to dispatch the email.

This script is useful for ad-hoc notifications, testing email functionality, or simple automated email tasks.

## Location

`src/scripts/utils/send-notification-email.ts`

## Key Features

- **Dynamic Email Content**: Recipient, subject, and body are specified via CLI arguments.
- **Simple Interface**: Uses `minimist` for straightforward argument parsing.
- **Leverages Core Email Library**: Relies on the centralized `sendEmail` function, ensuring consistency with how other parts of the application send emails (e.g., using SendGrid via environment configurations).

## Dependencies

- **`minimist`**: For parsing command-line arguments.
- **`../../lib/email/send-email` (`sendEmail` function)**: The core function responsible for constructing and sending emails, likely integrating with a service like SendGrid.

## Environment Variables Required

While not directly read by this script, the underlying `sendEmail` function (from `@lib/email/send-email`) will require certain environment variables to be set for it to function correctly. These typically include:

- `SENDGRID_API_KEY`: The API key for the SendGrid service.
- `SENDGRID_FROM_EMAIL`: The default email address to be used as the sender if not overridden by the `sendEmail` function's implementation (this script doesn't pass a `from` argument, so the default is used).

Ensure these are configured in your `.env` file or shell environment.

## Execution

The script is designed to be run from the command line using `tsx` (or a similar TypeScript runner configured in `package.json` like `npm run script`).

```bash
# General syntax
npm run script -- src/scripts/utils/send-notification-email.ts --to <email> --subject "<subject>" --body "<body>"

# Example
npm run script -- src/scripts/utils/send-notification-email.ts \
  --to <EMAIL> \
  --subject "Important Update" \
  --body "Please be advised of an important update to our services."
```

## Command-Line Arguments

The script uses `minimist` to parse the following command-line arguments:

- `--to <email>` (Required): The email address of the recipient.
- `--subject "<subject>"` (Required): The subject line of the email. Should be enclosed in quotes if it contains spaces.
- `--body "<body>"` (Required): The plain text content of the email. Should be enclosed in quotes if it contains spaces or special characters.

If any of these arguments are missing, the script will print a usage message to `stderr` and exit with code 1.

## Output

- **Console Logs**:
  - If arguments are missing: Prints a usage example to `stderr`.
  - Before sending: `Sending notification email to [<EMAIL>]...`
  - On success: `Email sent successfully.`
  - On failure: `Failed to send email: [errorDetails]` (and exits with code 1).
- **Email**: If successful, the specified recipient will receive an email with the provided subject and body from the application's default sender address.
