# Git Workflow Scripts

Utilities to help manage project branches.

- [merge-staging-to-main.sh](merge-staging-to-main.sh.md) – merge `staging` into `main`.
- [reset-to-staging.sh](reset-to-staging.sh.md) – reset local `staging` to remote.
- [update-alpha-from-staging.sh](update-alpha-from-staging.sh.md) – update `alpha` branch.
- [update-staging-from-main.sh](update-staging-from-main.sh.md) – merge `main` into `staging`.
