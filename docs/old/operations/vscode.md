---
title: vscode
last-reviewed: 2025-04-18
maintainer: TBD
---

# VS Code Configuration

This folder contains shared VS Code settings for the Y3DHub project.

## Included Files

- **settings.json**: Project-specific editor settings
- **extensions.json**: Recommended extensions for Y3DHub development
- **launch.json**: Debugging configurations for Next.js

## Purpose

These settings ensure a consistent development experience across the team, with standardized:

- Code formatting (Prettier)
- Linting rules (ESLint)
- Import ordering
- AI tool configurations
- TypeScript settings
- Debugging profiles

## For New Team Members

When opening this project for the first time in VS Code:

1. Install the recommended extensions (a prompt should appear)
2. Ensure ESLint and Prettier are properly configured
3. Use the debugging configurations for efficient Next.js debugging

These shared settings help maintain code quality and consistency across the Y3DHub order management system.
