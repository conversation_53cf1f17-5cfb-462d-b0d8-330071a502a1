---
title: Y3DHub Documentation
last-reviewed: 2025-04-18
maintainer: TBD
---

# Y3DHub Documentation

A modern order management and print queue system for 3D printing businesses. This documentation is organized into sections to help you navigate setup, development, integrations, and more.

## Getting Started

- [Getting Started](getting-started.md)

## Guides

- [Development Guide](guides/development.md)
- [Code Structure](guides/code-structure.md)
- [IDE Guidelines](guides/ide-guidelines.md)
- [Linting Guide](guides/linting-guide.md)
- [Code Review Process](guides/new-code-review.md)
- [Testing Guide](guides/testing.md)

## Reference

- [API Reference](reference/api-reference.md)
- [Command Reference](reference/commands.md)
- [Database Schema](reference/database.md)
- [Local LLM Prompting](reference/local-llm-prompting.md)

## Integrations

- [Amazon Color Processing](integrations/amazon-color-processing.md)
- [eBay Color Mapping](integrations/ebay.md)
- [ShipStation Sync](integrations/shipstation-sync.md)

## Operations

- [Edge Runtime Setup](operations/edge-runtime.md)
- [VS Code Setup](operations/vscode.md)
- Scripts:
  - [Crontab Configuration](operations/scripts/crontab.md)
  - [Cleanup Tasks](operations/scripts/cleanup.md)

## Troubleshooting

- [Troubleshooting](troubleshooting.md)

## Roadmap & Planning

- [Post-Refactoring TODO](todo.md)
- [Recommendations](recommendations.md)
- [Future Improvements](future-improvements.md)
