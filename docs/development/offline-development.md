# Offline Development Guide

This guide is specifically designed for AI-powered development tools like **Codex AI**, **Codeium**, and other offline-first IDEs that can't connect to external services.

## 🚀 Quick Start

```bash
# Setup offline environment (one-time)
npm run db:offline:setup

# Start development with SQLite
npm run dev:offline

# Build project offline
npm run build:offline
```

## 📁 Offline Environment Setup

### Environment Variables

The offline environment uses `.env.offline` which includes:

- **SQLite Database**: `file:./dev.db` (no external MySQL needed)
- **Mock API Keys**: Non-functional keys for development
- **Disabled External APIs**: All external services mocked/disabled
- **Local Ports**: Development server on port 3002

### Database

- **Provider**: SQLite (file-based, no server required)
- **Location**: `./dev.db` in project root
- **Schema**: Identical to production MySQL schema
- **Sample Data**: Pre-populated with realistic test data

## 🛠️ Available Commands

```bash
# Offline Development
npm run dev:offline          # Start Next.js with SQLite
npm run build:offline        # Build project with SQLite
npm run db:offline:setup     # Setup SQLite database

# Database Management
npx prisma studio --schema prisma/schema.sqlite.prisma  # Database GUI
npx prisma db push --schema prisma/schema.sqlite.prisma # Apply schema changes

# Testing
npm run test                 # Run tests (uses SQLite automatically)
npm run lint                 # Code linting (works offline)
npm run type-check          # TypeScript validation
```

## 🎯 For AI Development Tools

### Codex AI Setup

1. **Clone/Open Project**:

   ```bash
   git clone <your-repo>
   cd Y3DHub_staging
   ```

2. **Setup Offline Environment**:

   ```bash
   npm install
   npm run db:offline:setup
   ```

3. **Start Development**:

   ```bash
   npm run dev:offline
   ```

4. **AI Context**: The `.cursorrules` file provides Y3DHub-specific context for AI assistance.

### Benefits for Offline AI

- ✅ **No External Dependencies**: Everything runs locally
- ✅ **Fast Setup**: SQLite database ready in seconds
- ✅ **Complete Feature Set**: All UI/logic works offline
- ✅ **Real Data**: Sample orders, products, and print tasks
- ✅ **Type Safety**: Full TypeScript support
- ✅ **AI Context**: Rich project context in `.cursorrules`

## 📊 Sample Data Included

The offline database includes:

- **Orders**: 50+ sample orders from various marketplaces
- **Products**: Common 3D printing products with SKUs
- **Print Tasks**: Various status examples and customizations
- **Customers**: Sample customer data for testing
- **Users**: Test user accounts for authentication

## 🔧 Configuration Files

### Key Files

- `.env.offline`: Offline environment variables
- `prisma/schema.sqlite.prisma`: SQLite database schema
- `scripts/setup-offline-dev.sh`: Offline setup script
- `.cursorrules`: AI development context

### Database Schema

The SQLite schema mirrors production exactly:

- All tables and relationships preserved
- Enum values maintained
- Indexes for performance
- Foreign key constraints

## 🚦 Development Workflow

### 1. AI-Assisted Development

```bash
# Start offline environment
npm run dev:offline

# Open in browser: http://localhost:3002
# AI tools can now assist with full context
```

### 2. Database Changes

```bash
# Modify prisma/schema.sqlite.prisma
# Apply changes:
npx prisma db push --schema prisma/schema.sqlite.prisma
```

### 3. Testing Changes

```bash
# Run linting
npm run lint

# Type checking
npm run type-check

# Build verification
npm run build:offline
```

## 🎨 UI/UX Development

All UI components work offline:

- **Print Queue**: Sample print tasks with various statuses
- **Orders Page**: Paginated order listings with filters
- **Order Details**: Complete order information and customizations
- **Print Task Management**: Status updates and verification flows
- **Dashboard**: Charts and metrics (with sample data)

## 🔍 Debugging

### Database Issues

```bash
# Reset database
rm ./dev.db
npm run db:offline:setup

# Check database contents
npx prisma studio --schema prisma/schema.sqlite.prisma
```

### Build Issues

```bash
# Skip database validation
SKIP_DATABASE_VALIDATION=true npm run build

# Offline build
npm run build:offline
```

### Development Issues

```bash
# Clear Next.js cache
rm -rf .next

# Clean install
rm -rf node_modules package-lock.json
npm install
```

## 🛡️ Security Notes

- **Mock API Keys**: All API keys in `.env.offline` are non-functional
- **Local Only**: No external connections made
- **Sample Data**: All data is fictional for development only
- **No Secrets**: No real credentials stored in offline config

## 📈 Performance

Offline development provides:

- **Fast Startup**: No external API calls or network delays
- **Instant Builds**: No database connection requirements
- **Quick Tests**: SQLite in-memory operations
- **Local Everything**: All assets and data local

## 🎓 AI Context

The `.cursorrules` file provides AI tools with:

- **Project Overview**: Y3DHub's purpose and architecture
- **Code Patterns**: Established patterns and conventions
- **Business Logic**: 3D printing workflow understanding
- **API Structure**: Internal API design patterns
- **Database Schema**: Complete data model context

## 🚀 Ready for AI Development!

Your Y3DHub project is now fully configured for offline AI-assisted development. AI tools like Codex can:

- Understand your codebase structure
- Suggest appropriate code patterns
- Work with your database schema
- Maintain coding standards
- Provide context-aware assistance

**Start coding with AI assistance!** 🤖✨
