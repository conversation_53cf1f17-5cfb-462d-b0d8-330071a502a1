# Development Guide

This section covers coding standards, testing procedures, and contribution guidelines for Y3DHub.

## ⚠️ CRITICAL: Development Workflow

**🚨 NEVER WORK DIRECTLY ON THE MAIN BRANCH 🚨**

### Mandatory Branch Workflow

1. **ALWAYS** work on the `staging` branch for all development
2. **ALWAYS** commit changes to `staging` first
3. **ALWAYS** push to `staging` to trigger staging deployment (port 3001)
4. **ONLY** merge `staging` → `main` for production deployment (port 3000)

```bash
# ✅ CORRECT WORKFLOW
git checkout staging           # Work on staging
# ... make changes ...
git add . && git commit -m "..."
git push origin staging        # Triggers staging deployment

# After testing staging:
git checkout main
git merge staging              # Merge tested changes
git push origin main           # Triggers production deployment
```

## ✅ Code Quality Status

The Y3DHub codebase is currently **100% clean** with:

- ✅ **0 ESLint errors**
- ✅ **0 ESLint warnings**
- ✅ **Full TypeScript 5.8.3 compatibility**
- ✅ **Optimized VS Code configuration**
- ✅ **Prettier formatting setup**

## Contents

- [Coding Standards](coding-standards.md)
- [Testing](testing.md)
- [Contribution Guidelines](contributing.md)
- [Environment Setup](environment-setup.md)
