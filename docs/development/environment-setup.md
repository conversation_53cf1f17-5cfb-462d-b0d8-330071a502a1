# Development Environment Setup

Follow these steps to bootstrap a local Y3DHub environment.

1. **Install dependencies**

   ```bash
   npm ci
   ```

2. **Create your `.env` file**

   ```bash
   cp .env.example .env
   # edit .env for your environment:
   # • To use MySQL (default): set DATABASE_URL to your MySQL connection string
   # • To use local SQLite (offline): comment out or remove DATABASE_URL in .env
   ```

3. **Initialize Prisma schema**

   ```bash
   bash scripts/init-prisma-schema.sh
   ```

4. **Generate the Prisma client and run initial migrations**

   ```bash
   npm run db:generate
   npm run db:migrate
   ```

5. **(Optional) Prepare a SQLite database for local testing**

   ```bash
   npm run db:test:setup
   ```

6. **Run the development server**
   ```bash
   npm run dev
   ```

The `db:test:setup` step uses `prisma/schema.sqlite.prisma` to create `test.db` which is handy for running tests without MySQL. See [Testing](testing.md) for details.

**Note:** Ensure that `test.db` is added to your `.gitignore` file to avoid accidentally committing it to version control.
