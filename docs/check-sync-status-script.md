# ShipStation Sync Status Checker

## Overview

The `check-shipstation-sync-status.ts` script verifies that all outstanding ShipStation orders are properly synced with the local database and have the appropriate print tasks created.

## Purpose

This script helps ensure data integrity by:

1. **Fetching all outstanding orders** from ShipStation API (default: `awaiting_shipment`)
2. **Checking database sync** to verify orders exist locally
3. **Validating item counts** between ShipStation and database
4. **Verifying print tasks** exist for each order
5. **Providing actionable recommendations** for any issues found

## Usage

### Basic Commands

```bash
# Check all awaiting_shipment orders
npm run check:sync-status

# Check orders from last 3 days only
npm run check:sync-status -- --days 3

# Check different order status
npm run check:sync-status -- --status "on_hold"

# Dry run (check only, no actions)
npm run check:sync-status -- --dry-run

# Verbose output
npm run check:sync-status -- --verbose
```

### Command Options

| Option | Description | Default |
|--------|-------------|---------|
| `-d, --days <number>` | Limit to orders created in last N days | All time |
| `-s, --status <status>` | Order status to check | `awaiting_shipment` |
| `--dry-run` | Check only, no actions | `false` |
| `-v, --verbose` | Enable verbose output | `false` |

## Sync Status Types

The script categorizes orders into these sync statuses:

### ✅ SYNCED
- Order exists in database
- Item counts match between ShipStation and database
- Has adequate print tasks (at least 1 per item)

### ❌ MISSING_FROM_DB
- Order exists in ShipStation but not in local database
- **Action**: Run sync to import missing orders

### ⚠️ ITEM_MISMATCH
- Order exists in both systems
- Item counts don't match between ShipStation and database
- **Action**: Manual review or re-sync required

### 🚫 NO_PRINT_TASKS
- Order exists in database
- No print tasks have been created
- **Action**: Run populate print queue

### ⚡ PARTIAL_TASKS
- Order exists in database
- Has some print tasks but fewer than expected
- **Action**: Run populate print queue

## Sample Output

```
📊 SYNC STATUS REPORT
================================================================================

📈 SUMMARY:
   Total Orders Checked: 58
   ✅ Fully Synced: 55
   ❌ Missing from DB: 0
   ⚠️  Item Mismatch: 3
   🚫 No Print Tasks: 0
   ⚡ Partial Tasks: 0

🚨 ORDERS WITH ISSUES (3):
================================================================================

📋 Order: 3690219638 (SS ID: 278605202)
   Status: awaiting_shipment
   Sync Status: ITEM_MISMATCH
   In Database: Yes
   DB Order ID: 32091
   Items: SS=2, DB=1
   Print Tasks: 1
   Issues:
     - Item count mismatch: SS=2, DB=1

💡 RECOMMENDATIONS:

⚠️  Orders with Item Mismatches (3):
   These may need manual review or re-sync
```

## Automated Recommendations

The script provides specific recommendations based on issues found:

### Missing Orders
```bash
# Run sync to import missing orders
npm run sync-orders -- --mode recent --days-back 7
```

### Missing Print Tasks
```bash
# Run populate print queue for orders needing tasks
npm run populate-queue -- --limit 50
```

### Item Mismatches
- Manual review required
- May need individual order re-sync
- Check for ShipStation order modifications

## Integration with Workflow

This script is perfect for:

1. **Daily health checks** - Run before processing orders
2. **Post-sync validation** - Verify sync operations completed successfully
3. **Troubleshooting** - Identify specific orders with issues
4. **Monitoring** - Regular checks to ensure system integrity

## Common Use Cases

### Daily Health Check
```bash
# Quick check of recent orders
npm run check:sync-status -- --days 1
```

### Post-Sync Validation
```bash
# After running sync, verify everything is correct
npm run sync-orders
npm run check:sync-status -- --days 3
```

### Troubleshooting Missing Tasks
```bash
# Find orders without print tasks
npm run check:sync-status -- --status awaiting_shipment
```

### Full System Audit
```bash
# Check all outstanding orders (may take longer)
npm run check:sync-status
```

## Error Handling

The script includes robust error handling for:

- ShipStation API failures
- Database connection issues
- Missing data scenarios
- Network timeouts

## Performance Notes

- Uses pagination for large datasets
- Efficient database queries with proper includes
- Batched processing to avoid memory issues
- Configurable limits via days filter

## Related Scripts

- `npm run sync-orders` - Sync orders from ShipStation
- `npm run populate-queue` - Create print tasks
- `npm run check:amazon-urls` - Check Amazon URL processing
- `npm run full-workflow` - Complete processing workflow
