# Getting Started with Y3DHub

Follow these steps to bootstrap a local development environment. For an overview of the system architecture see the [System Overview](SYSTEM_OVERVIEW.md).

1. **Clone the repository**
   ```bash
   git clone https://github.com/y3dltd/Y3DCore.git
   cd Y3DCore
   ```
2. **Install dependencies**
   ```bash
   npm install
   ```
3. **Create your `.env` file**
   ```bash
   cp .env.example .env
   # edit .env with your database and API credentials
   ```
4. **Generate the Prisma client and run migrations**
   ```bash
   npx prisma generate
   npx prisma migrate dev
   ```
5. **Start the development server**
   ```bash
   npm run dev
   ```

For testing and contribution guidelines see the [Development Guide](development/index.md).
