# Y3DHub GitHub Actions Workflows

## ⚠️ CRITICAL: Branch Workflow Rules

**🚨 NEVER WORK DIRECTLY ON THE MAIN BRANCH 🚨**

### Mandatory Development Workflow

1. **ALWAYS** work on the `staging` branch for all development
2. **ALWAYS** commit changes to `staging` first
3. **ALWAYS** push to `staging` to trigger staging deployment (port 3001)
4. **ONLY** merge `staging` → `main` for production deployment (port 3000)

```bash
# ✅ CORRECT WORKFLOW
git checkout staging           # Work on staging
# ... make changes ...
git add . && git commit -m "..."
git push origin staging        # Triggers staging deployment (port 3001)

# After testing staging:
git checkout main
git merge staging              # Merge tested changes
git push origin main           # Triggers production deployment (port 3000)
```

**⚡ This workflow is MANDATORY for all Y3DHub development ⚡**

---

## Overview

This document describes the GitHub Actions workflow configuration for Y3DHub's automated deployment and quality control system.

## Workflow Summary

| Event                 | Workflows Triggered | Purpose                                 |
| --------------------- | ------------------- | --------------------------------------- |
| **Push to `staging`** | 1 workflow          | Fast staging deployment                 |
| **Push to `main`**    | 2 workflows         | Production deployment + dev environment |
| **Pull Request**      | 2 workflows         | Quality checks before merge             |

---

## Workflow Details

### 🚀 Deployment Workflows

#### 1. Deploy to Staging (`deploy-staging.yml`)

**Triggers:** Push to `staging` branch, manual dispatch

**Target:** `/home/<USER>/Y3DHub_staging_deploy` (Port 3001)

**Process:**

1. Clone/update code from staging branch
2. **Uses pre-configured static `.env` file** with staging settings:
   - `PORT=3001`
   - `NODE_ENV="staging"`
   - `NEXTAUTH_URL="http://localhost:3001"`
   - Uses development database (shared)
3. Install dependencies (`npm ci`)
4. Generate Prisma client
5. Run database migrations
6. Build application (`npm run build`)
7. Restart PM2 process: `Y3DHub_Staging` on port 3001
8. Health check on `http://localhost:3001/`

#### 2. Deploy to Production (`deploy-production.yml`)

**Triggers:** Push to `main` branch, manual dispatch

**Target:** `/home/<USER>/Y3DHub_production` (Port 3000)

**Process:**

1. Clone/update code from main branch
2. **Uses pre-configured static `.env` file** with production settings:
   - `PORT=3000`
   - `NODE_ENV="production"`
   - `NEXTAUTH_URL="http://localhost:3000"`
   - Database: `y3dhub_prod` (production database)
3. Install dependencies (`npm ci`)
4. Generate Prisma client
5. Run database migrations
6. Build application (`npm run build`)
7. Restart PM2 process: `Y3DHub_Production` using `npm run start-prod-http-3000`
8. Health check on `http://localhost:3000/`

---

### 🔍 Quality Control Workflows

#### 3. CI (`ci.yml`)

**Triggers:** Pull requests to `main` or `staging` branches

**Purpose:** Basic build and type checking

**Process:**

1. Install dependencies
2. TypeScript type checking (`npm run type-check`)
3. Build verification with SQLite schema
4. Uses CI-safe environment variables

#### 4. Code Quality & Build Checks (`claude-code.yml`)

**Triggers:** Pull requests (opened, edited, reopened, synchronized)

**Purpose:** Advanced code quality analysis

**Process:**

1. Install dependencies
2. TypeScript checking (continue on error)
3. ESLint analysis (`npm run lint`)
4. Advanced code quality metrics
5. PR comment integration

---

### 🛠️ Development Workflows

#### 5. Codespaces Prebuild (`codespaces-prebuild.yml`)

**Triggers:** Push to `main`, PRs to `main`, manual dispatch

**Purpose:** Prebuilds GitHub Codespaces environment

**Process:**

1. Sets up development environment
2. Installs dependencies
3. Configures development tools

---

## Environment Configuration

### Development Environment

- **Directory:** `/home/<USER>/Y3DHub_staging`
- **Port:** 3002
- **Database:** `y3dhub` (development)
- **Purpose:** Local development and testing

### Staging Environment

- **Directory:** `/home/<USER>/Y3DHub_staging_deploy`
- **Port:** 3001
- **Database:** `y3dhub` (shared with development)
- **Purpose:** Testing staging branch features

### Production Environment

- **Directory:** `/home/<USER>/Y3DHub_production`
- **Port:** 3000
- **Database:** `y3dhub_prod` (production)
- **Purpose:** Live production system

---

## File Management

### Static Environment Configuration

Each deployment environment uses its own pre-configured static `.env` file:

- **Development:** `/home/<USER>/Y3DHub_staging/.env` (PORT=3002)
- **Staging:** `/home/<USER>/Y3DHub_staging_deploy/.env` (PORT=3001)
- **Production:** `/home/<USER>/Y3DHub_production/.env` (PORT=3000)

### Simplified Deployment Approach

No dynamic file copying or environment variable manipulation during deployment:

- ✅ Static `.env` files with correct configurations for each environment
- ✅ No complex `sed` commands or file copying logic
- ✅ Reduced deployment complexity and potential for errors
- ✅ Clear separation of environment configurations

---

## Workflow Optimization

### Before Optimization (4+ workflows per push)

❌ Every branch push triggered multiple redundant workflows:

- CI, Code Quality, Deployment, Codespaces Prebuild

### After Optimization (1-2 workflows per push)

✅ **Staging pushes:** Only deployment workflow
✅ **Main pushes:** Deployment + lightweight prebuild
✅ **Pull requests:** All quality checks run here

---

## Security Features

### SSH Deployment

All deployments use secure SSH connections with:

- Private key authentication (`PROD_SSH_KEY`)
- Custom port configuration (`PROD_PORT`)
- Host verification (`PROD_HOST`)

### Environment Isolation

- Separate directories for each environment
- Environment-specific database configurations
- Port isolation (3000, 3001, 3002)

### Secret Management

- GitHub Secrets for SSH credentials
- Environment files copied from secure development directory
- No secrets committed to repository

---

## Monitoring & Health Checks

### Deployment Health Checks

Each deployment includes automatic health checks:

```bash
curl -f http://localhost:{PORT}/ || echo "⚠️ Health check failed, but deployment completed"
```

### PM2 Process Management

- Automatic process restart with zero-downtime deployment
- Named processes for easy identification:
  - `Y3DHub_Production` (port 3000)
  - `Y3DHub_Staging` (port 3001)
  - Development runs directly (port 3002)

---

## Troubleshooting

### Common Issues

#### 1. Missing Environment Files

**Error:** `DATABASE_URL not found`
**Solution:** Ensure static `.env` files exist in each deployment directory:

- Development: `/home/<USER>/Y3DHub_staging/.env`
- Staging: `/home/<USER>/Y3DHub_staging_deploy/.env`
- Production: `/home/<USER>/Y3DHub_production/.env`

#### 2. Port Conflicts

**Error:** `EADDRINUSE: address already in use`
**Solution:** PM2 automatically stops existing processes before restart

#### 3. Database Migration Failures

**Error:** Migration timeout or connection issues
**Solution:** Check database connectivity and permissions

### Manual Deployment

All workflows support manual triggering via `workflow_dispatch`:

1. Go to Actions tab in GitHub
2. Select the desired workflow
3. Click "Run workflow"
4. Choose branch and run

---

## Best Practices

### Branch Management

1. **Development:** Work in `/home/<USER>/Y3DHub_staging` (port 3002)
2. **Testing:** Push to `staging` branch → auto-deploy to port 3001
3. **Production:** Merge to `main` branch → auto-deploy to port 3000

### Quality Control

1. All changes go through PR review
2. CI checks must pass before merge
3. Staging testing before production deployment

### Environment Management

1. Keep `.env` files secure and up-to-date
2. Test environment changes in staging first
3. Monitor health checks after deployment

---

## Configuration Files

- **Workflows:** `.github/workflows/*.yml`
- **Environment:** `.env` (gitignored)
- **Database:** `prisma/schema.prisma`
- **PM2:** Auto-managed by deployment scripts

---

_Last updated: January 25, 2025_
_Workflow optimization completed: 4 workflows → 1-2 workflows per deployment_
_Deployment simplification completed: Static .env files, no dynamic copying_
