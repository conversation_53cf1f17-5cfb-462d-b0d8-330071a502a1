# Crontab Configuration for Y3DHub

## Overview

This document outlines the crontab configuration for the Y3DHub system across different deployment environments.

## Deployment Structure

### Directory Layout

- **Development**: `/home/<USER>/Y3DHub_staging` (development workspace)
- **Staging**: `/home/<USER>/Y3DHub_staging_deploy` (staging deployment - port 3001)
- **Production**: `/home/<USER>/Y3DHub_production` (production deployment - port 3000)

### GitHub Actions Deployment

- **Staging**: Push to `staging` branch → auto-deploys to staging environment
- **Production**: Push to `main` branch → auto-deploys to production environment

## Crontab Configuration

### Production Environment (`/home/<USER>/Y3DHub_production`)

```bash
# Y3DHub Production Cron Jobs
# Edit with: crontab -e

# STL Render Worker - Every 2 minutes
*/2 * * * * cd /home/<USER>/Y3DHub_production && npm run worker:stl >> /var/log/y3dhub/stl-worker.log 2>&1

# Full Workflow - Every 5 minutes (sync orders, populate queue, process tasks)
*/5 * * * * cd /home/<USER>/Y3DHub_production && npm run full-workflow >> /var/log/y3dhub/full-workflow.log 2>&1

# Sync Status Check - Every 30 minutes (health check)
*/30 * * * * cd /home/<USER>/Y3DHub_production && npm run check:sync-status >> /var/log/y3dhub/sync-status.log 2>&1

# Amazon URL Check - Every hour
0 * * * * cd /home/<USER>/Y3DHub_production && npm run check:amazon-urls >> /var/log/y3dhub/amazon-urls.log 2>&1

# Cleanup Print Tasks - Daily at 2 AM
0 2 * * * cd /home/<USER>/Y3DHub_production && npm run cleanup-print-tasks >> /var/log/y3dhub/cleanup.log 2>&1

# System Cleanup - Daily at 3 AM
0 3 * * * cd /home/<USER>/Y3DHub_production && npm run clean >> /var/log/y3dhub/system-cleanup.log 2>&1
```

### Staging Environment (`/home/<USER>/Y3DHub_staging_deploy`)

```bash
# Y3DHub Staging Cron Jobs (for testing)
# Edit with: crontab -e

# STL Render Worker - Every 5 minutes (less frequent for staging)
*/5 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run worker:stl >> /var/log/y3dhub/staging-stl-worker.log 2>&1

# Full Workflow - Every 10 minutes (testing)
*/10 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run full-workflow >> /var/log/y3dhub/staging-full-workflow.log 2>&1

# Sync Status Check - Every hour (staging health check)
0 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run check:sync-status >> /var/log/y3dhub/staging-sync-status.log 2>&1
```

## Key Scripts Explained

### Core Production Scripts

#### `npm run worker:stl`

- **Purpose**: Processes STL rendering tasks from the queue
- **Frequency**: Every 2 minutes
- **Function**: Renders 3D models, uploads to Google Drive, updates database

#### `npm run full-workflow`

- **Purpose**: Complete order processing workflow
- **Frequency**: Every 5 minutes
- **Includes**:
  - Sync orders from ShipStation
  - Populate print queue
  - Process pending tasks
  - Update order statuses

#### `npm run check:sync-status`

- **Purpose**: Health check for data integrity
- **Frequency**: Every 30 minutes
- **Function**: Verifies ShipStation orders are synced with database

#### `npm run check:amazon-urls`

- **Purpose**: Identifies and fixes Amazon URL issues
- **Frequency**: Every hour
- **Function**: Finds orders with Amazon URLs and triggers re-processing

#### `npm run cleanup-print-tasks`

- **Purpose**: Database maintenance
- **Frequency**: Daily at 2 AM
- **Function**: Removes old/completed print tasks

#### `npm run clean`

- **Purpose**: System maintenance
- **Frequency**: Daily at 3 AM
- **Function**: Cleans temporary files, caches, logs

## Log Management

### Log Directory Structure

```
/var/log/y3dhub/
├── stl-worker.log              # STL rendering logs
├── full-workflow.log           # Complete workflow logs
├── sync-status.log             # Sync health check logs
├── amazon-urls.log             # Amazon URL processing logs
├── cleanup.log                 # Database cleanup logs
├── system-cleanup.log          # System maintenance logs
├── staging-stl-worker.log      # Staging STL logs
├── staging-full-workflow.log   # Staging workflow logs
└── staging-sync-status.log     # Staging sync logs
```

### Log Rotation Setup

```bash
# Create log rotation config: /etc/logrotate.d/y3dhub
/var/log/y3dhub/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 jayson jayson
}
```

## Installation Commands

### 1. Create Log Directory

```bash
sudo mkdir -p /var/log/y3dhub
sudo chown jayson:jayson /var/log/y3dhub
sudo chmod 755 /var/log/y3dhub
```

### 2. Install Production Crontab

```bash
# Edit crontab
crontab -e

# Add the production cron jobs from above
```

### 3. Setup Log Rotation

```bash
# Create logrotate config
sudo tee /etc/logrotate.d/y3dhub << 'EOF'
/var/log/y3dhub/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 jayson jayson
}
EOF
```

### 4. Verify Crontab

```bash
# List current cron jobs
crontab -l

# Check cron service status
sudo systemctl status cron

# View cron logs
sudo tail -f /var/log/syslog | grep CRON
```

## Monitoring and Troubleshooting

### Check Script Status

```bash
# View recent logs
tail -f /var/log/y3dhub/stl-worker.log
tail -f /var/log/y3dhub/full-workflow.log

# Check for errors
grep -i error /var/log/y3dhub/*.log

# Monitor cron execution
sudo tail -f /var/log/syslog | grep CRON
```

### Manual Script Execution

```bash
# Test STL worker
cd /home/<USER>/Y3DHub_production && npm run worker:stl

# Test full workflow
cd /home/<USER>/Y3DHub_production && npm run full-workflow

# Test sync status
cd /home/<USER>/Y3DHub_production && npm run check:sync-status
```

### Performance Monitoring

```bash
# Check system resources during cron execution
htop

# Monitor disk space
df -h

# Check database connections
ps aux | grep node
```

## Environment Variables

Ensure these environment variables are properly set in each deployment:

### Production (`/home/<USER>/Y3DHub_production/.env`)

- `DATABASE_URL` - Production database connection
- `SHIPSTATION_API_KEY` - ShipStation API credentials
- `SHIPSTATION_API_SECRET` - ShipStation API credentials
- `GOOGLE_DRIVE_*` - Google Drive API credentials
- `OPENAI_API_KEY` - OpenAI API key

### Staging (`/home/<USER>/Y3DHub_staging_deploy/.env`)

- Same as production but pointing to staging/test resources

## Security Considerations

1. **File Permissions**: Ensure cron scripts have proper permissions
2. **Log Security**: Rotate and compress logs to prevent disk space issues
3. **API Keys**: Verify environment variables are properly secured
4. **Process Monitoring**: Monitor for hung processes or excessive resource usage

## Maintenance Schedule

- **Daily**: Review logs for errors
- **Weekly**: Check disk space and log rotation
- **Monthly**: Review cron job performance and timing
- **Quarterly**: Update dependencies and review security
