#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Create a simple Python script to use the Anthropic API
cat > /tmp/claude_cli.py << 'EOF'
#!/usr/bin/env python3

import sys
import os
import anthropic
from anthropic import Anthropic

def main():
    # Get API key from environment
    api_key = os.environ.get('ANTHROPIC_API_KEY')
    if not api_key:
        print("Error: ANTHROPIC_API_KEY environment variable not set")
        sys.exit(1)
    
    # Get input from command line arguments or stdin
    if len(sys.argv) > 1:
        prompt = " ".join(sys.argv[1:])
    else:
        print("Enter your prompt (Ctrl+D to submit):")
        prompt_lines = []
        try:
            for line in sys.stdin:
                prompt_lines.append(line)
        except KeyboardInterrupt:
            sys.exit(0)
        prompt = "".join(prompt_lines)
    
    # Initialize Anthropic client
    client = Anthropic(api_key=api_key)
    
    # Call Claude
    try:
        message = client.messages.create(
            model="claude-3-opus-20240229",
            max_tokens=4000,
            temperature=0.7,
            system="You are Claude, a helpful AI assistant. Provide clear, concise, and accurate responses.",
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # Print the response
        print(message.content[0].text)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x /tmp/claude_cli.py

# Run the Claude CLI script with the appropriate API key
ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY $VENV_PATH/bin/python /tmp/claude_cli.py "$@"
