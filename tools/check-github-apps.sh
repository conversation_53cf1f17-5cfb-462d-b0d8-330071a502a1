#!/bin/bash

echo "Checking GitHub App installations for this repository..."
echo "Repository: $(gh repo view --json nameWithOwner --jq .nameWithOwner)"
echo ""

# Check if we have access to installations
echo "=== GitHub Apps Installed ==="
if command -v gh >/dev/null 2>&1; then
    # Try to list installations (requires appropriate permissions)
    echo "Attempting to list GitHub app installations..."
    gh api "/repos/$(gh repo view --json nameWithOwner --jq .nameWithOwner)/installations" 2>/dev/null || {
        echo "Unable to list installations via API. This might be due to permissions."
        echo ""
        echo "Please check manually at:"
        echo "https://github.com/$(gh repo view --json nameWithOwner --jq .nameWithOwner)/settings/installations"
    }
else
    echo "GitHub CLI not available"
fi

echo ""
echo "=== Manual Steps ==="
echo "1. Go to: https://github.com/$(gh repo view --json nameWithOwner --jq .nameWithOwner)/settings/installations"
echo "2. Find 'Gemini Code Assist' or 'Google Code Assist'"
echo "3. Click 'Configure'"
echo "4. Ensure these permissions are granted:"
echo "   - Contents: Read and write"
echo "   - Metadata: Read"
echo "   - Pull requests: Read and write"
echo "   - Issues: Read and write"
echo ""
echo "=== Alternative: Install from GitHub Marketplace ==="
echo "If the app is not installed:"
echo "1. Go to: https://github.com/marketplace"
echo "2. Search for 'Gemini Code Assist' or 'Google Code Assist'"
echo "3. Install with full permissions"
echo ""
echo "=== Repository Info ==="
gh repo view --json isPrivate,viewerPermission,owner --template '
Private: {{.isPrivate}}
Your permission: {{.viewerPermission}}
Owner: {{.owner.login}}
'
