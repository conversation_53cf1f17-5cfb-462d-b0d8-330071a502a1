#!/bin/bash

# Source environment variables
source ~/.bashrc

# Check if OpenAI Codex CLI is installed
if ! command -v codex &> /dev/null; then
    echo "OpenAI Codex CLI is not installed. Installing now..."
    npm i -g @openai/codex
fi

# Change to the project directory
cd "$(dirname "$0")"

# Define patterns or specific folders to focus on
if [[ "$1" == "print-queue" ]]; then
    echo "Starting Codex CLI with focus on print queue components..."
    codex "I'm working with the print queue system in my Y3DHub application. Help me understand the relationships between order sync, print task generation, and the UI components."
elif [[ "$1" == "order-processing" ]]; then
    echo "Starting Codex CLI with focus on order processing system..."
    codex "I'm working with the order processing system in Y3DHub. Help me understand how orders are synchronized from ShipStation and processed into print tasks."
else
    # Default mode
    echo "Starting Codex CLI in Y3DHub context..."
    codex "I'm working with Y3DHub, a Next.js application for managing 3D printing tasks and orders with ShipStation API integration, OpenAI-powered text extraction, and OpenSCAD rendering."
fi

# Provide helpful information about Codex usage
echo ""
echo "Codex CLI is now active in your terminal."
echo "You can exit the session by typing 'exit' or using Ctrl+D."
echo ""
echo "Some useful commands:"
echo "- help: Show available commands"
echo "- explain <file>: Get an explanation of a file"
echo "- edit <file>: Edit a file with AI assistance"
echo "- run <command>: Execute a command with AI assistance"
echo ""
