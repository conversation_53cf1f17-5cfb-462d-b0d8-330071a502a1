#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Create a simple Python script to use AWS Bedrock
cat > /tmp/bedrock_cli.py << 'EOF'
#!/usr/bin/env python3

import sys
import os
import json
import boto3

def main():
    # Get AWS credentials from environment
    aws_access_key = os.environ.get('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
    aws_region = os.environ.get('AWS_DEFAULT_REGION')
    
    if not aws_access_key or not aws_secret_key:
        print("Error: AWS credentials not set in environment variables")
        sys.exit(1)
    
    # Get input from command line arguments or stdin
    if len(sys.argv) > 1:
        prompt = " ".join(sys.argv[1:])
    else:
        print("Enter your prompt (Ctrl+D to submit):")
        prompt_lines = []
        try:
            for line in sys.stdin:
                prompt_lines.append(line)
        except KeyboardInterrupt:
            sys.exit(0)
        prompt = "".join(prompt_lines)
    
    # Set up the Bedrock client
    bedrock_runtime = boto3.client(
        service_name='bedrock-runtime',
        region_name=aws_region,
        aws_access_key_id=aws_access_key,
        aws_secret_access_key=aws_secret_key
    )
    
    # Default to Claude model, but allow selection via MODEL env var
    model_id = os.environ.get('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
    
    # Ensure the model is available in the region
    # EU-West-2 has different model availability than US regions
    available_models = {
        'eu-west-2': [
            'anthropic.claude-3-sonnet-20240229-v1:0',
            'anthropic.claude-3-haiku-20240307-v1:0',
            'amazon.titan-text-express-v1',
            'cohere.command-text-v14'
        ]
    }
    
    # Check if the model is available in the region
    if aws_region in available_models and model_id not in available_models[aws_region]:
        print(f"Warning: Model {model_id} may not be available in region {aws_region}")
        print(f"Available models in {aws_region}: {', '.join(available_models[aws_region])}")
        # Fall back to a model that's definitely available
        if available_models.get(aws_region):
            model_id = available_models[aws_region][0]
            print(f"Falling back to {model_id}")

    
    try:
        # Prepare the request based on the model
        if 'anthropic' in model_id:
            # Claude model
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 4000,
                "temperature": 0.7,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
        elif 'amazon.titan' in model_id:
            # Amazon Titan model
            request_body = {
                "inputText": prompt,
                "textGenerationConfig": {
                    "maxTokenCount": 4000,
                    "temperature": 0.7,
                    "topP": 0.9
                }
            }
        elif 'cohere' in model_id:
            # Cohere model
            request_body = {
                "prompt": prompt,
                "max_tokens": 4000,
                "temperature": 0.7
            }
        elif 'meta' in model_id:
            # Meta Llama model
            request_body = {
                "prompt": prompt,
                "max_gen_len": 4000,
                "temperature": 0.7
            }
        else:
            print(f"Unsupported model: {model_id}")
            sys.exit(1)
        
        # Invoke the model
        response = bedrock_runtime.invoke_model(
            modelId=model_id,
            body=json.dumps(request_body)
        )
        
        # Parse the response based on the model
        response_body = json.loads(response['body'].read().decode('utf-8'))
        
        if 'anthropic' in model_id:
            # Claude model
            output = response_body['content'][0]['text']
        elif 'amazon.titan' in model_id:
            # Amazon Titan model
            output = response_body['results'][0]['outputText']
        elif 'cohere' in model_id:
            # Cohere model
            output = response_body['generations'][0]['text']
        elif 'meta' in model_id:
            # Meta Llama model
            output = response_body['generation']
        else:
            output = "Unsupported model response format"
        
        # Print the response
        print(output)
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x /tmp/bedrock_cli.py

# Run the Bedrock CLI script with the appropriate environment variables
AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION \
BEDROCK_MODEL_ID=${BEDROCK_MODEL_ID:-anthropic.claude-3-sonnet-20240229-v1:0} \
$VENV_PATH/bin/python /tmp/bedrock_cli.py "$@"
