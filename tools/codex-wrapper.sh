#!/bin/bash

# Source environment variables
source ~/.bashrc

# Set API key (ensure OPENAI_API_KEY is in .bashrc)
API_KEY=${OPENAI_API_KEY:-""}

# Parse command line arguments
if [[ $# -lt 1 ]]; then
  echo "Usage: $0 <file-pattern> [additional files...]"
  echo "Example: $0 'src/print-queue/*.ts' 'src/scripts/populate-print-queue*.ts'"
  exit 1
fi

# Create temp directory for file listing
TEMP_DIR=$(mktemp -d)
FILE_LIST="$TEMP_DIR/files.txt"

# Process each pattern and find matching files
for pattern in "$@"; do
  # Handle directory patterns
  if [[ -d "$pattern" ]]; then
    find "$pattern" -type f -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" >> "$FILE_LIST"
  # Handle glob patterns (*.ts)
  elif [[ "$pattern" == *\** ]]; then
    # Get the directory part and the file pattern
    dir_part=$(dirname "$pattern")
    file_part=$(basename "$pattern")
    find "$dir_part" -type f -name "$file_part" >> "$FILE_LIST"
  # Handle individual files
  elif [[ -f "$pattern" ]]; then
    echo "$pattern" >> "$FILE_LIST"
  fi
done

# Count files and estimate size
FILE_COUNT=$(wc -l < "$FILE_LIST")
echo "Found $FILE_COUNT files matching your patterns"

# Process in smaller batches if many files
MAX_BATCH=15
if [[ $FILE_COUNT -gt $MAX_BATCH ]]; then
  echo "Large number of files detected, processing in batches of $MAX_BATCH"
  BATCHES=$((($FILE_COUNT + $MAX_BATCH - 1) / $MAX_BATCH))
  
  for ((i=1; i<=BATCHES; i++)); do
    start_line=$(( (i-1) * MAX_BATCH + 1 ))
    end_line=$(( i * MAX_BATCH ))
    
    # Create batch file list
    BATCH_FILE="$TEMP_DIR/batch_$i.txt"
    sed -n "${start_line},${end_line}p" "$FILE_LIST" > "$BATCH_FILE"
    
    echo "Processing batch $i of $BATCHES..."
    
    # Open files in batch with VSCode
    FILES_TO_OPEN=$(cat "$BATCH_FILE")
    code $FILES_TO_OPEN
    
    # Open the OpenAI chat with GPT-4 for this batch
    python -c "
import webbrowser
import urllib.parse

files = []
with open('$BATCH_FILE', 'r') as f:
    files = [line.strip() for line in f.readlines()]

file_contents = {}
for file in files:
    try:
        with open(file, 'r') as f:
            file_contents[file] = f.read()
    except:
        file_contents[file] = f'Error reading {file}'

prompt = f'''I'm working on Y3DHub, a Next.js app for managing 3D printing tasks with ShipStation API integration. I've provided key files from my codebase below. Please help me understand these files and answer questions I might have.

{chr(10).join([f'File: {file}{chr(10)}```{chr(10)}{content}{chr(10)}```{chr(10)}' for file, content in file_contents.items()])}'''

encoded_prompt = urllib.parse.quote(prompt)
url = f'https://chat.openai.com/?model=gpt-4o&prompt={encoded_prompt}'
webbrowser.open(url)
"
    
    read -p "Press enter when ready to continue to the next batch..."
  done
else
  # Just open everything at once for smaller sets
  FILES_TO_OPEN=$(cat "$FILE_LIST")
  code $FILES_TO_OPEN
  
  # Open the OpenAI chat with GPT-4
  python -c "
import webbrowser
import urllib.parse

files = []
with open('$FILE_LIST', 'r') as f:
    files = [line.strip() for line in f.readlines()]

file_contents = {}
for file in files:
    try:
        with open(file, 'r') as f:
            file_contents[file] = f.read()
    except:
        file_contents[file] = f'Error reading {file}'

prompt = f'''I'm working on Y3DHub, a Next.js app for managing 3D printing tasks with ShipStation API integration. I've provided key files from my codebase below. Please help me understand these files and answer questions I might have.

{chr(10).join([f'File: {file}{chr(10)}```{chr(10)}{content}{chr(10)}```{chr(10)}' for file, content in file_contents.items()])}'''

encoded_prompt = urllib.parse.quote(prompt)
url = f'https://chat.openai.com/?model=gpt-4o&prompt={encoded_prompt}'
webbrowser.open(url)
"
fi

# Cleanup
rm -rf "$TEMP_DIR"
