#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Function to check if a model is available
check_model_availability() {
  local model_query=$1
  local result=$($VENV_PATH/bin/aider --list-models "$model_query" 2>/dev/null | grep -v "Models which match")
  if [[ -n "$result" ]]; then
    return 0  # Model found
  else
    return 1  # Model not found
  fi
}

# Function to use any custom model with Aider
use_custom_model() {
  local model_id=$1
  shift
  local provider="unknown"
  
  # Determine the provider based on the model ID
  if [[ "$model_id" == *"openai"* ]]; then
    provider="openai"
    $VENV_PATH/bin/aider --model "$model_id" --openai-api-key $OPENAI_API_KEY $CODE_EDIT_FLAGS "$@"
  elif [[ "$model_id" == *"anthropic"* ]]; then
    provider="anthropic"
    $VENV_PATH/bin/aider --model "$model_id" --anthropic-api-key $ANTHROPIC_API_KEY $CODE_EDIT_FLAGS "$@"
  elif [[ "$model_id" == *"together"* ]]; then
    provider="together"
    $VENV_PATH/bin/aider --model "$model_id" --api-key $TOGETHER_API_KEY --api-base https://api.together.xyz/v1 $CODE_EDIT_FLAGS "$@"
  elif [[ "$model_id" == *"bedrock"* ]]; then
    provider="bedrock"
    $VENV_PATH/bin/aider --model "$model_id" $CODE_EDIT_FLAGS "$@"
  elif [[ "$model_id" == *"openrouter"* ]]; then
    provider="openrouter"
    $VENV_PATH/bin/aider --model "$model_id" --openai-api-key $OPEN_ROUTER_API_KEY --openai-api-base https://openrouter.ai/api/v1 $CODE_EDIT_FLAGS "$@"
  else
    echo "Unknown provider for model: $model_id"
    echo "Using default provider (together)..."
    $VENV_PATH/bin/aider --model "$model_id" --api-key $TOGETHER_API_KEY --api-base https://api.together.xyz/v1 $CODE_EDIT_FLAGS "$@"
  fi
}

# Function to process all Markdown files in one go
process_markdown_files() {
    local MODEL_ARG="$1"
    # Check if model argument is provided
    if [[ -z "$MODEL_ARG" ]]; then
        echo "Error: No model specified for processing Markdown files."
        return 1
    fi
    # Collect all .md files into an array
    mapfile -t files < <(find docs -type f -name "*.md")
    if [ ${#files[@]} -eq 0 ]; then
        echo "No Markdown files found in docs/."
        return 0
    fi
    echo "Processing Markdown files: ${files[*]}"
    # Process all files together
    $VENV_PATH/bin/aider \
        --model "$MODEL_ARG" \
        --anthropic-api-key "$ANTHROPIC_API_KEY" \
        $CODE_EDIT_FLAGS \
        --file "${files[@]}"
}

# Default model and common flags for code editing
MODEL=${1:-claude}
shift 2>/dev/null || true

# Common flags for code editing (no commits; fully auto features enabled)
CODE_EDIT_FLAGS="--show-diffs --no-show-model-warnings --yes \
    --architect --auto-accept-architect --auto-lint --auto-test --suggest-shell-commands"

# Check if we're in a directory with files to edit
if [ $# -eq 0 ]; then
  # No files specified, look for common web files in current directory
  FILES=$(find . -maxdepth 2 -type f -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -o -name "*.html" -o -name "*.css" -o -name "*.py" -o -name "*.php" | head -n 10)
  
  if [ -n "$FILES" ]; then
    echo "Auto-detecting files to edit:"
    echo "$FILES"
    FILE_ARGS=$FILES
  else
    echo "No files specified and couldn't auto-detect. Please specify files to edit."
    echo "Usage: $0 [model] file1 file2 ..."
    echo "Available models: openai, claude, anthropic, together, alibaba, openrouter, bedrock, gemini"
    exit 1
  fi
else
  FILE_ARGS="$@"
fi

# Run Aider with the appropriate API key and model
case "$MODEL" in
  "openai" | "gpt4" | "gpt-4" | "gpt4o" | "gpt-4o")
    echo "Using OpenAI model..."
    if [[ "$MODEL" == "gpt4o" || "$MODEL" == "gpt-4o" ]]; then
      MODEL_NAME="openai/gpt-4o"
    else
      MODEL_NAME="openai/gpt-4-turbo"
    fi
    $VENV_PATH/bin/aider --model $MODEL_NAME --openai-api-key $OPENAI_API_KEY $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "anthropic" | "claude" | "claude-3" | "sonnet" | "opus" | "haiku")
    echo "Using Claude/Anthropic model..."
    
    # Select the specific Claude model based on input
    if [[ "$MODEL" == "opus" ]]; then
      MODEL_NAME="anthropic/claude-3-opus-20240229"
    elif [[ "$MODEL" == "haiku" ]]; then
      MODEL_NAME="anthropic/claude-3-5-haiku-20241022"
    else
      # Default to Sonnet
      MODEL_NAME="anthropic/claude-3-7-sonnet-20250219"
    fi
    
    $VENV_PATH/bin/aider --model $MODEL_NAME --anthropic-api-key $ANTHROPIC_API_KEY $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "together" | "llama" | "mistral" | "qwen" | "qwen3" | "qwen-coder" | "together-custom")
    echo "Using TogetherAI model..."
    
    # Check if a custom model is specified via TOGETHER_MODEL env var
    if [[ -n "$TOGETHER_MODEL" ]]; then
      echo "Using custom TogetherAI model: $TOGETHER_MODEL"
      MODEL_NAME="$TOGETHER_MODEL"
    # Select specific Together model based on input
    elif [[ "$MODEL" == "llama" ]]; then
      MODEL_NAME="together_ai/meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo"
    elif [[ "$MODEL" == "mistral" ]]; then
      MODEL_NAME="together_ai/mistralai/Mistral-Small-24B-Instruct-2501"
    elif [[ "$MODEL" == "qwen" ]]; then
      MODEL_NAME="together_ai/Qwen/Qwen2.5-72B-Instruct-Turbo"
    elif [[ "$MODEL" == "qwen3" ]]; then
      # Support for Qwen3-235B-A22B-fp8-tput
      MODEL_NAME="together_ai/Qwen/Qwen3-235B-A22B-fp8-tput"
    elif [[ "$MODEL" == "qwen-coder" ]]; then
      # Use OpenRouter for Qwen Coder 32B model
      echo "Using Qwen 2.5 Coder 32B via OpenRouter"
      $VENV_PATH/bin/aider --model "openrouter/qwen/qwen-2.5-coder-32b-instruct" --openai-api-key $OPEN_ROUTER_API_KEY --openai-api-base https://openrouter.ai/api/v1 $CODE_EDIT_FLAGS $FILE_ARGS
      return
    elif [[ "$MODEL" == "together-custom" ]]; then
      # Prompt for custom model ID
      echo "Enter custom TogetherAI model ID (e.g., together_ai/Qwen/Qwen3-235B-A22B-fp8-tput):"
      read CUSTOM_MODEL
      if [[ -n "$CUSTOM_MODEL" ]]; then
        MODEL_NAME="$CUSTOM_MODEL"
      else
        MODEL_NAME="together_ai/Qwen/Qwen2.5-72B-Instruct-Turbo"
      fi
    else
      # Default Together model
      MODEL_NAME="together_ai/Qwen/Qwen2.5-72B-Instruct-Turbo"
    fi
    
    $VENV_PATH/bin/aider --model $MODEL_NAME --api-key $TOGETHER_API_KEY --api-base https://api.together.xyz/v1 $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "alibaba")
    echo "Using Alibaba model..."
    $VENV_PATH/bin/aider --api-key $ALIBABA_API_KEY $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "openrouter")
    echo "Using OpenRouter model..."
    # Default to a good coding model on OpenRouter
    MODEL_NAME="openrouter/anthropic/claude-3-sonnet"
    $VENV_PATH/bin/aider --model $MODEL_NAME --openai-api-key $OPEN_ROUTER_API_KEY --openai-api-base https://openrouter.ai/api/v1 $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "bedrock" | "aws")
    echo "Using AWS Bedrock with Claude via Aider..."
    # Aider has direct support for Bedrock
    MODEL_NAME="bedrock/anthropic.claude-3-sonnet-20240229-v1:0"
    
    # Set AWS environment variables for Bedrock
    export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
    export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
    export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
    
    $VENV_PATH/bin/aider --model $MODEL_NAME $CODE_EDIT_FLAGS $FILE_ARGS
    ;;
    
  "google" | "gemini")
    echo "Using Google Gemini model for chat (not code editing)..."
    echo "Note: For code editing with Google models, consider using Claude or OpenAI instead."
    # Gemini doesn't work well with Aider for code editing yet
    ./gemini-wrapper.sh "$@"
    ;;
    
  "vertex")
    echo "Using Google Vertex AI for chat (not code editing)..."
    echo "Note: For code editing with Google models, consider using Claude or OpenAI instead."
    # Vertex AI doesn't work well with Aider for code editing yet
    ./vertex-wrapper.sh "$@"
    ;;
    
  "md-docs")
    echo "Processing all Markdown files in docs directory..."
    process_markdown_files "together_ai/Qwen/Qwen3-235B-A22B-fp8-tput"
    exit 0
    ;;
    
  "custom")
    echo "Using custom model..."
    echo "Enter the full model ID (e.g., together_ai/Qwen/Qwen3-235B-A22B-fp8-tput):"
    read CUSTOM_MODEL_ID
    if [[ -n "$CUSTOM_MODEL_ID" ]]; then
      # Check if model exists
      if check_model_availability "$CUSTOM_MODEL_ID"; then
        echo "Model found! Using $CUSTOM_MODEL_ID"
        use_custom_model "$CUSTOM_MODEL_ID" $FILE_ARGS
      else
        echo "Warning: Model '$CUSTOM_MODEL_ID' not found in Aider's model list."
        echo "Attempting to use it anyway..."
        use_custom_model "$CUSTOM_MODEL_ID" $FILE_ARGS
      fi
    else
      echo "No model specified. Defaulting to Claude..."
      $VENV_PATH/bin/aider --model anthropic/claude-3-7-sonnet-20250219 --anthropic-api-key $ANTHROPIC_API_KEY $CODE_EDIT_FLAGS $FILE_ARGS
    fi
    ;;
    
  *)
    # Check if the input might be a direct model ID
    if [[ "$MODEL" == *"/"* ]]; then
      echo "Detected potential model ID: $MODEL"
      # Try to use it directly
      use_custom_model "$MODEL" $FILE_ARGS
    else
      echo "Unknown model: $MODEL. Defaulting to Claude..."
      $VENV_PATH/bin/aider --model anthropic/claude-3-7-sonnet-20250219 --anthropic-api-key $ANTHROPIC_API_KEY $CODE_EDIT_FLAGS $FILE_ARGS
    fi
    ;;
esac
