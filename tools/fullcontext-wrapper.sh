#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Find important files matching the pattern, excluding node_modules
        # Limit files to avoid token overflow
        MAX_FILES=20

DEBUG_MODE=${DEBUG_MODE:-0}
if [[ "$DEBUG_MODE" == "1" ]]; then
    set -x  # Enable command tracing
    export LITELLM_LOG=DEBUG
    # This will show detailed LiteLLM debug output
fi

# Default model and common flags for code editing
MODEL=${1:-gpt-4-1}
CODE_EDIT_FLAGS="--show-diffs --no-show-model-warnings --yes --architect --auto-accept-architect"

# Function for full-context editing mode with very large context window models
full_context_mode() {
    local MODEL_ARG="$1"
    shift
    local FILE_PATTERN="$1"
    shift
    
    if [[ -z "$MODEL_ARG" ]]; then
        echo "Error: No model specified for full context mode."
        return 1
    fi
    
    # Default file pattern includes key source files
    if [[ -z "$FILE_PATTERN" ]]; then
        FILE_PATTERN="ts|tsx|md|js|jsx"
    fi
    
    # Set token management commands
    TOKEN_MANAGEMENT="
    /tokens - View token usage
    /drop path/to/file - Remove a file from context
    /clear - Clear chat history to free tokens
    "
    
    # Special case for print-queue pattern
    if [[ "$FILE_PATTERN" == "print-queue" ]]; then
        echo "Using print-queue specialized pattern"
        mapfile -t FILES < <(find src -type f \( -path "*/print-queue/*" -o -path "*/scripts/populate-print-queue*" -o -name "*print*.ts" -o -name "*task*.ts" \) | grep -v "node_modules" | grep -v "dist" | sort)
        FILE_PATTERN=""
    fi
    
    # Check if specific files were provided
    if [ $# -gt 0 ]; then
        echo "Using specified files for full context"
        FILES=("$@")
    else
        # Regular file pattern matching
        if [[ -n "$FILE_PATTERN" ]]; then
            echo "Finding key files matching: $FILE_PATTERN"
            mapfile -t FILES < <(find src docs -type f -regex ".*\.\(${FILE_PATTERN//|/\\|}\)" | grep -v "node_modules" | grep -v "dist" | sort)
        fi
    fi
    
    if [ ${#FILES[@]} -eq 0 ]; then
        echo "No matching files found."
        return 0
    fi
    
    # Count total files and estimate token count
    echo "Found ${#FILES[@]} files for full context editing"
    
    # Choose appropriate API setup based on model
    case "$MODEL_ARG" in
        "gemini-flash")
            echo "Using Gemini Flash 2.5 Pro with 1M context window"
            export GOOGLE_API_KEY="$GOOGLE_API_KEY"
            # Proper format is 'vertexai/gemini-pro' for LiteLLM
            $VENV_PATH/bin/aider \
                --model "vertexai/gemini-pro" \
                --max-chat-history-tokens 120000 \
                $CODE_EDIT_FLAGS \
                --file "${FILES[@]}"
            ;;
        "gpt4-1" | "gpt-4-1")
            echo "Using GPT-4.1 with 128k context window"
            $VENV_PATH/bin/aider \
                --model "openai/gpt-4-1106-preview" \
                --openai-api-key "$OPENAI_API_KEY" \
                --openai-organization "$OPENAI_ORGANIZATION" \
                --max-chat-history-tokens 120000 \
                $CODE_EDIT_FLAGS \
                --edit-format diff \
                --no-auto-commits \
                --file "${FILES[@]}"
            ;;
        "gpt4o" | "4o")
            echo "Using GPT-4o with 128k context window"
            $VENV_PATH/bin/aider \
                --model "openai/gpt-4o" \
                --openai-api-key "$OPENAI_API_KEY" \
                --openai-organization "$OPENAI_ORGANIZATION" \
                --max-chat-history-tokens 128000 \
                $CODE_EDIT_FLAGS \
                --file "${FILES[@]}"
            ;;
        "gpt4o-mini" | "4o-mini")
            echo "Using GPT-4o-mini with 128k context window"
            $VENV_PATH/bin/aider \
                --model "openai/gpt-4o-mini" \
                --openai-api-key "$OPENAI_API_KEY" \
                --openai-organization "$OPENAI_ORGANIZATION" \
                --max-chat-history-tokens 128000 \
                $CODE_EDIT_FLAGS \
                --file "${FILES[@]}"
            ;;
        *)
            echo "Using custom large-context model: $MODEL_ARG"
            $VENV_PATH/bin/aider \
                --model "$MODEL_ARG" \
                --max-chat-history-tokens 120000 \
                $CODE_EDIT_FLAGS \
                --file "${FILES[@]}"
            ;;
    esac
}

# Parse command line arguments
MODEL=${1:-4o-mini}
shift 2>/dev/null || true

# Parse file pattern (optional)
FILE_PATTERN=${1:-}
if [[ "$FILE_PATTERN" =~ ^[a-z0-9\|]+$ ]]; then
    shift
else
    FILE_PATTERN="ts|tsx|md|js|jsx"
fi

# Any remaining args are taken as specific files
FILE_ARGS=("$@")

# Execute full context mode
full_context_mode "$MODEL" "$FILE_PATTERN" "${FILE_ARGS[@]}"
