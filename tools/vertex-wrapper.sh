#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Create a simple Python script to use Google Vertex AI
cat > /tmp/vertex_cli.py << 'EOF'
#!/usr/bin/env python3

import sys
import os
import vertexai
from vertexai.generative_models import GenerativeModel, ChatSession
from google.cloud import aiplatform

def main():
    # Get Google Cloud credentials from environment
    credentials_path = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
    project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
    location = os.environ.get('GOOGLE_CLOUD_LOCATION', 'us-central1')
    
    if not credentials_path:
        print("Error: GOOGLE_APPLICATION_CREDENTIALS environment variable not set")
        sys.exit(1)
    
    if not project_id:
        print("Error: GOOGLE_CLOUD_PROJECT environment variable not set")
        sys.exit(1)
    
    # Get input from command line arguments or stdin
    if len(sys.argv) > 1:
        prompt = " ".join(sys.argv[1:])
    else:
        print("Enter your prompt (Ctrl+D to submit):")
        prompt_lines = []
        try:
            for line in sys.stdin:
                prompt_lines.append(line)
        except KeyboardInterrupt:
            sys.exit(0)
        prompt = "".join(prompt_lines)
    
    try:
        # Initialize Vertex AI
        vertexai.init(project=project_id, location=location)
        
        # Get the model name from environment or use default
        model_name = os.environ.get('VERTEX_MODEL_NAME', 'gemini-1.5-pro')
        
        # Create the model
        model = GenerativeModel(model_name)
        
        # Generate content
        response = model.generate_content(prompt)
        
        # Print the response
        print(response.text)
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x /tmp/vertex_cli.py

# Run the Vertex AI CLI script with the appropriate environment variables
GOOGLE_APPLICATION_CREDENTIALS=$GOOGLE_APPLICATION_CREDENTIALS \
GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-"y3dhub-ai"} \
GOOGLE_CLOUD_LOCATION=${GOOGLE_CLOUD_LOCATION:-"europe-west2"} \
VERTEX_MODEL_NAME=${VERTEX_MODEL_NAME:-"gemini-1.5-pro"} \
$VENV_PATH/bin/python /tmp/vertex_cli.py "$@"
