#!/bin/bash

# Source the environment variables from .bashrc
source ~/.bashrc

# Set the path to the virtual environment
VENV_PATH=~/ai-tools-env

# Create a simple Python script to use the Google Gemini API
cat > /tmp/gemini_cli.py << 'EOF'
#!/usr/bin/env python3

import sys
import os
import google.generativeai as genai

def main():
    # Get API key from environment
    api_key = os.environ.get('GOOGLE_API_KEY')
    if not api_key:
        print("Error: GOOGLE_API_KEY environment variable not set")
        sys.exit(1)
    
    # Configure the Gemini API
    genai.configure(api_key=api_key)
    
    # Get input from command line arguments or stdin
    if len(sys.argv) > 1:
        prompt = " ".join(sys.argv[1:])
    else:
        print("Enter your prompt (Ctrl+D to submit):")
        prompt_lines = []
        try:
            for line in sys.stdin:
                prompt_lines.append(line)
        except KeyboardInterrupt:
            sys.exit(0)
        prompt = "".join(prompt_lines)
    
    # Set up the model
    model = genai.GenerativeModel('gemini-1.5-pro')
    
    try:
        # Generate content
        response = model.generate_content(prompt)
        
        # Print the response
        print(response.text)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# Make the script executable
chmod +x /tmp/gemini_cli.py

# Run the Gemini CLI script with the appropriate API key
GOOGLE_API_KEY=$GOOGLE_API_KEY $VENV_PATH/bin/python /tmp/gemini_cli.py "$@"
