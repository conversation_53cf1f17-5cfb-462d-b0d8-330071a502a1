# Deployment Flow Test

**Test ID**: DEPLOY-TEST-$(date +%s)
**Timestamp**: $(date)
**Branch**: staging

## Test Objectives
- Verify staging deployment to `/home/<USER>/Y3DHub_staging_deploy` (port 3001)
- Verify production deployment to `/home/<USER>/Y3DHub_production` (port 3000)
- Confirm development environment remains untouched

## Expected Results
1. Staging push triggers deployment to separate staging directory
2. Main merge triggers deployment to separate production directory  
3. Development directory `/home/<USER>/Y3DHub_staging` unaffected
4. Each environment runs on correct ports with correct configurations

## Environment Verification
- Development: localhost:3002 (y3dhub database)
- Staging: localhost:3001 (y3dhub_staging database)
- Production: localhost:3000 (y3dhub_prod database)