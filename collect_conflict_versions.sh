#!/bin/bash

# <PERSON>ript to collect BASE, OURS, THEIRS, and CONFLICTED versions of a Git conflicted file.

CONFLICTED_FILE_PATH="src/lib/shipstation/api.ts"
OUTPUT_FILE="shipstation_api_conflict_data.txt"
PROJECT_ROOT="/home/<USER>/Y3DHub_staging"

# Ensure we are in the project root
cd "$PROJECT_ROOT" || { echo "Error: Could not navigate to project root: $PROJECT_ROOT"; exit 1; }

echo "Collecting versions for $CONFLICTED_FILE_PATH into $OUTPUT_FILE..."
echo "" > "$OUTPUT_FILE" # Clear or create the output file

# Get the stage information for the conflicted file
STAGE_INFO=$(git ls-files -u -- "$CONFLICTED_FILE_PATH")

if [ -z "$STAGE_INFO" ]; then
  echo "Error: Could not find stage information for $CONFLICTED_FILE_PATH."
  echo "Is the file actually in a conflicted state?"
  exit 1
fi

BASE_HASH=$(echo "$STAGE_INFO" | awk '$3 == 1 {print $2}')
OURS_HASH=$(echo "$STAGE_INFO" | awk '$3 == 2 {print $2}')
THEIRS_HASH=$(echo "$STAGE_INFO" | awk '$3 == 3 {print $2}')

if [ -z "$BASE_HASH" ] || [ -z "$OURS_HASH" ] || [ -z "$THEIRS_HASH" ]; then
  echo "Error: Could not parse all necessary blob hashes from git ls-files output."
  echo "Stage Info Found:"
  echo "$STAGE_INFO"
  exit 1
fi

echo "--- BEGIN FILE: BASE ---" >> "$OUTPUT_FILE"
echo "Version: Common Ancestor (Stage 1)" >> "$OUTPUT_FILE"
echo "Hash: $BASE_HASH" >> "$OUTPUT_FILE"
echo '```typescript' >> "$OUTPUT_FILE"
git show "$BASE_HASH" >> "$OUTPUT_FILE"
echo '```' >> "$OUTPUT_FILE"
echo "--- END FILE: BASE ---" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

echo "--- BEGIN FILE: OURS (LOCAL/HEAD) ---" >> "$OUTPUT_FILE"
echo "Version: Your Branch's Version (Stage 2)" >> "$OUTPUT_FILE"
echo "Hash: $OURS_HASH" >> "$OUTPUT_FILE"
echo '```typescript' >> "$OUTPUT_FILE"
git show "$OURS_HASH" >> "$OUTPUT_FILE"
echo '```' >> "$OUTPUT_FILE"
echo "--- END FILE: OURS (LOCAL/HEAD) ---" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

echo "--- BEGIN FILE: THEIRS (REMOTE/MERGE_HEAD) ---" >> "$OUTPUT_FILE"
echo "Version: Incoming Branch's Version (Stage 3)" >> "$OUTPUT_FILE"
echo "Hash: $THEIRS_HASH" >> "$OUTPUT_FILE"
echo '```typescript' >> "$OUTPUT_FILE"
git show "$THEIRS_HASH" >> "$OUTPUT_FILE"
echo '```' >> "$OUTPUT_FILE"
echo "--- END FILE: THEIRS (REMOTE/MERGE_HEAD) ---" >> "$OUTPUT_FILE"
echo "" >> "$OUTPUT_FILE"

echo "--- BEGIN FILE: CONFLICTED ---" >> "$OUTPUT_FILE"
echo "Version: Current file in your working directory with conflict markers" >> "$OUTPUT_FILE"
echo '```typescript' >> "$OUTPUT_FILE"
cat "$CONFLICTED_FILE_PATH" >> "$OUTPUT_FILE"
echo '```' >> "$OUTPUT_FILE"
echo "--- END FILE: CONFLICTED ---" >> "$OUTPUT_FILE"

echo ""
echo "Done. All versions have been written to: $PROJECT_ROOT/$OUTPUT_FILE"
echo "You can now copy the entire content of this file and paste it into the other AI model."
