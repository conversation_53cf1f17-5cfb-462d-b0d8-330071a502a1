# 🚀 GitHub Auto-Deployment Setup

## SSH Key Created ✅

I've generated an SSH key pair for GitHub Actions deployment:

- **Private Key**: `~/.ssh/github_deploy_key`
- **Public Key**: `~/.ssh/github_deploy_key.pub`
- **Added to**: `~/.ssh/authorized_keys`

## 🔧 GitHub Repository Secrets Setup

Go to your GitHub repository → **Settings** → **Secrets and variables** → **Actions** → **New repository secret**

Add these secrets:

```
Name: PROD_HOST
Value: *************

Name: PROD_USER
Value: jayson

Name: PROD_SSH_KEY
Value: [Copy the private key below]

Name: PROD_PORT
Value: 22
```

## 🔑 Private Key for GitHub Secrets

```
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```

## 🎯 How It Works

1. **Push to main branch** → GitHub Action triggers
2. **SSH into your server** using the key above
3. **Pulls latest code** from GitHub
4. **Builds and restarts** PM2 automatically

## 🧪 Test the Setup

1. Add the secrets to GitHub
2. Make a small change and push to main
3. Watch the **Actions** tab in GitHub

## 📋 Manual Sync (Alternative)

If you need to sync configs manually:

```bash
./scripts/sync-to-production.sh
```

## 🔒 Security Notes

- ✅ Key is dedicated for deployment only
- ✅ No password required (uses SSH key)
- ✅ Limited to specific user account
- ✅ Can be revoked anytime by removing from authorized_keys

## 🚨 Next Steps

1. **Copy the private key** above to GitHub secrets
2. **Test with a small commit** to main branch
3. **Monitor the deployment** in GitHub Actions

That's it! Your auto-deployment is ready! 🎉
