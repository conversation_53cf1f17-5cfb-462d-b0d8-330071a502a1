{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ESNext",
    "lib": ["ESNext", "DOM"],
    "moduleResolution": "Node",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "outDir": "./dist",
    "strict": true,
    "types": ["node"],
    "allowJs": true,
    "noEmit": true,
    "incremental": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "plugins": [
      {
        "name": "next"
      }
    ],
    // Path alias for absolute imports using '@'
    "baseUrl": "src",
    "paths": {
      "@/*": ["*"]
    }
  },
  "include": ["src/**/*", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
