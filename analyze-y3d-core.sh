#!/bin/bash

# Y3DHub Core Analysis Script
# This script identifies the most important files in the Y3DHub project
# and passes them to OpenAI Codex for analysis

# Set default parameters
FOCUS=${1:-"print-queue"}
OUTPUT_FILE="selected-files.txt"
MAX_FILES=25
BATCH_SIZE=20  # For processing large file sets

# Define color codes
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Y3DHub Core Analyzer ===${NC}"
echo -e "${YELLOW}Focus: ${FOCUS}${NC}"

# Function to select core files based on focus area
select_core_files() {
  local focus=$1
  
  case "$focus" in
    "print-queue")
      echo -e "${GREEN}Finding print queue related files...${NC}"
      find src -type f \( -name "*.ts" -o -name "*.tsx" \) | grep -v "node_modules\|dist\|.next" | grep -E "print|queue|task|orderItem|shipstation|openscad|populate" | sort
      ;;
      
    "order-processing")
      echo -e "${GREEN}Finding order processing related files...${NC}"
      find src -type f \( -name "*.ts" -o -name "*.tsx" \) | grep -v "node_modules\|dist\|.next" | grep -E "order|sync|shipstation|customer|amazon|marketplace" | sort
      ;;
      
    "core-logic")
      echo -e "${GREEN}Finding core business logic files...${NC}"
      find src/lib -type f -name "*.ts" | grep -v "node_modules\|dist\|.next" | sort
      ;;
      
    "scripts")
      echo -e "${GREEN}Finding main scripts...${NC}"
      find src/scripts -type f -name "*.ts" | grep -v "node_modules\|dist\|test" | sort
      ;;
      
    "all")
      echo -e "${GREEN}Finding all important files (excluding UI components)...${NC}"
      find src -type f \( -name "*.ts" -o -name "*.tsx" \) | grep -v "node_modules\|dist\|.next\|ui\|components" | sort
      ;;

    "docs")
      echo -e "${GREEN}Finding all documentation files...${NC}"
      find . -type f -name "*.md" | grep -v "node_modules\|dist\|.next" | sort
      ;;
      
    "everything")
      echo -e "${GREEN}Finding ALL JavaScript, TypeScript and Markdown files...${NC}"
      find . -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" -o -name "*.md" \) | grep -v "node_modules\|dist\|.next\|\.git" | sort
      ;;
      
    *)
      echo -e "${RED}Unknown focus area. Using print-queue as default.${NC}"
      find src -type f \( -name "*.ts" -o -name "*.tsx" \) | grep -v "node_modules\|dist\|.next" | grep -E "print|queue|task|orderItem" | sort
      ;;
  esac
}

# Always-include files (schema, env, config, main docs)
ALWAYS_INCLUDE=(
  "prisma/schema.prisma"
  ".env"
  "next.config.js"
  "tsconfig.json"
  "package.json"
  "README.md"
  "docs/SYSTEM_OVERVIEW.md"
  "docs/Y3DHUB_MASTER_REFERENCE.md"
)

# Select files and store in temporary file
SELECTED_FILES=$(select_core_files "$FOCUS")

# Add always-include files if they exist and not already present
for f in "${ALWAYS_INCLUDE[@]}"; do
  if [ -f "$f" ]; then
    if ! echo "$SELECTED_FILES" | grep -qxF "$f"; then
      SELECTED_FILES="$f
$SELECTED_FILES"
    fi
  fi
  done

echo "$SELECTED_FILES" > "$OUTPUT_FILE"

# Count files
FILE_COUNT=$(echo "$SELECTED_FILES" | wc -l)

# If too many files, select the most important ones
if [ "$FILE_COUNT" -gt "$MAX_FILES" ]; then
  echo -e "${YELLOW}Found $FILE_COUNT files, limiting to $MAX_FILES most relevant files${NC}"
  
  # Define priority patterns based on focus
  if [ "$FOCUS" = "print-queue" ]; then
    # For print-queue, prioritize these specific files
    cat "$OUTPUT_FILE" | grep -E "populate-print-queue-v2|print-queue/page|print-task|openscad" | head -n $MAX_FILES > "$OUTPUT_FILE.tmp"
  elif [ "$FOCUS" = "order-processing" ]; then
    # For order-processing, prioritize these specific files
    cat "$OUTPUT_FILE" | grep -E "order-sync|shipstation/api|mappers|amazon/customization" | head -n $MAX_FILES > "$OUTPUT_FILE.tmp"
  else
    # Default: just take the first MAX_FILES
    head -n $MAX_FILES "$OUTPUT_FILE" > "$OUTPUT_FILE.tmp"
  fi
  
  mv "$OUTPUT_FILE.tmp" "$OUTPUT_FILE"
  SELECTED_FILES=$(cat "$OUTPUT_FILE")
  FILE_COUNT=$(echo "$SELECTED_FILES" | wc -l)
fi

echo -e "${GREEN}Selected $FILE_COUNT files for analysis${NC}"

# Display selected files
echo -e "${BLUE}Selected Files:${NC}"
cat "$OUTPUT_FILE" | nl

# Ask user if they want to proceed with analysis
echo -e "${YELLOW}Do you want to analyze these files with Codex? (y/n)${NC}"
read -r PROCEED

if [[ "$PROCEED" =~ ^[Yy]$ ]]; then
  # Check if we need to process in batches (for 'everything' mode)
  if [ "$FOCUS" = "everything" ] && [ "$FILE_COUNT" -gt "$BATCH_SIZE" ]; then
    echo -e "${YELLOW}Large number of files detected. Processing in batches of $BATCH_SIZE${NC}"
    
    # Create batches directory
    BATCH_DIR="codex_batches_$(date +%s)"
    mkdir -p "$BATCH_DIR"
    
    # Split files into batches
    split -l "$BATCH_SIZE" "$OUTPUT_FILE" "$BATCH_DIR/batch_"
    
    # Process each batch
    BATCH_COUNT=$(ls "$BATCH_DIR/batch_"* | wc -l)
    echo -e "${BLUE}Created $BATCH_COUNT batches${NC}"
    
    BATCH_NUM=1
    for BATCH_FILE in "$BATCH_DIR"/batch_*; do
      echo -e "${YELLOW}Processing batch $BATCH_NUM of $BATCH_COUNT${NC}"
      
      # Prepare file list for this batch
      FILES_ARG=$(cat "$BATCH_FILE" | tr '\n' ' ')
      
      # Construct prompt for this batch
      PROMPT="Analyze batch $BATCH_NUM of $BATCH_COUNT from Y3DHub's codebase. Focus on understanding the structure, dependencies, and key functionality."
      
      # Run Codex with this batch and save output
      echo -e "${BLUE}Running Codex analysis for batch $BATCH_NUM...${NC}"
      codex "$PROMPT" > "$BATCH_DIR/batch_${BATCH_NUM}_summary.txt"
      
      echo -e "${GREEN}Batch $BATCH_NUM complete. Summary saved to $BATCH_DIR/batch_${BATCH_NUM}_summary.txt${NC}"
      BATCH_NUM=$((BATCH_NUM + 1))
      
      # Ask if user wants to continue to next batch
      if [ "$BATCH_NUM" -le "$BATCH_COUNT" ]; then
        echo -e "${YELLOW}Continue to next batch? (y/n)${NC}"
        read -r CONTINUE
        if [[ ! "$CONTINUE" =~ ^[Yy]$ ]]; then
          echo -e "${RED}Batch processing stopped.${NC}"
          break
        fi
      fi
    done
    
    # After all batches, provide merge instructions
    echo -e "${GREEN}All batches processed. Files lists and summaries saved in $BATCH_DIR${NC}"
    echo -e "${BLUE}To merge all batch summaries into a single master summary, run:${NC}"
    echo -e "cat $BATCH_DIR/batch_*_summary.txt > Y3DHub_MASTER_SUMMARY.md"
    echo -e "${YELLOW}After merging, review Y3DHub_MASTER_SUMMARY.md and use the template below to extract and document all rules, conventions, and architectural notes.${NC}"
    echo -e "\n---\nRULE/ARCHITECTURE EXTRACTION TEMPLATE (add to top of Y3DHub_MASTER_SUMMARY.md):\n\n# Y3DHub Codebase Condensed Rules & Architecture\n\n## Coding Conventions\n- [ ] List all naming conventions, file structure rules, and patterns\n\n## Business Logic & Workflow Rules\n- [ ] Document order processing, print queue, personalization, etc.\n\n## Configuration & Environment\n- [ ] Summarize key configs (.env, schema.prisma, etc.)\n\n## Architectural Overview\n- [ ] High-level diagram or description of main modules and their relationships\n\n---\n"
    
  else
    # Process all files in one go (standard mode)
    # Prepare file list for codex command
    FILES_ARG=$(cat "$OUTPUT_FILE" | tr '\n' ' ')
    
    # Construct prompt based on focus
    if [ "$FOCUS" = "print-queue" ]; then
      PROMPT="Analyze these core files from Y3DHub's print queue system. Focus on how orders from ShipStation are processed into print tasks, how personalization is extracted, and how the queue is managed."
    elif [ "$FOCUS" = "order-processing" ]; then
      PROMPT="Analyze these core files from Y3DHub's order processing system. Focus on how orders are synced from ShipStation, how they're mapped to our database schema, and how customizations are extracted."
    elif [ "$FOCUS" = "docs" ]; then
      PROMPT="Analyze these documentation files from Y3DHub. Summarize the key information and how the documentation is structured."
    elif [ "$FOCUS" = "everything" ]; then
      PROMPT="Analyze these files from Y3DHub. Provide a high-level overview of the project structure, key components, and how they fit together."
    else
      PROMPT="Analyze these core files from Y3DHub. Explain the relationships between these components and how data flows through the system."
    fi
    
    # Run Codex with the selected files
    echo -e "${BLUE}Running Codex analysis...${NC}"
    codex "$PROMPT"
    
    # Clean up
    echo -e "${GREEN}Analysis complete. File list saved to $OUTPUT_FILE${NC}"
  fi
else
  echo -e "${YELLOW}Analysis cancelled. File list saved to $OUTPUT_FILE${NC}"
fi
