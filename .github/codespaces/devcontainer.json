{
  "$schema": "https://containers.dev/schemas/devcontainer.json",
  "name": "Y3DHub Codespaces (Optimized)",

  "dockerComposeFile": ["../../.devcontainer/docker-compose.yml"],
  "service": "app",
  "workspaceFolder": "/workspace",

  "build": {
    "dockerfile": "../../Dockerfile"
  },

  "mounts": [
    "source=node_modules,target=/workspace/node_modules,type=volume",
    "source=${localWorkspaceFolder}/src,target=/workspace/src,type=bind,consistency=cached"
  ],

  "customizations": {
    "vscode": {
      "extensions": [
        // Essential for Y3DHub
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "prisma.prisma",

        // GitHub integration (built-in in Codespaces)
        "github.copilot",
        "github.copilot-chat",
        "github.vscode-pull-request-github",

        // Next.js/React development
        "ms-vscode.vscode-json",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",

        // Git tools
        "eamodio.gitlens",
        "github.vscode-github-actions",

        // Database tools
        "mtxr.sqltools",
        "mtxr.sqltools-driver-mysql",

        // Codespaces-optimized productivity
        "ms-vscode.vscode-dotenv",
        "yzhang.markdown-all-in-one",
        "redhat.vscode-yaml",
        "usernamehw.errorlens",
        "gruntfuggly.todo-tree",
        "aaron-bond.better-comments"
      ],
      "settings": {
        // Codespaces-optimized settings
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit",
          "source.organizeImports": "explicit"
        },

        // Performance optimizations for cloud
        "search.exclude": {
          "**/node_modules": true,
          "**/dist": true,
          "**/.next": true,
          "**/coverage": true
        },
        "files.watcherExclude": {
          "**/node_modules/**": true,
          "**/.next/**": true
        },

        // TypeScript optimizations
        "typescript.preferences.importModuleSpecifier": "non-relative",
        "typescript.suggest.autoImports": true,
        "typescript.updateImportsOnFileMove.enabled": "always",

        // Tailwind CSS
        "tailwindCSS.includeLanguages": {
          "typescript": "javascript",
          "typescriptreact": "javascript"
        },

        // AI assistance optimizations
        "github.copilot.chat.enabled": true,
        "github.copilot.chat.completionContext.typescript.mode": "on",
        "github.copilot.chat.languageContext.typescript.enabled": true,

        // Codespaces terminal
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "bash",
            "args": ["-l"]
          }
        },

        // Auto-save for cloud environments
        "files.autoSave": "afterDelay",
        "files.autoSaveDelay": 1000,

        // Better error visibility
        "problems.showCurrentInStatus": true,
        "editor.rulers": [80, 120]
      }
    },
    "codespaces": {
      "repositories": {
        "y3dltd/Y3DCore": {
          "permissions": {
            "contents": "write",
            "pull_requests": "write",
            "actions": "read",
            "issues": "write"
          }
        }
      },
      "openFiles": ["README.md", "src/app/page.tsx", "prisma/schema.prisma", ".cursorrules"]
    }
  },

  "forwardPorts": [3001, 3000, 3306, 4000],
  "portsAttributes": {
    "3001": {
      "label": "Next.js Dev Server",
      "onAutoForward": "openPreview",
      "visibility": "public"
    },
    "3000": {
      "label": "Next.js Production",
      "onAutoForward": "silent",
      "visibility": "private"
    },
    "3306": {
      "label": "MySQL",
      "onAutoForward": "silent",
      "visibility": "private"
    },
    "4000": {
      "label": "LiteLLM",
      "onAutoForward": "silent",
      "visibility": "private"
    }
  },

  "postCreateCommand": "npm install && npx prisma generate && echo 'Codespace ready for Y3DHub development!'",
  "postStartCommand": "npm run db:migrate || echo 'Migration skipped - check database connection'",
  "postAttachCommand": "echo 'Welcome to Y3DHub Codespace! Run: npm run dev'",

  "remoteUser": "node",
  "updateRemoteUserUID": true,
  "remoteEnv": {
    "DATABASE_URL": "mysql://dbuser:dbpassword@db:3306/y3dhub",
    "NEXTAUTH_SECRET": "codespaces-dev-secret-key-change-in-production",
    "NEXTAUTH_URL": "https://${CODESPACE_NAME}-3001.${GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}",
    "NODE_ENV": "development",
    "CODESPACES": "true"
  },

  "hostRequirements": {
    "cpus": 4,
    "memory": "8gb",
    "storage": "32gb"
  },

  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "22"
    },
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  }
}
