# Y3DHub Codespaces Setup

This directory contains GitHub Codespaces-specific configurations for Y3DHub development.

## 🚀 Quick Start

1. **Create Codespace**: Click "Code" → "Codespaces" → "Create codespace on main"
2. **Wait for Setup**: Initial setup takes ~3-5 minutes
3. **Start Development**: Run `npm run dev` and open the forwarded port

## 📋 What's Included

### Pre-installed Extensions

- **Essential**: <PERSON>tti<PERSON>, ESLint, TypeScript, Tailwind CSS, Prisma
- **GitHub**: Copilot, Pull Requests, Actions
- **Development**: GitLens, Error Lens, Todo Tree, Better Comments
- **Database**: SQL Tools with MySQL driver

### Pre-configured Services

- **Next.js Dev Server**: Port 3001 (auto-opens)
- **MySQL Database**: Port 3306 (internal)
- **LiteLLM Proxy**: Port 4000 (internal)

### Environment Setup

- **Node.js 22**: Latest LTS version
- **Database**: MySQL 8.0 with sample data
- **Authentication**: NextAuth.js configured for Codespaces
- **AI Context**: `.cursorrules` for better AI assistance

## 🔧 Configuration Files

- `devcontainer.json`: Main Codespaces configuration
- `../../.devcontainer/docker-compose.yml`: Docker services
- `../../Dockerfile`: Container image definition

## 🌐 Environment Variables

Codespaces automatically sets:

- `NEXTAUTH_URL`: Dynamic Codespace URL
- `DATABASE_URL`: Internal MySQL connection
- `CODESPACES=true`: Codespace detection flag

For production APIs, add these to your Codespace secrets:

- `SHIPSTATION_API_KEY`
- `SHIPSTATION_API_SECRET`
- `AMAZON_ACCESS_KEY`
- `AMAZON_SECRET_KEY`
- `OPENAI_API_KEY`

## 🚦 Getting Started

1. **Install Dependencies** (auto-runs):

   ```bash
   npm install
   ```

2. **Setup Database** (auto-runs):

   ```bash
   npx prisma generate
   npm run db:migrate
   ```

3. **Start Development**:

   ```bash
   npm run dev
   ```

4. **Open Application**:
   - Click the popup to open port 3001
   - Or go to Ports tab and click the globe icon

## 🔍 Available Commands

```bash
# Development
npm run dev              # Start Next.js dev server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:push         # Push schema changes
npm run db:migrate      # Run migrations
npm run db:reset        # Reset database
npm run db:seed         # Seed with sample data

# Quality
npm run lint            # ESLint check
npm run type-check      # TypeScript check
npm run test            # Run tests

# Scripts
npm run sync:orders     # Sync orders from ShipStation
npm run process:queue   # Process print queue
```

## 🎯 AI-Powered Development

### Cursor/Windsurf Features

- **Context**: `.cursorrules` provides Y3DHub-specific context
- **Patterns**: AI understands your codebase patterns
- **Business Logic**: AI knows about 3D printing workflows

### GitHub Copilot

- **Chat**: Enabled for TypeScript and React
- **Suggestions**: Context-aware code completion
- **Tests**: Auto-generate test suggestions

## 🔧 Troubleshooting

### Database Connection Issues

```bash
# Check if MySQL is running
docker ps | grep mysql

# Restart database
docker-compose -f .devcontainer/docker-compose.yml restart db

# Reset database
npm run db:reset
```

### Port Forwarding Issues

- Go to "Ports" tab in VS Code
- Make port 3001 public if needed
- Check firewall settings in Codespace

### Performance Issues

- Codespaces provides 4 CPU cores and 8GB RAM
- If slow, try restarting the Codespace
- Check browser DevTools for client-side issues

### Extension Issues

- Extensions auto-install on Codespace creation
- Manually install missing extensions from `.vscode/extensions.json`
- Reload window if extensions don't activate

## 📈 Performance Tips

1. **Use Prebuilds**: Enable prebuild for faster startup
2. **Auto-save**: Files auto-save after 1 second
3. **Search Exclusions**: node_modules and build folders excluded
4. **Memory Management**: TypeScript server optimized for cloud

## 🛡️ Security

- **Secrets**: Use Codespace secrets for API keys
- **Environment**: Development environment only
- **Database**: Isolated MySQL instance
- **Ports**: Internal ports not publicly accessible by default

## 📚 Resources

- [GitHub Codespaces Docs](https://docs.github.com/en/codespaces)
- [Dev Container Specification](https://containers.dev/)
- [Y3DHub Documentation](../../docs/README.md)
- [VS Code in Browser](https://code.visualstudio.com/docs/remote/codespaces)

---

**Ready to develop Y3DHub in the cloud! 🚀**
