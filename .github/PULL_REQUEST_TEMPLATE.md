# Pull Request

## Summary

<!-- Brief description of the changes -->

## Type of Change

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 🔧 Refactoring (code change that neither fixes a bug nor adds a feature)
- [ ] 📚 Documentation update
- [ ] 🎨 Style/UI changes
- [ ] ⚡ Performance improvement
- [ ] 🔒 Security fix
- [ ] 🗄️ Database changes
- [ ] 🔌 API integration changes

## Y3DHub Business Impact

<!-- How does this change affect the 3D printing workflow, order processing, or customer experience? -->

### Order Processing Impact

- [ ] Affects order import from ShipStation
- [ ] Changes print queue optimization
- [ ] Modifies STL generation process
- [ ] Updates shipping/fulfillment workflow
- [ ] No impact on order processing

### Performance Considerations

- [ ] Database query optimization
- [ ] API call efficiency
- [ ] STL generation performance
- [ ] UI responsiveness
- [ ] Memory usage optimization

## Technical Details

### Database Changes

- [ ] Schema migrations included
- [ ] Indexes added/modified
- [ ] Data migrations required
- [ ] No database changes

### API Integration Changes

- [ ] ShipStation API updates
- [ ] Amazon SP-API modifications
- [ ] OpenAI API usage changes
- [ ] New external API integration
- [ ] No API changes

### Security Considerations

- [ ] Environment variables added/changed
- [ ] Authentication/authorization changes
- [ ] Input validation updates
- [ ] API security improvements
- [ ] No security implications

## Testing

- [ ] Unit tests added/updated
- [ ] Integration tests included
- [ ] E2E tests verified
- [ ] Manual testing completed
- [ ] Performance testing done

### Test Coverage

<!-- Describe what has been tested -->

## Deployment Notes

- [ ] Requires environment variable updates
- [ ] Database migration needed
- [ ] PM2 restart required
- [ ] External service configuration needed
- [ ] Ready for deployment

## Documentation

- [ ] Code comments added
- [ ] README updated
- [ ] API documentation updated
- [ ] Deployment guide updated
- [ ] No documentation changes needed

---

## Claude Review Request

**@claude** - Please review this PR focusing on:

- [ ] **Code Quality**: TypeScript usage, error handling, performance
- [ ] **Security**: Input validation, API security, data protection
- [ ] **Architecture**: Design patterns, maintainability, scalability
- [ ] **Business Logic**: Order processing workflow correctness
- [ ] **Integration**: ShipStation/Amazon API best practices
- [ ] **Database**: Query optimization, schema design
- [ ] **Testing**: Coverage and test quality
- [ ] **Performance**: Impact on high-volume operations (1000+ orders/day)

### Specific Areas for Claude Focus

<!-- Optional: Specify particular files or aspects you want Claude to examine closely -->

---

## Checklist

- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Tests pass locally
- [ ] No console errors or warnings
- [ ] Documentation updated
- [ ] Breaking changes documented
- [ ] Security implications considered
- [ ] Performance impact assessed
