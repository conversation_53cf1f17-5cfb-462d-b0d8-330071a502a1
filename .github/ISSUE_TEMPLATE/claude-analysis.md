---
name: 🤖 Claude Code Analysis Request
about: Request Claude to analyze specific code, features, or architectural decisions
title: '@claude [Brief description of analysis request]'
labels: ['claude-analysis', 'help-wanted']
assignees: []
---

## Analysis Request

**@claude** - Please analyze the following:

### Code/Feature to Analyze

<!-- Specify the file(s), component(s), or feature area you want analyzed -->

### Specific Questions

<!-- What specific aspects do you want <PERSON> to focus on? -->

- [ ] Performance optimization opportunities
- [ ] Security vulnerabilities
- [ ] Code architecture and design patterns
- [ ] TypeScript type safety improvements
- [ ] Database query optimization
- [ ] API integration best practices
- [ ] Accessibility compliance
- [ ] Testing coverage and strategies
- [ ] Error handling robustness
- [ ] Business logic correctness

### Context

<!-- Provide any additional context about the business requirements, performance needs, or constraints -->

### Current Issues or Concerns

<!-- Describe any specific problems or concerns you're experiencing -->

### Expected Outcome

<!-- What do you hope to achieve from this analysis? -->

---

**Y3DHub Context:**

- 3D printing business management platform
- Handles 1000+ orders per day
- Integrates with ShipStation, Amazon SP-API, OpenAI
- Uses Next.js 14, Prisma, MySQL, TypeScript
- Performance-critical STL file generation
