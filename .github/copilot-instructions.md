# Y3DHub – Copilot Project Guide

## Project Overview

Y3DHub (repository name **Y3DCore**) is a full-stack web platform for automating 3D printing order fulfillment. It is built with **Next.js 14 (React 18)** and manages the workflow from **order receipt to STL model generation** for personalized 3D-printed products. The system integrates with external services – **ShipStation API** for importing e-commerce orders, **OpenAI** for extracting personalization text, **OpenSCAD** for generating 3D models – and uses a MySQL database via **Prisma ORM** to track orders, print tasks, and related data. The primary goal is to streamline a 3D print business’s operations by automatically syncing orders, creating **print tasks** for each item (including any custom text or color personalization), rendering the required **STL files**, and managing the print queue through to completion. Key capabilities include order management and synchronization, a print task workflow (pending → in progress → completed), automated STL rendering, AI-driven text extraction for personalization details, product SKU normalization across marketplaces (Amazon, Etsy, eBay), and a review system for flagged tasks.

**Deployment Architecture:** Y3DHub is typically deployed in three parallel environments on the same server: a development instance, a staging test instance, and production, each running on different ports (e.g. 3002 dev, 3001 staging, 3000 prod). All instances share a MySQL database server but use separate schemas for prod vs. non-prod data. Docker containers and PM2 are used for deployment and process management, and environment-specific configurations are stored in `.env` files (not committed, containing keys for ShipStation, OpenAI, etc.).

## Project Structure and Architecture

**Repository Layout:** The project follows a modular monorepo-style structure with clear separation of concerns between frontend pages, backend logic, integrations, and documentation. Notable top-level files and directories include:

- **Documentation:** `README.md` (overview and usage) and a rich `docs/` folder (system overview, guides, references).
- **Configuration:** Node/TypeScript config files like `package.json`, `tsconfig.json`, and environment config `.env` (holds sensitive keys). Build and deploy configs include `Dockerfile`, `docker-compose.litellm.yml`, and `vercel.json` for containerization and cloud deployment settings.
- **Database Schema:** `prisma/` directory with Prisma ORM definitions (`schema.prisma`) and migration files. The Prisma schema defines models such as **Order**, **OrderItem**, **Customer**, **PrintOrderTask**, **Product**, **User** (NextAuth), etc., plus enums for various statuses.
- **3D Models:** `openscad/` folder containing OpenSCAD `.scad` files and JSON presets for parametric model generation. These define base models (e.g. keychains, cable tags) and configuration for customization (e.g. dual-color parameters).
- **Scripts & DevOps:** `scripts/` (shell scripts for automation and maintenance) and `.devcontainer/` (development container config). For example, `workflow.sh` orchestrates syncing and task creation in one go, and there are deploy sync scripts.

**Source Code (`src/`):** All application code resides under `src/` with a structured layout:

- **`src/app/`** – Next.js **App Router** implementation of the UI and API endpoints. This includes React page components for main UI sections and corresponding API route handlers:

  - `app/orders/` – Pages for order management (list orders, order details).
  - `app/print-queue/` – Pages for managing the print task queue (view tasks, filtering, approvals). The print queue UI is protected by auth and allows reviewing tasks (e.g. marking tasks as approved or needing changes).
  - `app/planner/` – Page for the **plate planner**, which groups print tasks into optimal batches (by color/SKU) for efficient printing (uses AI to suggest groupings).
  - `app/api/` – Next.js **API routes** for backend endpoints (e.g. `/api/orders`, `/api/print-tasks`, etc.). Each is implemented as a `route.ts` in a folder under `api/`. For example, `api/print-tasks/bulk-status/route.ts` handles bulk status updates from the UI, and `api/sync/shipstation/route.ts` might trigger an on-demand sync. All API routes use Next.js Request/Response (`NextRequest`, `NextResponse`) and often share common patterns (auth checking, Zod validation, standardized error responses – see **Code Conventions**).

- **`src/components/`** – Reusable **UI components** used across pages. This includes both generic UI elements (possibly built on Tailwind CSS, NextUI and Radix UI libraries) and domain-specific components (e.g. a task status badge, modals, tables). Components follow React best practices (functional components with hooks, composition over inheritance) and are typically in TypeScript/JSX (often PascalCase filenames for React components). Styling is primarily via **Tailwind CSS** utility classes and NextUI theming.

- **`src/lib/`** – **Core libraries and business logic** modules. This is the heart of the application’s backend functionality, organized by domain:

  - **`lib/ai/`** – Integration with OpenAI for AI-powered features. It contains utilities for calling the OpenAI API, constructing prompts, and parsing AI responses. For instance, `ai/extract-personalization.ts` uses the OpenAI API to extract names, colors, or messages from order notes when generating print tasks. There is also an `ai/prompts/` subfolder with prompt templates (e.g. prompts for grouping print jobs or plate planning). The AI calls are logged in the database (`AiCallLog` model) for monitoring.
  - **`lib/auth/`** – Authentication logic using **NextAuth.js**. This includes NextAuth configuration (providers, callbacks) and helper functions. Y3DHub uses NextAuth’s **Credentials provider** (email/password login) with Prisma for storing users. For security, passwords are hashed (see `lib/server-only/auth-password.ts` for hashing logic) and sessions are stored in the DB (via NextAuth’s built-in models). A Next.js middleware (`src/middleware.ts`) protects application routes by redirecting unauthenticated users to login.
  - **`lib/db/`** – Database utilities and Prisma client setup. For example, `lib/prisma.ts` instantiates the Prisma client and may apply logging or middleware. The DB layer uses **Prisma 6** with strict typing. Common patterns include using `prisma.transaction()` for multi-step operations and including related entities to avoid N+1 queries. The schema uses **CUID strings** for some IDs (e.g. PrintOrderTask.id) and autoincrement ints for others, and defines indexes for performance-critical fields.
  - **`lib/email/`** – Email integration (using **SendGrid**). This likely includes functions to send order notifications or failure alerts via SendGrid API. For example, an email may be sent when a print task fails or when an order is ready.
  - **`lib/orders/`** – Order processing logic. This module contains functions for synchronizing orders from ShipStation, mapping incoming data to internal models, and validating order data. Key files include `lib/orders/sync.ts` (core logic for order import/update), `mappers.ts` or `mappers/` (transform ShipStation API data to Prisma format), and `amazon/` subfolder for Amazon-specific handling (e.g. parsing Amazon Custom data files).
  - **`lib/openscad/`** – OpenSCAD integration for 3D model rendering. Provides utilities to interface with the OpenSCAD CLI. For instance, `lib/openscad/render.ts` can build command-line arguments and invoke the OpenSCAD binary to generate an STL based on a given `.scad` file and parameters. It may also handle any necessary post-processing or file management (e.g., ensuring output directories exist).
  - **`lib/products/`** – Product definitions and SKU mapping logic. Y3DHub normalizes product identifiers from various marketplaces into a unified representation. For example, it uses mapping tables or logic (`lib/products/mapping.ts`) to translate marketplace-specific SKUs or options into internal product IDs and model parameters. This module ensures that, for a given order item, the system knows which OpenSCAD model and preset to use and what type of personalization to apply.
  - **`lib/shipstation/`** – ShipStation API client and utilities. Handles communication with ShipStation’s REST API for orders, and possibly also for updating tracking info or order notes. Likely contains an `api.ts` or `client.ts` that wraps fetch calls with the API key/secret, and a `types.ts` for ShipStation DTO types. It may also include a `db-sync.ts` helper that upserts fetched data into the local DB (invoked by the order sync script). **Marketplace Support:** ShipStation aggregates orders from multiple marketplaces (Amazon, eBay, Etsy, etc.), so order records carry a `marketplace` field indicating the source. The sync logic applies marketplace-specific handling; for instance, Amazon Custom orders come with an extra **customization file** (image or JSON) which Y3DHub downloads and processes (`AmazonCustomizationFile` model stores the file info and extracted text/colors). If Amazon provides a personalization text via a “customization URL”, the system will attempt to parse it (in `lib/orders/amazon/customization.ts`) before falling back to AI extraction.

- **`src/scripts/`** – Standalone **Node.js scripts** for background or one-time tasks, often run via CLI or cron jobs. These TypeScript scripts tie together the lib functions for specific workflows. Important scripts include:

  - `sync-orders.ts` – Fetches the latest orders from ShipStation and syncs them into the local database. It supports command-line flags (e.g. `--mode all|recent|single`, `--days-back N`, `--order-id` for targeted sync) to control the sync scope. Internally it calls `lib/orders/sync.ts` which handles pagination through the ShipStation API, mapping data via mappers, and upserting **Order**, **OrderItem**, **Customer**, and **OrderTag** records in Prisma. It ensures existing orders are updated (if modified on the marketplace) and new orders are inserted. Sync operations are logged for audit – e.g. a `SyncProgress` or `SyncMetrics` record tracks how many orders/items were processed and any errors. Relevant environment variables include `SHIPSTATION_API_KEY`, `SHIPSTATION_API_SECRET` (for API auth).

  - `populate-print-queue.ts` – Processes orders in the DB and creates **PrintOrderTask** entries (print jobs) for each order item that needs to be printed. This is typically run right after syncing orders. The script filters for orders that are in a status like “awaiting_shipment” or “on_hold” (indicating they need printing) and haven’t yet been fully processed. For each eligible **OrderItem**, it determines the product and personalization: if the item requires customization (e.g. SKU prefix “PER-” indicating a personalized product), it tries to extract the custom text or names. The extraction is done either by parsing known fields (for Amazon, using the downloaded customization JSON/image if available) or by sending a prompt to OpenAI to parse notes/options. This uses the logic in `lib/ai/` (with prompts that look at order notes like “Name: John, Color: Blue”). The result is used to populate fields like custom_text, color_1, color_2 in a new **PrintOrderTask** record. The product SKU is mapped via `lib/products` to link the task to a specific **Product** (which knows which OpenSCAD model to use). Each PrintOrderTask is initially marked with a status (e.g. `pending` or `pending_review` if the AI was not confident). If certain details are missing or ambiguous, the task’s `needs_review` flag is set so it can be manually reviewed in the UI. This script also can update the source order in ShipStation: for example, adding an internal note “Print task created” or applying a tag to indicate the item is being processed. (Such updates use the ShipStation API through `lib/shipstation/api.ts`.)

  - `complete-shipped-print-tasks.ts` – Marks print tasks as **completed** when their orders have shipped. Typically run periodically (e.g. via cron), this script checks for any Order in the database that has `order_status = shipped` (which would have been updated by the sync script via ShipStation data). For each shipped order, it finds associated PrintOrderTasks that are still open (not completed/cancelled) and updates their status to `completed`. This ensures the print queue reflects reality and can help trigger any post-completion actions (like sending a dispatch email or logging metrics).

  - Other scripts: There are utility scripts (e.g., `review-existing-tasks.ts` for re-checking tasks, or maintenance scripts under docs/scripts). Also, `scripts/workflow.sh` is a shell script that simply runs the above three in sequence (sync orders → populate queue → complete shipped) to automate the full daily workflow. Scripts can log their runs in `ScriptRunLog` table (with timestamps and status) for monitoring.

- **`src/workers/`** – **Background worker processes** for continuous tasks. The primary worker is `workers/stl-render-worker.ts`, which is a long-running service (often started via `npm run worker:stl`) that handles generating STL files for print tasks. This worker continuously polls the database for any PrintOrderTasks that are ready to be rendered (e.g. tasks with `stl_render_state = "pending"` and approved/past review). It uses a concurrency limit (e.g. environment variable `STL_WORKER_CONCURRENCY`) to render multiple models in parallel without overloading the system. Each cycle, it:

  - Fetches a batch of pending tasks and marks each as `running` (to avoid duplication).
  - For each task, determines which OpenSCAD model and preset to use by looking up the associated **Product** (the Product may store the path or key for the relevant `.scad` file). It collects the personalization parameters (text, colors, dimensions) from the PrintOrderTask (and possibly related `AiPersonalizationResult` if detailed parsing was stored separately).
  - Calls OpenSCAD via command-line using the helpers in `lib/openscad/` to render the model with the given parameters. The output STL is saved under `public/stls/` with a structured file name (often including Order ID or Item ID and a timestamp). If configured, the worker can also **upload the STL to Google Drive**: if `GDRIVE_ENABLED=true`, the generated file is uploaded using a service account (credentials provided by `GDRIVE_SERVICE_ACCOUNT_PATH`) and the `gdrive_file_id` and `gdrive_public_link` fields of the task are populated. The upload replicates the local folder structure in a specified Drive folder (given by `GDRIVE_FOLDER_ID`).
  - Updates the task’s `stl_render_state` to `completed` on success, along with saving the `stl_path` (and Drive link if applicable). If the OpenSCAD process fails (non-zero exit or throws), it catches the error, marks the task as `failed`, logs the error message, and will retry up to a max number of retries (`render_retries` count, max e.g. 10). The worker also has a startup routine to clean up any tasks left in an indeterminate state (e.g. if a previous run crashed with tasks still marked “running”, it resets those back to pending).
  - **Output:** Successfully rendered STL files are stored in `public/stls/[OrderId]/` directories so that they can be accessed via the web UI or downloaded by users. The UI provides STL previews using these files.

  In addition to STL rendering, future or additional workers could be added (e.g., an email notification worker or AI processing worker), but currently STL generation is the main background process. There is also a legacy Python worker script `src/workers/stlworker.py` (possibly an earlier approach to STL conversion or an integration hook), but the TypeScript worker supersedes it.

## Data Model Highlights

Y3DHub’s **database** (MySQL) is defined via Prisma and includes tables for orders, tasks, and related entities. Key models and relationships:

- **Order** – Core order info from ShipStation. Each Order has many OrderItems and can have multiple PrintOrderTasks (though typically one task per item). Fields include status (`order_status` from marketplace, e.g. “awaiting_shipment” or “shipped”), customer info, shipping details, notes, etc.. An `internal_status` field tracks the order’s progress in Y3DHub (e.g., new, processing, printing, completed), separate from the marketplace status. Order has `marketplace` and store/channel identifiers to distinguish source, and a JSON `tag_ids` field storing any ShipStation tags applied (for custom workflows or flags).

- **OrderItem** – Line items within an order (product, quantity, price, etc.). Each OrderItem is linked to one Product and may result in one or more PrintOrderTasks (though usually one task per item unless the quantity is split).

- **Product** – Catalog of products the business can print. Contains fields like SKU, name, weight, and mappings to external IDs (e.g. ShipStation’s internal product ID). It may also include configuration for how to render that product (not directly in the DB, but in code logic linking SKU to OpenSCAD file/preset). Each Product is associated with OrderItems and PrintOrderTasks. Some products are “personalized” (e.g. custom keychains) which triggers the AI extraction.

- **PrintOrderTask** – Represents a discrete 3D print job to be done, usually corresponding to an OrderItem. This is a central model linking to Order (many-to-one), OrderItem, Product, and optionally a Customer (if needed for personalization context). Important fields include: `status` (overall task status in the print workflow), `needs_review` (flag if manual review is required), `custom_text` (personalization text like a name or message), `color_1` and `color_2` (for multi-color prints), and `quantity`. There is also `stl_path` for the generated file path and `stl_render_state` to track the rendering process state separately. For example, a task starts with status “pending” and stl*render_state “pending”. When picked up by the renderer, stl_render_state goes to “running”, and on success it becomes “completed” (with `stl_path` set). The task status might remain “pending” until someone actually prints it; when printing is done, the status can be set to “completed”. Enumerations in the schema define these states (PrintOrderTask_status: \_pending, in_progress, completed, cancelled*; PrintOrderTask*stl_render_state: \_pending, running, completed, failed*). PrintOrderTask also stores `gdrive_file_id` and `gdrive_public_link` if Google Drive upload is used, and tracks if it was verified by a user (for QA) with a `verifiedByUserId`.

- **Customer** – Customer information (name, email, etc.) extracted from orders. Orders link to a Customer record (so repeated customers can be tracked).

- **User / Account / Session** – Tables for the NextAuth authentication system (users, their credential or OAuth accounts, sessions, password reset tokens, etc.). In Y3DHub, Users are primarily admin or staff accounts that log into the dashboard. Credentials (email/password) are stored in the User table (with hashed passwords), and NextAuth’s Account table may hold provider info if needed.

- **AiCallLog** – Logs each call made to an AI service (like OpenAI). It records which script or module made the call (e.g. “populate-print-queue”), the order context, which AI model was used, the prompt sent, the raw response, success/failure, and timing. This is used for debugging AI behavior and auditing usage (e.g., to monitor costs or identify why an AI extraction failed).

- **AmazonCustomizationFile** – Stores info about customization files from Amazon Custom orders. When an Amazon order includes a custom engraving or image (accessible via a URL), the system downloads it and logs the local path, plus fields for processing status and any extracted text/colors. This ties to an OrderItem (one-to-one).

- **ScriptRunLog / SyncMetrics** – Tracks execution of scripts and performance metrics. ScriptRunLog might have fields for script name, start/end time, status, and error message/stack if any. SyncMetrics and related tables (possibly including `Metric`) gather statistics like how many orders/items were processed in a sync run, how long it took, etc., to identify bottlenecks (these were referenced in docs as part of performance monitoring).

_(Other tables like Tag, VerificationToken, etc., exist but are not central to development tasks. Tags from ShipStation are stored and linked to orders for potential use in logic, and verification tokens are for password reset if implemented.)_

## Core Workflows & Features

**Order Sync (ShipStation Integration):** Orders are imported via the ShipStation API to ensure Y3DHub’s database stays up-to-date with marketplace sales. A cron job or manual trigger runs `sync-orders.ts` periodically. This reaches out to ShipStation’s **List Orders** endpoint, retrieving orders updated since the last sync (by default the last 48 hours, overrideable via `--days-back` or `--hours` flags). The sync uses ShipStation’s API key/secret from env vars and can operate in modes (all orders, recent only, single order by ID, etc.). For each fetched order, the logic in `lib/orders/sync.ts` and `lib/shipstation` does the following:

- **Upsert Orders**: If the order’s ShipStation ID is not in the local DB, a new Order record is created; if it exists, it’s updated (e.g., status changes from shipped, or notes updated). ShipStation’s order number, status, customer info, and all line items are mapped to the Prisma models. Data mapping is handled by **mappers** (converting ShipStation’s JSON fields to our field names/types, e.g. ShipStation’s `orderNumber` → `shipstation_order_number`, etc.).

- **Customers and Items**: The customer on the order is upserted into the Customer table. Each line item from ShipStation becomes an OrderItem record linked to the Order. Item data includes SKU, name, quantity, item-specific options (if any). If ShipStation provides product IDs or SKU that match our Product table, the item may be linked to a Product entry (alternatively, linking is done later during task generation).

- **Tags**: Any tags on the order in ShipStation are stored (ShipStation provides tag IDs and names). These might be saved in an OrderTag join table or as JSON (`tag_ids`) on Order. Tags can be used to mark orders that require special handling (e.g., “EXPRESS” shipping or “Gift”).

- **Error Handling**: The sync process is robust – it logs errors without crashing the whole sync. For example, if ShipStation API rate-limits or an entry fails to insert, it catches that. The system has built-in logic to handle known error cases like duplicate unique constraints (e.g., trying to insert an order that was already synced by a previous run). Those are handled via upsert logic or caught as Prisma errors and logged. The use of `handleApiError` is more for API route errors; in scripts, errors might be logged to console or to the ScriptRunLog.

- **Performance**: The sync uses pagination to handle potentially thousands of orders, retrieving in batches (ShipStation API returns pages of results). It also can limit by date to avoid pulling too much data. After each run, the SyncMetrics (if implemented) records how many orders were fetched, how many created vs updated, etc., to monitor throughput.

After sync, the local DB now contains new orders which then feed into the next step.

**Print Task Generation (Print Queue Population):** This workflow creates individual print jobs (PrintOrderTask) for each newly synced order item that needs printing. It is run via `populate-print-queue.ts` typically right after syncing, so that the print queue is always up to date. Key steps:

- It queries the DB for OrderItems that correspond to orders awaiting shipment (or on hold) which do not yet have print tasks. This can be based on an Order status or an internal flag. The script may consider only orders from the last N days unless `--all` is passed.

- **AI Personalization Extraction:** For each OrderItem that represents a customizable product, the system determines if personalization details (names, messages, colors) are provided or need extraction. For Amazon Custom orders, often an image or JSON file contains the personalization (downloaded separately), so `lib/orders/amazon/customization.ts` would extract text (for example, decode a JSON with a “Name” field or perform OCR on an image if needed). If that yields nothing or if it’s a non-Amazon order (e.g., Etsy or eBay through ShipStation) where personalization is embedded in the “Notes to Seller” or in item options, Y3DHub uses **OpenAI** to parse the text. It constructs a prompt like: _“Extract the following information: name, color, etc., from this text: ‘Personalization: Red keychain with name Alex.’”_ and sends it to an OpenAI model. The response is parsed (via `lib/ai/extract-personalization.ts`) to get structured data. AI usage is logged in AiCallLog with success or flagged if the AI is uncertain. If the AI returns a low confidence or ambiguous result, the task is marked for manual review (`needs_review = true`, and `review_reason` might note why).

- **SKU/Product Mapping:** The item’s SKU or name is used to identify which **Product** and OpenSCAD model to use. The `lib/products` module contains mapping logic – for example, if an SKU starts with `KEY3D-DC-` it might map to the “DualColorKeychain” product and use `DualColour.scad` model. If an item’s SKU isn’t recognized, the system may default to a generic product or skip task creation for that item (logging a warning). Each PrintOrderTask includes a reference to the determined `productId`, plus the extracted `custom_text` (e.g. a name to engrave) and `color_1`/`color_2` (e.g. primary/secondary filament color) if applicable.

- **Task Creation:** A PrintOrderTask entry is upserted for the OrderItem. If an OrderItem was processed before (existing task), it may update it (for example, if a second run of the script catches an order that was partially processed). New tasks get a status like `pending` and initial `stl_render_state = pending`. If the item requires approval (due to AI uncertainty or perhaps if it’s a new product type), the status might start as `pending_review` (or simply pending with a flag). Otherwise, tasks can be auto-approved and ready for rendering. There is logic to avoid duplicate tasks: the script ensures one task per OrderItem (perhaps via a unique index on PrintOrderTask for orderItemId + taskIndex).

- **ShipStation Feedback (Optional):** After successfully creating tasks, the script may push a note or tag back to the order on ShipStation. For example, it could add an internal note: “Y3DHub: Print task created for item X” or apply a tag like “In Production”. This keeps external systems informed that the order is being processed in printing. The code uses `lib/shipstation/api` to send an update (e.g., a POST to ShipStation’s add note endpoint). This step is optional and controlled by config flags.

- **Metrics:** The script logs how many tasks were created and how many might need manual review. It also catches and logs errors (e.g., OpenAI API errors or database issues), possibly creating AiCallLog entries with `success=false` if an AI call fails. By the end of this step, the **“Print Queue”** (collection of PrintOrderTask records) is populated with all pending print jobs, ready for the rendering worker or for manual approval if needed.

**STL Generation (Rendering Worker):** This is handled by the continuously running **STL render worker** (`stl-render-worker.ts`) as described in the structure section. Key points of this workflow, emphasizing concurrency and error handling:

- The worker runs indefinitely and polls every few seconds for tasks with `stl_render_state = "pending"`. It uses `prisma.printOrderTask.findMany` with a filter on that state and maybe also `status = approved` (to skip tasks not approved). It may also filter by tasks whose `Order` is not shipped/cancelled.

- It processes tasks in parallel up to a limit (e.g., 4 concurrent OpenSCAD processes). This is implemented by taking a batch of tasks and updating them in the DB to mark them as `running` (often using an atomic update or selecting with a `FOR UPDATE` lock to prevent multi-process overlap).

- Each task is processed: the code loads the corresponding **Product** to know which SCAD file to use (the mapping might be a simple switch or stored in the Product record). It builds the command for OpenSCAD CLI, including: input `.scad` path, output file path (within `public/stls/`), and `-D` defines for each parameter (e.g., `-D name="Alex" -D color1="Red"` etc.). The OpenSCAD binary path is configurable via `OPENSCAD_EXECUTABLE_PATH` env (or it assumes `openscad` is on the system PATH).

- The worker invokes OpenSCAD and waits for it to complete. If OpenSCAD returns successfully (exit code 0), it means the STL was generated. The code then updates the PrintOrderTask: sets `stl_render_state = completed`, fills in the `stl_path` (relative path to the file) and resets any error fields. If Google Drive integration is enabled, it will also call the GDrive API to upload the file and then save `gdrive_file_id` and `gdrive_public_link`. The upload is done using a service account JSON, and the code will create folder structure on Drive if not already present (organizing by Order ID, etc.).

- If OpenSCAD fails (non-zero exit or throws an exception), the worker catches it. It updates the task’s `stl_render_state` to `failed`, increments `render_retries` count, and records the error (possibly in the task’s `annotation` or a separate ErrorLog). The task remains in the queue, and on the next poll, if `render_retries` is below `MAX_RETRIES` (e.g., 10), it will attempt again. This could happen if, say, the OpenSCAD model couldn’t compile because of a bad parameter (the system might mark needs_review in that case for human to fix input).

- Throughout, the worker uses the centralized logger (`lib/shared/logging.ts`) for console output of info/warnings/errors. Logging is done in JSON format for easier monitoring (each log entry has timestamp, component, level, message).

This automated rendering means as soon as an order comes in and is processed into a PrintOrderTask, within minutes an STL will be generated and available for the print operator. The output STLs can be previewed in the Y3DHub UI (likely via a three.js or STL viewer component on the print queue page).

**Post-Printing and Completion:** Once the physical printing is done and the order ships, the system closes the loop. The `complete-shipped-print-tasks.ts` script or a similar mechanism ensures that any tasks for shipped orders are marked completed so they drop out of the active queue. Also, if an order is cancelled or refunded, there could be logic (either via another script or part of sync) to mark its tasks as `cancelled` so resources aren’t wasted.

**Plate Planner (Batch Grouping):** Y3DHub includes a feature to optimize print scheduling by grouping tasks that can be printed together (on the same printer bed, referred to as a “plate”). This **Plate Planner** uses AI to suggest groupings of tasks with compatible attributes. For example, tasks that use the same filament color and material type could be grouped to print in one batch to minimize machine setup changes. The planner interface (`/app/planner`) allows the user to see suggested “plates” or create their own groups of tasks. Under the hood, the system has AI prompt templates (e.g., `prompt-grouping-v1.txt`, and various `prompt-system-vX-plate-planner.txt` versions) that outline the rules (group by color, ensure total print time under X, etc.). When invoked, the planner takes the list of pending tasks (with their attributes like color, volume, due date) and feeds it to a ChatGPT prompt, which returns a grouping recommendation. The result might be presented as a list of groupings which the user can confirm or adjust. This is a newer/experimental feature and relies on the accuracy of the AI suggestions. It demonstrates how the system leverages AI not just for text parsing but also for decision support (print optimization).

**Dashboard UI:** On the frontend, Y3DHub provides a protected dashboard for users (staff) to monitor and manage everything. Key pages include:

- **Orders Page (`/orders`)** – list of orders, their statuses (both marketplace status and internal status), and possibly a detail view showing items and whether tasks have been created. This page likely allows filtering by status or date, and links to the Print Queue or Planner for further action.
- **Print Queue Page (`/print-queue`)** – central place to manage print tasks. It shows a table of PrintOrderTasks with columns like Order #, Product, Name/Text, Status, STL Render Status, etc.. Users can filter by status (e.g. show all “needs review” tasks), approve tasks that are pending review, or mark tasks as printed. Bulk actions might be available (the API route `print-tasks/bulk-status` suggests the UI can update multiple tasks at once, e.g., mark a batch as “printing” or “completed”). Real-time updates could be present (perhaps via revalidation or simple polling).
- **Planner Page (`/planner`)** – interface for the plate planning feature, as discussed. Users can select tasks and group them, or accept AI-suggested groupings.
- **Users/Settings (`/users` etc.)** – there may be an admin page to manage user accounts (the existence of `api/users` suggests an endpoint for creating users, perhaps exposed in a UI).
- **Auth Pages** – NextAuth provides pages for login. Since Credentials provider is used, likely a custom login page is implemented in `app/(auth)/login` or similar, where staff enter email/password to sign in. After login, a session cookie is set and NextAuth handles session verification.

The UI is built with modern React patterns: it uses **Server Components** for initial page loads where possible (to leverage Next.js 13+ benefits) and **Client Components** for interactive parts (marked with the `"use client"` directive). The project favors server-side data fetching via Next.js (e.g., using `getServerSession` in API routes to verify sessions, and possibly using `Prisma` directly in server components or via API routes) for simplicity and consistency. Tailwind CSS is used for styling, along with component libraries (NextUI, Radix) for complex widgets like modals or dropdowns. The UI also considers **accessibility** (ARIA attributes and semantic HTML per React best practices) and responsiveness. For instance, tables might use Radix UI components for accessibility and are made searchable/sortable using Tanstack React Table. Chart.js and Recharts are included, indicating there might be dashboards or charts (e.g., metrics of tasks per day, etc.) in the admin interface.

## Coding Conventions and Style Guidelines

The Y3DCore project adheres to strict coding standards to ensure consistency and reliability across the codebase. Below are key conventions derived from the repository’s guidelines and configurations:

- **General (Readability & Consistency):** Code should be clean and self-explanatory with clear naming. Follow existing patterns in the codebase; for example, follow similar function structures and error handling approaches used elsewhere. Maintain modularity and comment any complex logic (especially in mathematical or algorithmic code, e.g., OpenSCAD scripts).

- **TypeScript/JavaScript Style:** The project follows an adaptation of **Airbnb’s JavaScript Style Guide** with Prettier formatting. Key points include:

  - **Indentation:** 2 spaces (no tabs) for all frontend and Node code.
  - **Line Length:** Aim for max \~80 characters per line for readability.
  - **Quotes:** Use single quotes for strings in JavaScript/TypeScript code (Prettier will enforce this).
  - **Naming:** Use `camelCase` for variable and function names. Classes and React components use **PascalCase** (capitalized camelCase). Constants that are truly constant (e.g., config objects) can be UPPER_SNAKE_CASE. For TypeScript types vs interfaces, prefer **interface** declarations for object shapes where possible.
  - **Semicolons:** Generally include semicolons (standard TS/JS practice; not explicitly stated, but Prettier default).
  - **Trailing commas:** Prettier default (which is to add trailing commas where valid in multi-line literals) is used.
  - **Curly braces/bracket spacing:** Handled by Prettier – e.g., one space after `if` and before `(`, etc.

- **TypeScript Practices:** The project is written in **TypeScript** with `"strict": true` compiler settings, which means no implicit `any` and thorough type-checking. All new code should be in TS (no new plain JS unless absolutely needed). However, explicit `any` is tolerated in certain cases (ESLint’s `@typescript-eslint/no-explicit-any` rule is turned **off** to allow flexibility) – use it sparingly and prefer specific types or `unknown` if applicable. Functions do not require explicit return type annotations (`@typescript-eslint/explicit-function-return-type` is off), relying on inference. Use **Generics** for reusable functions where appropriate and leverage utility types from `@types/node` and others (already included). Avoid using the non-null assertion (`!`) unless absolutely certain. Prefer `??` and `?.` (nullish coalescing and optional chaining) for safe property access.

- **React and Next.js Patterns:** By convention, Next.js **Server Components** are used by default for page and layout components to optimize performance (no client-side overhead). Components that need state or browser APIs are marked with `"use client"` at top. **Functional components** with hooks are used exclusively (no class components). Use React Hooks like `useState`, `useEffect` wisely – minimize heavy logic in `useEffect` on client components. Favor **composition** over inheritance for components (e.g., compose smaller components to create bigger ones). Pass data via props from server components to client components as needed, and keep prop types well-defined (use TypeScript interfaces for props).

  - **UI/UX:** Implement proper loading and error states for Next.js routes using the built-in conventions (e.g., `loading.tsx` and `error.tsx` files in app routes). Ensure each page has appropriate metadata (Next.js head tags) for SEO. Use **Tailwind CSS** for utility-first styling, possibly augmented by NextUI components for ready-made UI. Ensure any interactive component is accessible (use Radix UI for accessible primitives, and include ARIA labels when needed).

  - **Forms:** Use libraries like **React Hook Form** for complex forms (the styleguide suggests this), to manage form state and validation cleanly. For simple forms, you can use controlled components with local state. Client-side validation should mirror server-side (e.g., using the same Zod schemas).

- **Backend & API Design:** Next.js API route handlers (in `app/api/**/route.ts`) should always return a standardized JSON response. Use **Zod** for input validation on API endpoints – define a schema for expected request body or query parameters and call `schema.parse()` on the input. If validation fails, return a `400 Bad Request` with a JSON error payload (including details of which fields are wrong). For example, the create-user endpoint uses a Zod schema and returns `{ error: "Invalid input", details: [...] }` with 400 if parsing fails.

  - Use **HTTP status codes** appropriately: 200 for success (or 201 for creation), 400 for bad input, 401 for unauthorized (if a user is not logged in or not admin), 404 for not found, 409 for conflicts (e.g., duplicate record), and 500 for unhandled server errors. The project provides a helper `handleApiError(error)` that catches exceptions in API routes and formats a JSON error with a message and appropriate status code. Always wrap route logic in a `try/catch` and in the `catch`, call `return handleApiError(error)` to ensure the client receives a standardized response and the error is logged. The `handleApiError` will recognize instances of our custom `ApiError` (which includes an HTTP status) and known Prisma errors (like unique constraint violations, etc.) and set meaningful messages/status codes.

  - **Prisma usage:** Interact with the database through the `prisma` client imported from `lib/prisma.ts`. Use **prisma transactions** (`prisma.$transaction`) when performing multi-step operations that must all succeed together (for example, creating an Order and its OrderItems in one go). Avoid unchecked raw SQL; use Prisma query methods for type safety. Handle Prisma exceptions as noted (e.g., catch `PrismaClientKnownRequestError` for specific error codes like P2002 unique violations). When writing queries, include relational data if needed instead of issuing N+1 separate queries – e.g., use `.findMany({ include: { OrderItems: true } })` to get an order with its items in one query. This ensures efficiency given the large volume (the system is expected to handle 1000+ orders/day). Add database **indexes** for any field that is frequently filtered or sorted (the schema already indexes fields like order `created_at`, `marketplace`, and task `status` and `stl_render_state` to optimize queries).

- **Error Handling & Logging:** The philosophy is to **fail gracefully** and informatively. All major operations (API routes, scripts, worker tasks) are wrapped in try/catch. On the server side, errors are logged to console (and thus to server logs) with contextual information. The logger (`lib/shared/logging.ts`) should be used where possible, e.g. `logger.error("ShipStation API failed", { orderId, error })` to include JSON context. In API routes, avoid leaking internal stack traces to the client; instead return a friendly message (handled via `handleApiError` as described, which returns e.g. `{ message: "Internal Server Error" }` with a 500). Some client-side code might catch errors (e.g., in a form submission) and display a user-friendly message.

  - **Custom Errors:** The project defines an `ApiError` class (in `lib/errors.ts`) that extends Error and carries an HTTP status and optional details. Throw `new ApiError("message", 400)` in scenarios where you want to directly signal an HTTP error in route logic – the handler will catch it and respond accordingly. For example, if a user tries to create a duplicate entry, you might throw an ApiError(“User already exists”, 409) which results in a 409 Conflict JSON response.

  - **Front-end Errors:** Utilize Next.js error boundaries (`error.tsx`) to catch rendering errors on pages. Also, use try/catch in client-side async calls (like using fetch from the browser) and display notifications if something fails. The style guide emphasizes input validation and sanitization to prevent crashes and security issues.

- **Security Best Practices:** Never commit secrets to the repo – all API keys and passwords are pulled from environment variables (`process.env`) at runtime. Use NextAuth’s secure cookies for sessions and the built-in CSRF protection it provides for any sensitive endpoints. Validate **all inputs** on both client and server side (Zod helps on server; for client, basic checks if needed). Sanitize any user-provided content that will be rendered (to prevent XSS) – e.g., if you ever render a custom text on a page or in an STL, ensure it’s alphanumeric or safely escaped. The ShipStation webhooks (if any) or API callbacks should be verified (e.g., HMAC signature if provided). Database queries are parameterized through Prisma, so SQL injection is mitigated by design. Use HTTPS for any outgoing requests to APIs (ShipStation, OpenAI, etc., all require HTTPS). The Next.js app should be served over HTTPS in production as well.

- **Testing and QA:** The project uses **Vitest** (a Vite-compatible test runner) for unit tests. Test files are located alongside code in `__tests__` directories or with a `.test.ts` suffix. Key areas to unit test include utility functions (e.g., mapping functions, text extraction logic). Tests should mock external API calls – for example, use a dummy ShipStation response or stub OpenAI API calls to not hit the real service during tests. There are likely integration tests as well (possibly using Playwright, since it’s mentioned, for end-to-end testing of the UI). When writing new code, accompany it with tests if it contains non-trivial logic or if it could regress easily. For example, a function parsing color codes from a string should have a couple of unit tests with sample inputs.

  The testing approach emphasizes covering both typical and error scenarios (e.g., test that `populate-print-queue` handles an order with missing personalization by flagging review, and that `handleApiError` returns the correct status for a known error). Use factories or test data defined in a `tests/` utility to generate consistent inputs.

- **Git & Collaboration:** (While not directly impacting Copilot’s code generation, it’s worth noting) The project follows **Conventional Commits** for git messages (e.g., “feat: add new planner grouping logic”). Code is reviewed via pull requests, ensuring every change gets a second set of eyes for standards compliance. Copilot suggestions should thus aim to meet the standards above so that they pass review.

## Configuration Patterns and Environment Modes

Y3DHub supports multiple runtime configurations:

- **Development vs Production:** In development, `npm run dev` starts the Next.js app with hot-reload and uses the local MySQL (or an alternate SQLite DB for offline mode). Prisma migrations can be run in dev via `prisma migrate dev`. In production, `npm run build && npm start` builds and serves a optimized app. The Next.js config (`next.config.js`) likely enables the experimental App Router and maybe some rewrites, but is largely default. One key config is **TypeScript path alias**: the `tsconfig.json` sets `baseUrl: "src"` and a path alias `"@/*": ["*"]` so that imports can use `@/` prefix for any src modules (e.g., `import { prisma } from '@/lib/prisma';`). Copilot should prefer these absolute imports with `@/` for internal modules as it’s the project standard.

- **Offline Mode:** There is an “offline development” mode intended for working without internet (useful for developing AI features without calling external APIs, or working on a plane, etc.). In this mode, an SQLite database is used instead of MySQL, and certain features are stubbed. Scripts `npm run dev:offline` and `npm run build:offline` switch the app to this mode. Likely, an alternate Prisma schema (`schema.sqlite.prisma`) or datasource URL is used for SQLite. Also, the OpenAI calls might be disabled or pointed to a mock. The code might check `process.env.OFFLINE === "true"` or similar to decide whether to call real APIs or use dummy data. When generating code, consider if offline support is needed (for example, if writing a new feature that calls an API, perhaps have a fallback if offline).

- **Environment Variables:** Key env vars include:

  - **Database**: `DATABASE_URL` (connection string for Prisma, e.g., MySQL or SQLite)
  - **ShipStation**: `SHIPSTATION_API_KEY`, `SHIPSTATION_API_SECRET`, and possibly `SHIPSTATION_PARTNER_KEY` if required by their API.
  - **OpenAI**: `OPENAI_API_KEY` for making OpenAI API calls. Possibly `OPENAI_API_BASE` if using a proxy endpoint.
  - **NextAuth**: `NEXTAUTH_SECRET` (for session encryption) and `NEXTAUTH_URL`. If email provider or others were used, their creds too, but with Credentials provider the main secret is used for cookies.
  - **SendGrid**: `SENDGRID_API_KEY` for email sending.
  - **OpenSCAD**: `OPENSCAD_EXECUTABLE_PATH` if the OpenSCAD binary is not in PATH or a custom path is needed.
  - **Google Drive**: `GDRIVE_ENABLED` (set `"true"` to enable upload) and `GDRIVE_FOLDER_ID` (the root Drive folder to upload into), plus `GDRIVE_SERVICE_ACCOUNT_PATH` (path to the JSON key file for service account credentials).
  - **Misc:** `DEBUG` (if set, enables verbose debug logging from our logger for level 'debug'), and perhaps feature flags for things like plate planner. Also, since Next.js, variables prefixed with `NEXT_PUBLIC_` would be exposed to the client if any (e.g., `NEXT_PUBLIC_ANALYTICS_ID` for some analytics).

Ensure any new code uses `process.env` for configurable values rather than hardcoding them. For example, if adding a new integration, define its API keys in env and read them in the code.

- **Naming Conventions (Files & Directories):** Files are generally named in **kebab-case** (dash-separated) or lowercase, especially in Next.js app route (e.g., `print-queue/page.tsx`). React component files might be PascalCase if they export a component, but within the app directory it’s common to keep file names lowercase except for component files outside of app. Directory names are lowercase (e.g., `lib/shipstation`, `lib/openscad`). Test files mirror the source file name but with `.test.ts`. Migration files under `prisma/migrations` are timestamp-prefixed. When generating new files or modules, follow similar naming; e.g., a new utility for orders should reside in an appropriate `lib/[domain]` subfolder rather than top-level, and tests in a `__tests__` folder next to it.

- **Commits and Documentation:** When you implement a new feature or change, if it impacts the architecture or public interface, update the relevant docs in `docs/`. Documentation is taken seriously to keep the team (and AI assistants like Copilot) informed. The `.cursorrules` and `.claude/styleguide.md` are in the repo to guide AI tools, so you can also update those if conventions change. Commits should be in the _conventional commits_ style (e.g., `feat:`, `fix:`, `docs:` prefixes) which helps in changelog generation.

## Example Patterns and Best Practices

By following the established patterns in Y3DHub’s code, Copilot can generate output that seamlessly fits into the project. Here are some typical examples:

- **API Route Handler Pattern:** Every API route uses the **same basic structure**: validate input → perform action → return JSON or error. For instance, the **Create User** endpoint (in `api/users/route.ts`) reads `request.json()`, parses it with a Zod schema, and handles errors like this:

  ```ts
  // Inside POST handler
  const body = await request.json();
  const data = createUserSchema.parse(body); // may throw ZodError
  // ... use data to perform operation (e.g., prisma.user.create)
  return NextResponse.json(result, { status: 201 });
  ```

  If parsing fails, it catches the `ZodError` and returns `NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })`. After performing the database action, it returns the created user object with a 201 status. Any unexpected error is caught by the outer try/catch, logged, and passed to `handleApiError(error)` which will produce a standardized `{ message: "..."} ` JSON with an appropriate status (500 or specific if ApiError). **Guideline:** Follow this pattern for new routes – always validate inputs and use `handleApiError` in catch blocks so that error responses are uniform and helpful.

- **CLI Script Pattern:** Scripts in `src/scripts` typically import the needed lib functions and use a CLI arg parsing library like **Yargs** or **Commander** (or even simple `process.argv` parsing) to handle flags. For example, `sync-orders.ts` likely uses `yargs` to define options `--days-back`, `--order-id`, etc., and then calls an async function to perform the sync. After execution, it might log a summary and call `process.exit(0)` or allow the Node process to exit naturally. **Guideline:** New scripts should similarly structure: define config (possibly at top), use `prisma` from the lib (ensuring to import the correct one with `@/lib/prisma` alias), and wrap the main logic in a try/catch to log errors. Also, if the script uses `console.log` for output, format messages clearly (the existing scripts often print headings and color-coded messages to console for clarity).

- **Logging Usage:** Rather than using `console.log` directly throughout the app, get a logger instance from `lib/shared/logging.ts`. For example, in a module for ShipStation sync, do `const logger = getLogger("ShipStationSync")` at top, then use `logger.info("Fetched orders", { count: orders.length })`. This will produce a JSON log line with a timestamp, level, component, message, and context object. This unified logging makes it easier to filter logs in production. **Guideline:** Use `logger.error` instead of console.error for errors within the app logic (the only place we intentionally use raw `console.error` is in the global `handleApiError` where a caught error is output, to avoid missing stack trace). The logger also respects a `DEBUG` env: use `logger.debug` for verbose logs that should be hidden normally but appear when debugging is enabled.

- **Data Access and Computation:** Use **functional programming style** for data transformations when possible (map, filter, reduce) – this aligns with modern JS and the existing code uses such patterns (for instance, mapping ShipStation API JSON to Prisma input objects). Keep functions pure unless they specifically perform I/O or DB updates. For heavy computations (like possibly complex mesh calculations or image processing), consider offloading to a worker or a WebWorker in the browser if needed – but currently most heavy work (STL gen) is in the Node worker.

- **Example – Creating a Print Task:** Suppose we want to add a new function to manually create a print task for an order item (for a hypothetical scenario where an admin can trigger task creation for an order on demand). Following project patterns, we would:

  1. Add a new API route (e.g., `api/print-tasks/create/route.ts`). In the handler, use `POST` method, parse JSON body for `orderItemId` or details, validate with Zod (ensure `orderItemId` is number).
  2. In the business logic, use `prisma.orderItem.findUnique` to get the item and related Order/Product. Check that a PrintOrderTask isn’t already present for that item. Then reuse existing library functions if available (maybe refactor the logic in `populate-print-queue.ts` into a reusable function that can be called here). If none, implement similar steps: extract personalization (call AI if needed, or if the request body includes personalization, use that), determine product mapping (perhaps `lib/products` has a function to get product by SKU), then `prisma.printOrderTask.create` with appropriate data.
  3. If any step fails (e.g., AI call error), consider throwing `ApiError` with a message (which results in a proper error JSON). On success, return the created task JSON with 201. Use `handleApiError` in catch as usual.
     This hypothetical example touches many aspects: using Prisma, AI integration, following the error handling and validation patterns – all of which are covered by existing code that can be referenced (e.g., populate-print-queue logic and users route for structure).

- **Dual Color Example:** The system supports dual-color prints (e.g., two-tone keychains). In code, this means a PrintOrderTask may have `color_1` and `color_2` fields. The OpenSCAD model (e.g., `DualColour.scad`) expects parameters for two colors. The print task creation logic ensures that if a product is dual-color, both color fields are populated (if the order only specifies one color, perhaps the second is set to a default or the task is flagged for review). When the STL worker runs, it will pass both colors into OpenSCAD. If Copilot is asked to modify or create any code around color handling, it should preserve this logic – e.g., always validate that required colors are present. In practice, color strings could be simple names (“Red”) or hex codes depending on input; the system likely normalizes color names (maybe `lib/orders` or `lib/products` contains a color mapping, given there's a `colors.ts` file) to ensure consistency.

- **Plate Planning AI Prompt:** The prompt files in `lib/ai/prompts` show how the AI is guided. If writing code that interacts with these, say generating a prompt for the AI, follow the pattern: the repository uses template text files for prompts and might use placeholders or JSON. For example, `generate-grouping-prompt.ts` (mentioned in AGENTS.md) likely takes pending tasks data and inserts it into a prompt template to ask ChatGPT for an optimized grouping. Copilot should be aware that prompt engineering is part of this codebase – any changes to prompts should maintain clarity and include system/user role separation if needed, and any code should handle the tokens limit (maybe the code counts tasks and splits into multiple prompts if too many).

By adhering to these established patterns and guidelines, any new code generated will integrate smoothly with Y3DHub’s codebase, and Copilot’s suggestions will be more aligned with the project’s needs. Remember to always **use existing code as reference** (many common use-cases have examples in this repository – e.g., how to write a Next.js route, how to query Prisma for a certain case, how to log properly). Consistency is key in this project’s collaborative environment, and these instructions should equip Copilot to provide consistent and context-aware assistance.
