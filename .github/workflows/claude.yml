name: <PERSON> Assistant

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  issues:
    types: [opened, assigned]
  pull_request_review:
    types: [submitted]

jobs:
  claude-assistant:
    if: |
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
      (github.event_name == 'issues' && (contains(github.event.issue.body, '@claude') || contains(github.event.issue.title, '@claude')))
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      issues: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: <PERSON> Code Assistant Response
        uses: actions/github-script@v7
        with:
          script: |
            const response = `## 🤖 <PERSON> Code Assistant

            Hi! I noticed you mentioned @claude in this ${context.eventName === 'issues' ? 'issue' : 'PR'}.

            ### 📋 Available Commands
            - **Code Review**: I can help review code quality, performance, and security
            - **Technical Guidance**: Ask about Y3DHub architecture, patterns, and best practices  
            - **Debugging Help**: Assistance with complex business logic and integrations
            - **Performance Analysis**: Optimization suggestions for high-volume operations

            ### 🔧 Current Capabilities
            - TypeScript type safety analysis
            - Database query optimization
            - API integration patterns  
            - 3D printing workflow guidance
            - Security best practices

            ### 💡 How to Use
            For now, please use **Claude Code CLI** directly in your development environment for the most comprehensive assistance:

            \`\`\`bash
            # Install Claude Code CLI
            npm install -g @anthropic/claude-code

            # Use in your Y3DHub project
            claude review src/app/api/
            claude analyze --performance src/lib/
            \`\`\`

            ### 🚀 Coming Soon
            Direct GitHub integration with Anthropic's official Claude Code action is being developed.

            ---
            *This is an automated response. For immediate assistance, use Claude Code CLI or visit the [Y3DHub documentation](${context.repo.html_url}/blob/main/README.md).*`;

            if (context.eventName === 'issues') {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: response
              });
            } else if (context.eventName === 'pull_request_review_comment' || context.eventName === 'issue_comment') {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: response
              });
            }
