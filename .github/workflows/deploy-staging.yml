name: Deploy to Staging

on:
  push:
    branches: [staging] # Only deploy to staging on staging branch
  workflow_dispatch: # Allow manual trigger

jobs:
  deploy:
    name: Deploy to Staging Server
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Staging Server
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: ${{ secrets.PROD_PORT || 22 }}
          script: |
            set -e
            echo "🚀 Starting deployment to staging..."

            # Navigate to staging deployment directory
            cd /home/<USER>/Y3DHub_staging_deploy || {
              echo "Creating staging deployment directory..."
              git clone https://github.com/y3dltd/Y3DCore.git /home/<USER>/Y3DHub_staging_deploy
              cd /home/<USER>/Y3DHub_staging_deploy
            }

            # Backup current state
            echo "📦 Creating backup..."
            git stash push -m "Auto-backup before staging deployment $(date)"

            # Pull latest changes from staging branch
            echo "⬇️ Pulling latest changes from staging..."
            git fetch origin
            git checkout staging # Ensure we are on the staging branch
            echo "🔄 Resetting local staging to match origin/staging..."
            git reset --hard origin/staging # This will discard any local changes/commits on staging

            # Environment files should be pre-configured in each deployment directory
            echo "📋 Using pre-configured staging environment..."
            echo "✅ Staging environment: port 3001"

            # Install dependencies
            echo "📦 Installing dependencies..."
            npm ci

            # Generate Prisma client
            echo "🗄️ Generating Prisma client..."
            npx prisma generate

            # Run database migrations (safe on staging)
            echo "🗄️ Running database migrations..."
            npx prisma migrate deploy

            # Build application
            echo "🔨 Building application..."
            npm run build

            # Install PM2 if not found
            echo "🔧 Checking PM2 installation..."
            if ! command -v pm2 &> /dev/null; then
              echo "📦 Installing PM2 globally..."
              sudo npm install -g pm2
            else
              echo "✅ PM2 already installed"
            fi

            # Restart staging process on port 3001
            echo "🔄 Restarting staging process..."
            pm2 delete Y3DHub_Staging 2>/dev/null || true
            pm2 start npm --name "Y3DHub_Staging" -- start -- -p 3001

            # Health check
            echo "🩺 Running health check..."
            sleep 10
            curl -f http://localhost:3001/ || echo "⚠️ Health check failed, but deployment completed"

            echo "✅ Staging deployment completed successfully!"

            # Send notification (optional)
            echo "📧 Staging deployment completed at $(date)" >> /tmp/staging-deployment.log
