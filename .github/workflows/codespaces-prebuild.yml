name: Codespaces Prebuild

on:
  push:
    branches: [main] # Only prebuild on main branch
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  prebuild:
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'y3dltd' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma client
        run: npx prisma generate

      - name: Type check
        run: npm run type-check

      - name: Lint
        run: npm run lint

      - name: Build Next.js
        run: npm run build

      - name: Cache build artifacts
        uses: actions/cache@v4
        with:
          path: |
            .next
            node_modules
          key: ${{ runner.os }}-nextjs-${{ hashFiles('package-lock.json') }}-${{ hashFiles('**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('package-lock.json') }}-
            ${{ runner.os }}-nextjs-
