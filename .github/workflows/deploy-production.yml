name: Deploy to Production

on:
  push:
    branches: [main] # Only deploy to production on main branch
  workflow_dispatch: # Allow manual trigger

jobs:
  deploy:
    name: Deploy to Production Server
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to Production Server
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          port: ${{ secrets.PROD_PORT || 22 }}
          script: |
            set -e
            echo "🚀 Starting deployment to production..."

            # Navigate to production directory
            cd /home/<USER>/Y3DHub_production || {
              echo "Creating production directory..."
              git clone https://github.com/y3dltd/Y3DCore.git /home/<USER>/Y3DHub_production
              cd /home/<USER>/Y3DHub_production
            }

            # Backup current state
            echo "📦 Creating backup..."
            git stash push -m "Auto-backup before deployment $(date)"

            # Pull latest changes
            echo "⬇️ Pulling latest changes from main..."
            git fetch origin
            git checkout main
            git pull origin main

            # Environment files should be pre-configured in each deployment directory
            echo "📋 Using pre-configured production environment..."
            echo "✅ Production environment: port 3000, y3dhub_prod database"
            # Install dependencies
            echo "📦 Installing dependencies..."
            npm ci

            # Generate Prisma client
            echo "🗄️ Generating Prisma client..."
            npx prisma generate

            # Run database migrations
            echo "🗄️ Running database migrations..."
            npx prisma migrate deploy

            # Build application
            echo "🔨 Building application..."
            npm run build

            # Install PM2 if not found
            echo "🔧 Checking PM2 installation..."
            if ! command -v pm2 &> /dev/null; then
              echo "📦 Installing PM2 globally..."
              sudo npm install -g pm2
            else
              echo "✅ PM2 already installed"
            fi

            # Simple PM2 restart (much simpler approach)
            echo "🔄 Restarting production process..."
            # Stop existing production process only
            pm2 delete Y3DHub_Production 2>/dev/null || true
            # Start production on port 3000 using the correct script
            pm2 start npm --name "Y3DHub_Production" -- run start-prod-http-3000

            # Health check
            echo "🩺 Running health check..."
            sleep 10
            curl -f http://localhost:3000/ || echo "⚠️ Health check failed, but deployment completed"

            echo "✅ Deployment completed successfully!"

            # Send notification (optional)
            echo "📧 Deployment completed at $(date)" >> /tmp/deployment.log
