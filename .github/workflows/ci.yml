name: CI

on:
  pull_request:
    branches: [main, staging] # Only run on PRs, not direct pushes

jobs:
  basic-checks:
    name: Basic Checks
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: TypeScript check
        run: npm run type-check

      - name: Build check
        env:
          DATABASE_URL: 'sqlite:./dev.db'
          NEXTAUTH_SECRET: 'ci-secret-for-build-only-not-for-production-use-32-chars'
          NEXTAUTH_URL: 'http://localhost:3000'
          NODE_ENV: 'production'
          SKIP_DATABASE_VALIDATION: 'true'
        run: |
          # Use SQLite schema for CI builds
          cp prisma/schema.sqlite.prisma prisma/schema.prisma
          npx prisma generate
          npm run build
