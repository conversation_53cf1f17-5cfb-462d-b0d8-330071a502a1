#!/usr/bin/env bash
set -euo pipefail

# Usage: scripts/setup-sqlite-db.sh [db_path]
# Creates a local SQLite database using the schema designed for testing.

DB_PATH="${1:-./test.db}"
SCHEMA="prisma/schema.sqlite.prisma"

export DATABASE_URL="file:${DB_PATH}"

echo "\n📦 Generating SQLite database at ${DB_PATH}..."

npx prisma db push --schema "$SCHEMA" --skip-generate
npx prisma db seed --schema "$SCHEMA"

echo "✅ SQLite database ready: ${DB_PATH}"
