#!/bin/bash

# Y3DHub Crontab Setup Script
# This script sets up the crontab for Y3DHub production and staging environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if directory exists
check_directory() {
    local dir=$1
    if [ ! -d "$dir" ]; then
        print_error "Directory $dir does not exist!"
        return 1
    fi
    return 0
}

# Function to check if npm scripts exist
check_npm_scripts() {
    local dir=$1
    print_status "Checking npm scripts in $dir..."
    
    cd "$dir" || return 1
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in $dir"
        return 1
    fi
    
    # Check for required scripts
    local required_scripts=("worker:stl" "full-workflow" "check:sync-status" "check:amazon-urls" "cleanup-print-tasks" "clean")
    
    for script in "${required_scripts[@]}"; do
        if ! npm run | grep -q "$script"; then
            print_warning "Script '$script' not found in package.json"
        else
            print_success "Found script: $script"
        fi
    done
    
    return 0
}

# Function to create log directory
setup_log_directory() {
    print_status "Setting up log directory..."
    
    local log_dir="/var/log/y3dhub"
    
    if [ ! -d "$log_dir" ]; then
        print_status "Creating log directory: $log_dir"
        sudo mkdir -p "$log_dir"
        sudo chown jayson:jayson "$log_dir"
        sudo chmod 755 "$log_dir"
        print_success "Log directory created successfully"
    else
        print_success "Log directory already exists"
    fi
    
    # Create individual log files with proper permissions
    local log_files=(
        "stl-worker.log"
        "full-workflow.log"
        "sync-status.log"
        "amazon-urls.log"
        "cleanup.log"
        "system-cleanup.log"
        "staging-stl-worker.log"
        "staging-full-workflow.log"
        "staging-sync-status.log"
    )
    
    for log_file in "${log_files[@]}"; do
        local full_path="$log_dir/$log_file"
        if [ ! -f "$full_path" ]; then
            sudo touch "$full_path"
            sudo chown jayson:jayson "$full_path"
            sudo chmod 644 "$full_path"
            print_status "Created log file: $log_file"
        fi
    done
}

# Function to setup log rotation
setup_log_rotation() {
    print_status "Setting up log rotation..."
    
    local logrotate_config="/etc/logrotate.d/y3dhub"
    
    if [ ! -f "$logrotate_config" ]; then
        print_status "Creating logrotate configuration..."
        sudo tee "$logrotate_config" > /dev/null << 'EOF'
/var/log/y3dhub/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 jayson jayson
}
EOF
        print_success "Logrotate configuration created"
    else
        print_success "Logrotate configuration already exists"
    fi
}

# Function to generate crontab entries
generate_production_crontab() {
    cat << 'EOF'

# Y3DHub Production Cron Jobs - Auto-generated
# STL Render Worker - Every 2 minutes
*/2 * * * * cd /home/<USER>/Y3DHub_production && npm run worker:stl >> /var/log/y3dhub/stl-worker.log 2>&1

# Full Workflow - Every 5 minutes
*/5 * * * * cd /home/<USER>/Y3DHub_production && npm run full-workflow >> /var/log/y3dhub/full-workflow.log 2>&1

# Sync Status Check - Every 30 minutes
*/30 * * * * cd /home/<USER>/Y3DHub_production && npm run check:sync-status >> /var/log/y3dhub/sync-status.log 2>&1

# Amazon URL Check - Every hour
0 * * * * cd /home/<USER>/Y3DHub_production && npm run check:amazon-urls >> /var/log/y3dhub/amazon-urls.log 2>&1

# Cleanup Print Tasks - Daily at 2 AM
0 2 * * * cd /home/<USER>/Y3DHub_production && npm run cleanup-print-tasks >> /var/log/y3dhub/cleanup.log 2>&1

# System Cleanup - Daily at 3 AM
0 3 * * * cd /home/<USER>/Y3DHub_production && npm run clean >> /var/log/y3dhub/system-cleanup.log 2>&1

EOF
}

# Function to generate staging crontab entries
generate_staging_crontab() {
    cat << 'EOF'

# Y3DHub Staging Cron Jobs - Auto-generated
# STL Render Worker - Every 5 minutes (staging)
*/5 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run worker:stl >> /var/log/y3dhub/staging-stl-worker.log 2>&1

# Full Workflow - Every 10 minutes (staging)
*/10 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run full-workflow >> /var/log/y3dhub/staging-full-workflow.log 2>&1

# Sync Status Check - Every hour (staging)
0 * * * * cd /home/<USER>/Y3DHub_staging_deploy && npm run check:sync-status >> /var/log/y3dhub/staging-sync-status.log 2>&1

EOF
}

# Function to install crontab
install_crontab() {
    local environment=$1
    
    print_status "Installing crontab for $environment environment..."
    
    # Backup existing crontab
    local backup_file="/tmp/crontab_backup_$(date +%Y%m%d_%H%M%S)"
    crontab -l > "$backup_file" 2>/dev/null || echo "# No existing crontab" > "$backup_file"
    print_status "Existing crontab backed up to: $backup_file"
    
    # Create new crontab
    local temp_crontab="/tmp/new_crontab_$$"
    
    # Start with existing crontab (excluding old Y3DHub entries)
    grep -v "Y3DHub" "$backup_file" > "$temp_crontab" 2>/dev/null || true
    
    # Add new entries based on environment
    if [ "$environment" = "production" ]; then
        generate_production_crontab >> "$temp_crontab"
    elif [ "$environment" = "staging" ]; then
        generate_staging_crontab >> "$temp_crontab"
    elif [ "$environment" = "both" ]; then
        generate_production_crontab >> "$temp_crontab"
        generate_staging_crontab >> "$temp_crontab"
    fi
    
    # Install new crontab
    crontab "$temp_crontab"
    rm "$temp_crontab"
    
    print_success "Crontab installed successfully for $environment"
}

# Function to show current crontab
show_crontab() {
    print_status "Current crontab entries:"
    echo "----------------------------------------"
    crontab -l 2>/dev/null || echo "No crontab entries found"
    echo "----------------------------------------"
}

# Function to test scripts
test_scripts() {
    local dir=$1
    print_status "Testing scripts in $dir..."
    
    if ! check_directory "$dir"; then
        return 1
    fi
    
    cd "$dir" || return 1
    
    # Test each script with dry run or help flags where available
    print_status "Testing worker:stl script..."
    timeout 10s npm run worker:stl --help 2>/dev/null || print_warning "worker:stl test timed out or failed"
    
    print_status "Testing check:sync-status script..."
    timeout 30s npm run check:sync-status -- --help 2>/dev/null || print_warning "check:sync-status test failed"
    
    print_success "Script testing completed"
}

# Main function
main() {
    print_status "Y3DHub Crontab Setup Script"
    print_status "============================"
    
    # Parse command line arguments
    local environment="production"
    local test_only=false
    local show_only=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --environment|-e)
                environment="$2"
                shift 2
                ;;
            --test-only|-t)
                test_only=true
                shift
                ;;
            --show|-s)
                show_only=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  -e, --environment ENV    Environment: production, staging, or both (default: production)"
                echo "  -t, --test-only         Only test scripts, don't install crontab"
                echo "  -s, --show              Show current crontab and exit"
                echo "  -h, --help              Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Show current crontab if requested
    if [ "$show_only" = true ]; then
        show_crontab
        exit 0
    fi
    
    # Validate environment parameter
    if [[ ! "$environment" =~ ^(production|staging|both)$ ]]; then
        print_error "Invalid environment: $environment. Must be 'production', 'staging', or 'both'"
        exit 1
    fi
    
    # Setup log directory and rotation
    setup_log_directory
    setup_log_rotation
    
    # Test scripts if requested
    if [ "$test_only" = true ]; then
        if [[ "$environment" =~ ^(production|both)$ ]]; then
            test_scripts "/home/<USER>/Y3DHub_production"
        fi
        if [[ "$environment" =~ ^(staging|both)$ ]]; then
            test_scripts "/home/<USER>/Y3DHub_staging_deploy"
        fi
        exit 0
    fi
    
    # Check directories exist
    if [[ "$environment" =~ ^(production|both)$ ]]; then
        check_directory "/home/<USER>/Y3DHub_production" || exit 1
        check_npm_scripts "/home/<USER>/Y3DHub_production" || exit 1
    fi
    
    if [[ "$environment" =~ ^(staging|both)$ ]]; then
        check_directory "/home/<USER>/Y3DHub_staging_deploy" || exit 1
        check_npm_scripts "/home/<USER>/Y3DHub_staging_deploy" || exit 1
    fi
    
    # Install crontab
    install_crontab "$environment"
    
    # Show final crontab
    echo ""
    show_crontab
    
    print_success "Crontab setup completed successfully!"
    print_status "Monitor logs in /var/log/y3dhub/"
    print_status "Check cron service: sudo systemctl status cron"
    print_status "View cron logs: sudo tail -f /var/log/syslog | grep CRON"
}

# Run main function with all arguments
main "$@"
