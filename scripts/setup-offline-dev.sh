#!/usr/bin/env bash
set -euo pipefail

# Setup offline development environment for Codex AI and other offline tools
# This creates a local SQLite database with sample data for development

DB_PATH="./dev.db"
SCHEMA="prisma/schema.sqlite.prisma"

echo "🔧 Setting up offline development environment..."

# Remove existing database
if [ -f "$DB_PATH" ]; then
    echo "   Removing existing database: $DB_PATH"
    rm "$DB_PATH"
fi

# Set database URL for SQLite
export DATABASE_URL="file:$DB_PATH"

echo "   Using schema: $SCHEMA"
echo "   Database path: $DB_PATH"

# Generate Prisma client for SQLite
echo "📦 Generating Prisma client for SQLite..."
npx prisma generate --schema "$SCHEMA"

# Push schema to SQLite (creates the database)
echo "🗄️  Creating SQLite database..."
npx prisma db push --schema "$SCHEMA" --skip-generate --accept-data-loss

# Seed with sample data
echo "🌱 Seeding database with sample data..."
npx prisma db seed --schema "$SCHEMA"

echo "✅ Offline development environment ready!"
echo ""
echo "💡 Usage:"
echo "   npm run dev:offline    # Start development server with SQLite"
echo "   npm run build:offline  # Build project with SQLite"
echo ""
echo "📂 Database location: $DB_PATH"
echo "🔗 Database URL: file:$DB_PATH"
echo ""
echo "🚀 Ready for Codex AI offline development!"