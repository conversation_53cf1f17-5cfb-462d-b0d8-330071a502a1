#!/usr/bin/env bash
set -euo pipefail

# Initialize prisma/schema.prisma based on DATABASE_URL presence in .env
# If DATABASE_URL is set, use the MySQL schema; otherwise fall back to SQLite.

# Load .env to pick up DATABASE_URL if present
if [[ -f .env ]]; then
  set -a
  source .env
  set +a
fi

SCHEMA_DIR="prisma"
MYSQL_SCHEMA_FILE="schema.mysql.prisma"
SQLITE_SCHEMA_FILE="schema.sqlite.prisma"

# Preserve original DATABASE_URL for logging
orig_url="${DATABASE_URL:-}"

if [[ -n "$orig_url" ]]; then
  echo "🗄️  Using MySQL schema"
  cp "${SCHEMA_DIR}/${MYSQL_SCHEMA_FILE}" "${SCHEMA_DIR}/schema.prisma"
else
  echo "🛠️  DATABASE_URL unset, using SQLite schema"
  export DATABASE_URL="file:./dev.db"
  cp "${SCHEMA_DIR}/${SQLITE_SCHEMA_FILE}" "${SCHEMA_DIR}/schema.prisma"
fi

echo "✅ schema.prisma set to $([[ -n "$orig_url" ]] && echo 'MySQL' || echo 'SQLite') (DATABASE_URL=$DATABASE_URL)"