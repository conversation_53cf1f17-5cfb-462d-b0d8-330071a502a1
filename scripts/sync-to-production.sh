#!/bin/bash
set -e

echo "🚀 Syncing staging changes to production..."

PROD_DIR="/home/<USER>/Y3DHub_prod"
STAGING_DIR="/home/<USER>/Y3DHub_staging"

# Check if we're in staging directory
if [[ ! -f "package.json" ]] || [[ ! -d ".git" ]]; then
    echo "❌ Please run this script from the Y3DHub_staging directory"
    exit 1
fi

# Check if prod directory exists
if [[ ! -d "$PROD_DIR" ]]; then
    echo "❌ Production directory not found: $PROD_DIR"
    exit 1
fi

echo "📂 Staging: $STAGING_DIR"
echo "📂 Production: $PROD_DIR"

# Copy key configuration files that should be synced
echo "📋 Syncing configuration files..."

# VS Code settings (if not ignored in prod)
if [[ -d ".vscode" ]]; then
    rsync -av --delete .vscode/ "$PROD_DIR/.vscode/"
    echo "✅ VS Code settings synced"
fi

# Git hooks
if [[ -d ".husky" ]] && [[ ! -d "$PROD_DIR/.husky" ]]; then
    cp -r .husky "$PROD_DIR/"
    echo "✅ Git hooks synced"
fi

# Configuration files
for file in \
    "lint-staged.config.js" \
    "commitlint.config.mjs" \
    "eslint.config.mjs" \
    "vitest.config.ts" \
    "vitest.setup.ts" \
    "tsconfig.json" \
    ".prettierrc" \
    "components.json"
do
    if [[ -f "$file" ]]; then
        cp "$file" "$PROD_DIR/"
        echo "✅ $file synced"
    fi
done

# DevContainer configs
if [[ -d ".devcontainer" ]]; then
    rsync -av --delete .devcontainer/ "$PROD_DIR/.devcontainer/"
    echo "✅ DevContainer configs synced"
fi

# GitHub workflows
if [[ -d ".github" ]]; then
    rsync -av --delete .github/ "$PROD_DIR/.github/"
    echo "✅ GitHub workflows synced"
fi

echo ""
echo "🎯 Next steps for production:"
echo "1. cd $PROD_DIR"
echo "2. git add . && git commit -m 'sync: update configs from staging'"
echo "3. npm ci (if package.json changed)"
echo "4. npm run build"
echo "5. pm2 restart all"
echo ""
echo "✅ Configuration sync completed!"