#!/bin/bash

# Reorganize STL files from nested structure to correct Azure structure
# Move files from /media/shared/shared/stl-files/* to /media/shared/stl-files/*

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

SOURCE_DIR="/media/shared/shared/stl-files"
TARGET_DIR="/media/shared/stl-files"

echo_info "🔄 Reorganizing STL files in Azure File Share"
echo_info "============================================="

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
    echo_error "Source directory not found: $SOURCE_DIR"
    exit 1
fi

# Create target directory if it doesn't exist
echo_info "Creating target directory structure..."
mkdir -p "$TARGET_DIR"

# Count files before moving
TOTAL_FILES=$(find "$SOURCE_DIR" -name "*.stl" -type f | wc -l)
echo_info "Found $TOTAL_FILES STL files to reorganize"

# Function to move files from a subdirectory
move_category() {
    local category="$1"
    local source_cat_dir="$SOURCE_DIR/$category"
    local target_cat_dir="$TARGET_DIR/$category"
    
    if [ -d "$source_cat_dir" ]; then
        echo_info "Processing category: $category"
        
        # Create target category directory
        mkdir -p "$target_cat_dir"
        
        # Count files in this category
        local file_count=$(find "$source_cat_dir" -name "*.stl" -type f | wc -l)
        
        if [ $file_count -gt 0 ]; then
            echo_info "  Moving $file_count files from $category..."
            
            # Move all STL files, preserving directory structure
            find "$source_cat_dir" -name "*.stl" -type f | while read -r file; do
                # Get relative path from source category dir
                rel_path="${file#$source_cat_dir/}"
                target_file="$target_cat_dir/$rel_path"
                
                # Create target subdirectory if needed
                target_subdir=$(dirname "$target_file")
                mkdir -p "$target_subdir"
                
                # Move the file
                mv "$file" "$target_file"
                echo "    Moved: $rel_path"
            done
            
            echo_success "  Completed $category: $file_count files moved"
        else
            echo_info "  No STL files found in $category"
        fi
    else
        echo_warning "  Category directory not found: $category"
    fi
}

# Move files from each category
echo_info "Moving files by category..."
move_category "bubble-style"
move_category "dual-colours" 
move_category "reg-keys"
move_category "signature-style"
move_category "Others"

# Handle any other subdirectories
echo_info "Checking for additional subdirectories..."
for subdir in "$SOURCE_DIR"/*; do
    if [ -d "$subdir" ]; then
        category=$(basename "$subdir")
        case "$category" in
            "bubble-style"|"dual-colours"|"reg-keys"|"signature-style"|"Others")
                # Already processed
                ;;
            *)
                echo_info "Found additional category: $category"
                move_category "$category"
                ;;
        esac
    fi
done

# Clean up empty directories in source
echo_info "Cleaning up empty source directories..."
find "$SOURCE_DIR" -type d -empty -delete 2>/dev/null || true

# Remove the nested /shared directory if it's empty
if [ -d "/media/shared/shared" ] && [ -z "$(ls -A /media/shared/shared)" ]; then
    rmdir "/media/shared/shared"
    echo_success "Removed empty nested /shared directory"
fi

# Count files after moving
FINAL_COUNT=$(find "$TARGET_DIR" -name "*.stl" -type f | wc -l)

echo ""
echo_success "🎉 STL file reorganization completed!"
echo ""
echo_info "📊 Summary:"
echo_info "   - Files moved: $TOTAL_FILES"
echo_info "   - Final count: $FINAL_COUNT"
echo_info "   - Source: $SOURCE_DIR"
echo_info "   - Target: $TARGET_DIR"
echo ""
echo_info "📁 New structure:"
ls -la "$TARGET_DIR" 2>/dev/null || echo "Could not list target directory"
echo ""
echo_info "💡 STL worker will now use the correct directory structure"
