# Y3DHub Azure Integration

**One script to rule them all!** 🎯

## Quick Start

```bash
# Complete setup (first time)
./y3dhub-azure-integration.sh setup

# Verify everything is working
./y3dhub-azure-integration.sh verify

# Copy STL files to Azure
./y3dhub-azure-integration.sh share-stl
```

## What This Script Does

✅ **Complete Azure File Share Setup**

- Mounts Azure File Share at `/media/shared`
- Sets proper permissions for `jayson` user
- Creates secure credentials
- Configures automatic mounting

✅ **Environment Configuration**

- Adds `AZURE_SHARED_DIR="/media/shared"` to `.env` files
- Configures STL worker settings
- Updates both staging and production environments

✅ **Verification & Utilities**

- Verifies setup is working correctly
- Provides backup and file sharing utilities
- Monitors storage usage

## All Available Commands

```bash
# Setup Commands
./y3dhub-azure-integration.sh setup      # Complete setup
./y3dhub-azure-integration.sh verify     # Verify setup
./y3dhub-azure-integration.sh configure  # Configure env vars only

# Utility Commands
./y3dhub-azure-integration.sh check      # Check mount status
./y3dhub-azure-integration.sh share-stl  # Copy STL files to Azure
./y3dhub-azure-integration.sh usage      # Show storage usage
./y3dhub-azure-integration.sh backup-db  # Backup database
./y3dhub-azure-integration.sh help       # Show all commands
```

## Configuration

- **Mount Point**: `/media/shared`
- **Azure Path**: `//y3dshare.file.core.windows.net/shared`
- **Credentials**: `/etc/smbcredentials/y3dshare.cred` (secure)
- **Environment**: `AZURE_SHARED_DIR="/media/shared"`

## Directory Structure

```
/media/shared/
├── stl-files/      # STL files copied here automatically
├── backups/        # Database and app backups
├── uploads/        # Manual file uploads
└── logs/           # Log archives
```

## How STL Worker Integration Works

1. STL worker generates files in `./output_stl/`
2. If `AZURE_SHARED_DIR` is set, files are automatically copied to Azure
3. Directory structure is maintained on Azure
4. Worker continues even if Azure copy fails

## Troubleshooting

```bash
# Check if mounted
./y3dhub-azure-integration.sh check

# Verify everything
./y3dhub-azure-integration.sh verify

# Re-run setup if needed
./y3dhub-azure-integration.sh setup
```

That's it! One script, all functionality. 🚀
