#!/bin/bash

# Y3DHub Azure File Share Complete Setup & Integration Script
# This script handles everything: setup, configuration, verification, and utilities

set -e

# Configuration
STORAGE_ACCOUNT="y3dshare"
FILE_SHARE="shared"
AZURE_MOUNT="/media/shared"
CREDENTIALS_FILE="/etc/smbcredentials/y3dshare.cred"
STORAGE_KEY="****************************************************************************************"

BACKUP_DIR="$AZURE_MOUNT/backups"
LOGS_DIR="$AZURE_MOUNT/logs"
UPLOADS_DIR="$AZURE_MOUNT/uploads"
STL_DIR="$AZURE_MOUNT/stl-files"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

# Setup Azure File Share
setup_azure() {
    echo_info "🔧 Setting up Azure File Share for STL Worker"
    echo_info "============================================="

    # Create mount directory
    echo_info "Creating mount directory..."
    sudo mkdir -p "$AZURE_MOUNT"
    echo_success "Created $AZURE_MOUNT"

    # Create credentials directory
    echo_info "Creating credentials directory..."
    sudo mkdir -p /etc/smbcredentials

    # Create credentials file
    echo_info "Creating credentials file..."
    sudo bash -c "cat > $CREDENTIALS_FILE << EOF
username=$STORAGE_ACCOUNT
password=$STORAGE_KEY
EOF"
    sudo chmod 600 "$CREDENTIALS_FILE"
    sudo chown root:root "$CREDENTIALS_FILE"
    echo_success "Credentials file created securely"

    # Unmount if already mounted
    if mountpoint -q "$AZURE_MOUNT" 2>/dev/null; then
        echo_info "Unmounting existing mount..."
        sudo umount "$AZURE_MOUNT"
    fi

    # Mount the file share with correct permissions
    echo_info "Mounting Azure File Share..."
    sudo mount -t cifs \
        "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE" \
        "$AZURE_MOUNT" \
        -o credentials="$CREDENTIALS_FILE",dir_mode=0755,file_mode=0755,uid=1000,gid=1000,serverino,nosharesock,mfsymlinks,actimeo=30

    if mountpoint -q "$AZURE_MOUNT"; then
        echo_success "Azure File Share mounted successfully!"
    else
        echo_error "Failed to mount Azure File Share"
        exit 1
    fi

    # Update fstab for persistent mounting
    echo_info "Adding to /etc/fstab for automatic mounting..."
    sudo sed -i "\|$AZURE_MOUNT|d" /etc/fstab
    echo "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE $AZURE_MOUNT cifs nofail,credentials=$CREDENTIALS_FILE,dir_mode=0755,file_mode=0755,uid=1000,gid=1000,serverino,nosharesock,mfsymlinks,actimeo=30" | sudo tee -a /etc/fstab > /dev/null
    echo_success "Added to /etc/fstab"

    # Create directory structure
    echo_info "Creating directory structure..."
    mkdir -p "$STL_DIR"/{bubble-style,dual-colours,reg-keys}
    mkdir -p "$BACKUP_DIR" "$LOGS_DIR" "$UPLOADS_DIR"
    echo_success "Directory structure created"

    # Configure environment variables
    configure_environment

    echo_success "🎉 Azure File Share setup completed!"
}

# Configure environment variables
configure_environment() {
    echo_info "Configuring environment variables..."

    for env_dir in "." "../Y3DHub_production" "../Y3DHub_main"; do
        if [ -f "$env_dir/.env" ]; then
            echo_info "Updating $env_dir/.env"

            # Add or update Azure configuration
            if grep -q "^AZURE_SHARED_DIR=" "$env_dir/.env"; then
                sed -i "s|^AZURE_SHARED_DIR=.*|AZURE_SHARED_DIR=\"$AZURE_MOUNT\"|" "$env_dir/.env"
            else
                echo "" >> "$env_dir/.env"
                echo "# Azure File Share Configuration" >> "$env_dir/.env"
                echo "AZURE_SHARED_DIR=\"$AZURE_MOUNT\"" >> "$env_dir/.env"
            fi

            # Add STL worker configs if not present
            for var in "STL_OUTPUT_DIR=\"./output_stl\"" "OPENSCAD_DIR=\"./openscad\"" "STL_WORKER_CONCURRENCY=\"2\""; do
                var_name=$(echo "$var" | cut -d'=' -f1)
                if ! grep -q "^$var_name=" "$env_dir/.env"; then
                    echo "$var" >> "$env_dir/.env"
                fi
            done

            echo_success "Updated $env_dir/.env"
        fi
    done
}

# Check if Azure mount is available
check_azure_mount() {
    if ! mountpoint -q "$AZURE_MOUNT"; then
        echo_error "Azure File Share not mounted at $AZURE_MOUNT"
        echo_info "Run: $0 setup"
        return 1
    fi
    echo_success "Azure File Share is mounted and accessible"
    return 0
}

# Backup MySQL database
backup_database() {
    local db_name="${1:-y3dhub_prod}"
    local backup_file="$BACKUP_DIR/mysql-${db_name}-$(date +%Y%m%d_%H%M%S).sql"
    
    echo_info "Backing up database: $db_name"
    
    if mysqldump -u root -pDmggg130319188*** "$db_name" > "$backup_file"; then
        echo_success "Database backup saved: $backup_file"
        
        # Compress the backup
        gzip "$backup_file"
        echo_success "Backup compressed: ${backup_file}.gz"
        
        # Keep only last 7 days of backups
        find "$BACKUP_DIR" -name "mysql-${db_name}-*.sql.gz" -mtime +7 -delete
        echo_info "Cleaned up old backups (>7 days)"
    else
        echo_error "Database backup failed"
        return 1
    fi
}

# Backup application files
backup_application() {
    local env="${1:-production}"
    local source_dir
    
    case $env in
        "production")
            source_dir="/home/<USER>/Y3DHub_production"
            ;;
        "staging")
            source_dir="/home/<USER>/Y3DHub_staging"
            ;;
        *)
            echo_error "Unknown environment: $env"
            return 1
            ;;
    esac
    
    local backup_file="$BACKUP_DIR/y3dhub-${env}-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    echo_info "Backing up $env application files from $source_dir"
    
    if tar -czf "$backup_file" -C "$(dirname "$source_dir")" "$(basename "$source_dir")" \
        --exclude="node_modules" \
        --exclude=".git" \
        --exclude=".next" \
        --exclude="logs" \
        --exclude="output_stl"; then
        
        echo_success "Application backup saved: $backup_file"
        
        # Keep only last 3 days of app backups (they're larger)
        find "$BACKUP_DIR" -name "y3dhub-${env}-*.tar.gz" -mtime +3 -delete
        echo_info "Cleaned up old application backups (>3 days)"
    else
        echo_error "Application backup failed"
        return 1
    fi
}

# Archive logs to Azure
archive_logs() {
    local log_archive="$LOGS_DIR/y3dhub-logs-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    echo_info "Archiving Y3DHub logs"
    
    if tar -czf "$log_archive" -C /var/log y3dhub/ 2>/dev/null; then
        echo_success "Logs archived: $log_archive"
        
        # Keep only last 30 days of log archives
        find "$LOGS_DIR" -name "y3dhub-logs-*.tar.gz" -mtime +30 -delete
        echo_info "Cleaned up old log archives (>30 days)"
    else
        echo_warning "No logs found to archive or archive failed"
    fi
}

# Verify setup
verify_setup() {
    echo_info "🔍 Verifying Azure File Share Setup"
    echo_info "==================================="

    local errors=0

    # Check mount
    if mountpoint -q "$AZURE_MOUNT"; then
        echo_success "Azure File Share mounted at $AZURE_MOUNT"

        # Check permissions
        if [ -w "$AZURE_MOUNT" ]; then
            echo_success "Write permissions OK"
        else
            echo_error "No write permissions"
            ((errors++))
        fi

        # Show disk usage
        df -h "$AZURE_MOUNT" 2>/dev/null || echo_warning "Could not get disk usage"
    else
        echo_error "Azure File Share not mounted"
        ((errors++))
    fi

    # Check credentials
    if [ -f "$CREDENTIALS_FILE" ]; then
        echo_success "Credentials file exists"
        local perms=$(stat -c "%a" "$CREDENTIALS_FILE")
        if [ "$perms" = "600" ]; then
            echo_success "Credentials permissions OK"
        else
            echo_warning "Credentials permissions: $perms (should be 600)"
        fi
    else
        echo_error "Credentials file missing"
        ((errors++))
    fi

    # Check environment
    if [ -f ".env" ] && grep -q "AZURE_SHARED_DIR" .env; then
        echo_success "Environment variables configured"
    else
        echo_error "Environment variables not configured"
        ((errors++))
    fi

    # Check directories
    for dir in "$STL_DIR" "$BACKUP_DIR" "$LOGS_DIR" "$UPLOADS_DIR"; do
        if [ -d "$dir" ]; then
            echo_success "Directory exists: $(basename "$dir")"
        else
            echo_warning "Directory missing: $(basename "$dir")"
        fi
    done

    # Test file operations
    local test_file="$AZURE_MOUNT/test-$(date +%s).txt"
    if echo "Test" > "$test_file" 2>/dev/null; then
        echo_success "File operations working"
        rm -f "$test_file"
    else
        echo_error "File operations failed"
        ((errors++))
    fi

    # Summary
    if [ $errors -eq 0 ]; then
        echo_success "🎉 All checks passed! Setup is working correctly."
    else
        echo_error "❌ $errors error(s) found. Run '$0 setup' to fix."
        exit 1
    fi
}

# Share STL files
share_stl_files() {
    local stl_source="./output_stl"
    local stl_dest="$STL_DIR"

    echo_info "Sharing STL files to Azure"

    if [ -d "$stl_source" ] && [ "$(ls -A "$stl_source")" ]; then
        # Copy maintaining directory structure
        rsync -av "$stl_source/" "$stl_dest/" --include="*.stl" --exclude="*" 2>/dev/null || {
            # Fallback to simple copy
            find "$stl_source" -name "*.stl" -exec cp {} "$stl_dest/" \; 2>/dev/null || true
        }
        echo_success "STL files copied to Azure"

        # Show count
        local count=$(find "$stl_dest" -name "*.stl" 2>/dev/null | wc -l)
        echo_info "Total STL files in Azure: $count"
    else
        echo_warning "No STL files found to share"
    fi
}

# Upload files to Azure
upload_file() {
    local source_file="$1"
    local dest_name="${2:-$(basename "$source_file")}"
    
    if [ ! -f "$source_file" ]; then
        echo_error "Source file not found: $source_file"
        return 1
    fi
    
    local dest_path="$UPLOADS_DIR/$dest_name"
    
    echo_info "Uploading file to Azure: $source_file -> $dest_path"
    
    if cp "$source_file" "$dest_path"; then
        echo_success "File uploaded successfully"
        echo_info "Azure path: $dest_path"
    else
        echo_error "Upload failed"
        return 1
    fi
}

# Show Azure storage usage
show_usage() {
    echo_info "Azure File Share Usage:"
    df -h "$AZURE_MOUNT"
    echo ""
    
    echo_info "Directory sizes:"
    du -sh "$BACKUP_DIR" "$LOGS_DIR" "$UPLOADS_DIR" "$SHARED_DIR" 2>/dev/null || true
}

# Full backup (database + application + logs)
full_backup() {
    local env="${1:-production}"
    
    echo_info "Starting full backup for $env environment"
    
    backup_database "y3dhub_prod"
    backup_application "$env"
    archive_logs
    
    echo_success "Full backup completed!"
    show_usage
}

# Show help
show_help() {
    echo "Y3DHub Azure File Share Complete Integration"
    echo "==========================================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Setup Commands:"
    echo "  setup                    - Complete Azure File Share setup"
    echo "  verify                   - Verify setup is working"
    echo "  configure                - Configure environment variables only"
    echo ""
    echo "Utility Commands:"
    echo "  check                    - Check Azure mount status"
    echo "  backup-db [db_name]      - Backup MySQL database (default: y3dhub_prod)"
    echo "  backup-app [env]         - Backup application files (production|staging)"
    echo "  archive-logs             - Archive application logs"
    echo "  share-stl                - Copy STL files to shared directory"
    echo "  upload [file] [name]     - Upload a file to Azure"
    echo "  usage                    - Show storage usage"
    echo "  full-backup [env]        - Full backup (db + app + logs)"
    echo "  help                     - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 setup                 # First time setup"
    echo "  $0 verify                # Check everything is working"
    echo "  $0 share-stl             # Copy STL files to Azure"
    echo "  $0 backup-db y3dhub_prod"
    echo "  $0 full-backup production"
}

# Main execution
main() {
    case "${1:-help}" in
        "setup")
            setup_azure
            ;;
        "verify")
            verify_setup
            ;;
        "configure")
            configure_environment
            echo_success "Environment configuration completed!"
            ;;
        "check")
            check_azure_mount
            ;;
        "backup-db")
            check_azure_mount
            backup_database "$2"
            ;;
        "backup-app")
            check_azure_mount
            backup_application "$2"
            ;;
        "archive-logs")
            check_azure_mount
            archive_logs
            ;;
        "share-stl")
            check_azure_mount
            share_stl_files
            ;;
        "upload")
            check_azure_mount
            upload_file "$2" "$3"
            ;;
        "usage")
            check_azure_mount
            show_usage
            ;;
        "full-backup")
            check_azure_mount
            full_backup "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
