#!/bin/bash

# MySQL Replication Reset Script
# This script will dump the master database and reset replication

set -e

MASTER_HOST="*************"
MASTER_USER="root"
MASTER_PASS="Dmggg13031988***"
LOCAL_USER="root"
LOCAL_PASS="Dmggg130319188***"

echo "🔄 Starting MySQL replication reset..."

# Step 1: Stop local slave
echo "📛 Stopping local slave replication..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "STOP SLAVE;"

# Step 2: Reset slave configuration
echo "🔄 Resetting slave configuration..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "RESET SLAVE ALL;"

# Step 3: Drop and recreate local databases
echo "🗑️ Dropping local databases..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "DROP DATABASE IF EXISTS y3dhub_prod;"
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "DROP DATABASE IF EXISTS y3dhub;"
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "DROP DATABASE IF EXISTS y3dhub_staging;"

# Step 4: Create backup directory
BACKUP_DIR="/tmp/mysql_dump_$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR
echo "📁 Created backup directory: $BACKUP_DIR"

# Step 5: Dump master database with master position
echo "📥 Dumping master database..."
mysqldump -h $MASTER_HOST -u $MASTER_USER -p$MASTER_PASS \
    --single-transaction \
    --routines \
    --triggers \
    --master-data=2 \
    --all-databases \
    --flush-logs \
    > $BACKUP_DIR/master_dump.sql

echo "✅ Master dump completed"

# Step 6: Import dump to local MySQL
echo "📤 Importing dump to local MySQL..."
mysql -u $LOCAL_USER -p$LOCAL_PASS < $BACKUP_DIR/master_dump.sql

echo "✅ Import completed"

# Step 7: Extract master position from dump
echo "🔍 Extracting master position..."
MASTER_INFO=$(grep -A 1 "CHANGE MASTER TO" $BACKUP_DIR/master_dump.sql | head -2)
MASTER_LOG_FILE=$(echo "$MASTER_INFO" | grep "MASTER_LOG_FILE" | sed "s/.*MASTER_LOG_FILE='\([^']*\)'.*/\1/")
MASTER_LOG_POS=$(echo "$MASTER_INFO" | grep "MASTER_LOG_POS" | sed "s/.*MASTER_LOG_POS=\([0-9]*\).*/\1/")

echo "📍 Master position: File=$MASTER_LOG_FILE, Position=$MASTER_LOG_POS"

# Validate extracted values
if [ -z "$MASTER_LOG_FILE" ] || [ -z "$MASTER_LOG_POS" ]; then
    echo "❌ Failed to extract master position. Checking dump file..."
    grep -A 5 -B 5 "CHANGE MASTER TO" $BACKUP_DIR/master_dump.sql
    exit 1
fi

# Step 8: Configure replication
echo "⚙️ Configuring replication..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "
CHANGE MASTER TO
    MASTER_HOST='$MASTER_HOST',
    MASTER_USER='$MASTER_USER',
    MASTER_PASSWORD='$MASTER_PASS',
    MASTER_LOG_FILE='$MASTER_LOG_FILE',
    MASTER_LOG_POS=$MASTER_LOG_POS;
"

# Step 9: Start replication
echo "▶️ Starting replication..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "START SLAVE;"

# Step 10: Check status
echo "📊 Checking replication status..."
mysql -u $LOCAL_USER -p$LOCAL_PASS -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error|Seconds_Behind_Master)"

echo ""
echo "🎉 MySQL replication reset completed!"
echo "📁 Backup saved in: $BACKUP_DIR"
echo ""
echo "To verify replication is working:"
echo "mysql -u root -p -e \"SHOW SLAVE STATUS\\G\""
