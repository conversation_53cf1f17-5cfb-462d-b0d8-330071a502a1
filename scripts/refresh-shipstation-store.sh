#!/usr/bin/env bash
set -euo pipefail

# Load environment variables if .env exists (optional)
if [ -f "$(dirname "$0")/../.env" ]; then
  export $(grep -v '^#' $(dirname "$0")/../.env | xargs)
fi

# Required environment variables - SHIPSTATION_STORE_ID will be static
: "${SHIPSTATION_API_KEY:?Need to set SHIPSTATION_API_KEY}"
: "${SHIPSTATION_API_SECRET:?Need to set SHIPSTATION_API_SECRET}"

# ShipStation Store ID (Static value for Amazon UK)
SHIPSTATION_STORE_ID_STATIC="112576"

# ShipStation API endpoint
API_URL="https://ssapi.shipstation.com/stores/refreshstore?storeId=${SHIPSTATION_STORE_ID_STATIC}"

# Optional: set refreshDate to now in PST (not required for normal operation)
# REFRESH_DATE=$(TZ=America/Los_Angeles date '+%Y-%m-%d %H:%M:%S')
# DATA="{\"storeId\": ${SHIPSTATION_STORE_ID_STATIC}, \"refreshDate\": \"$REFRESH_DATE\"}"
DATA="{\"storeId\": ${SHIPSTATION_STORE_ID_STATIC}}"

# Log file
LOG_FILE="/home/<USER>/Y3DHub_staging/logs/refresh-shipstation-store.log"
mkdir -p "$(dirname "$LOG_FILE")"

# Make the API call
RESPONSE=$(curl -s -u "$SHIPSTATION_API_KEY:$SHIPSTATION_API_SECRET" \
  -H "Content-Type: application/json" \
  -X POST "$API_URL" \
  -d "$DATA")

# Log with timestamp
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "$TIMESTAMP $RESPONSE" >> "$LOG_FILE"

# Optionally, print to stdout for debugging
echo "$RESPONSE" 