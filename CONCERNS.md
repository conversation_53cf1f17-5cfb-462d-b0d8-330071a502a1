# Populate Print Queue Script Analysis Report

## Executive Summary

The local (uncommitted) version of `populate-print-queue.ts` contains several significant enhancements compared to the remote version. **No functionality has been lost**, but there are critical new features that require careful testing. The main changes include:

1. **Bulk Order Personalization Handling** - New parser for bulk customer notes
2. **Live ShipStation Notes Fetching** - Real-time data fetching on force-recreate
3. **Enhanced Amazon URL Handling** - Now fetches from database table
4. **Improved AI Fallback Logic** - Better handling of empty/invalid AI responses

## Detailed Changes Analysis

### 1. **New Bulk Personalization Parser** (Lines 44-61)

**New Code Added:**

```typescript
type PersonalizationNote = { customText: string; color1: string; color2: string };

function parseBulkPersonalizationNotes(
  notes: string
): Array<PersonalizationNote> {
  // Parses notes with format:
  // Primary Blue & Secondary Red
  // John
  // Mary
  // etc.
}
```

**Purpose:** Handles bulk orders where customers provide a list of names with shared color groups
**Risk:** Low - This is a new feature that doesn't interfere with existing logic

### 2. **Enhanced Amazon URL Extraction** (Lines 346-439)

**Changes:**

- **Old:** Extracted Amazon URL from `item.print_settings`
- **New:** Fetches from `AmazonCustomizationFile` database table

```typescript
// Old approach
const amazonUrl = extractCustomizationUrl(item);

// New approach
const amazonCustomFile = await prisma.amazonCustomizationFile.findUnique({
  where: { orderItemId: item.id },
});
amazonUrl = amazonCustomFile?.originalUrl || null;
```

**Risk:** Medium - Depends on `AmazonCustomizationFile` table being properly populated

### 3. **Live ShipStation Notes Fetching** (Lines 556-592, 1715-1734)

**New Features:**

- Fetches live customer/internal notes from ShipStation when `--force-recreate` is used
- Prioritizes live notes over database-stored notes
- Passes live notes through to transaction processing

**Changes in `extractOrderPersonalization`:**

```typescript
// Added return values
return {
  // ... existing fields ...
  liveCustomerNotes?: string | null;
  liveInternalNotes?: string | null;
}
```

**Risk:** Low - Falls back gracefully if API call fails

### 4. **Enhanced AI Fallback Logic** (Lines 1017-1137)

**New Logic:**

```typescript
// Check if AI returned only empty/invalid personalization
if (
  itemPzResult.personalizations.length === 1 &&
  !itemPzResult.personalizations[0].customText &&
  !itemPzResult.personalizations[0].color1 &&
  !itemPzResult.personalizations[0].color2
) {
  useNotesParser = true; // Trigger fallback parser
}
```

**Fallback Priority Order:**

1. Live customer notes (if fetched)
2. Live internal notes (if fetched)
3. DB customer notes
4. DB internal notes
5. Legacy notes field
6. Customer profile notes

**Risk:** Medium - Logic is more complex but provides better fallback options

### 5. **Function Signature Changes**

**`extractDirectItemData`** - Added `prisma` parameter:

```typescript
async function extractDirectItemData(
  prisma: PrismaClient, // NEW PARAMETER
  order: OrderWithItemsAndProducts,
  item: OrderWithItemsAndProducts['items'][number],
  product: OrderWithItemsAndProducts['items'][number]['product']
);
```

**`createOrUpdateTasksInTransaction`** - Added live notes parameters:

```typescript
async function createOrUpdateTasksInTransaction(
  // ... existing params ...
  liveCustomerNotes: string | null | undefined, // NEW
  liveInternalNotes: string | null | undefined // NEW
);
```

### 6. **Import Changes**

Added import for `fetchShipStationOrderDetails`:

```typescript
import {
  fetchOrdersWithRetry,
  updateOrderItemsOptionsBatch,
  fetchShipStationOrderDetails, // NEW
} from '../lib/shipstation/api';
```

## Potential Issues & Recommendations

### 1. **Database Dependency**

- **Issue:** Amazon URL extraction now depends on `AmazonCustomizationFile` table
- **Recommendation:** Ensure migration/population scripts have run to populate this table
- **Fallback:** Old extraction method still exists but is commented out

### 2. **Complex Fallback Logic**

- **Issue:** Multiple fallback paths make debugging harder
- **Recommendation:** Add comprehensive logging at each fallback step
- **Testing:** Test with orders that have various combinations of notes

### 3. **API Rate Limiting**

- **Issue:** Live notes fetching adds extra ShipStation API calls
- **Recommendation:** Monitor API usage, especially during bulk operations
- **Mitigation:** Only fetches when `--force-recreate` is used

### 4. **Bulk Order Testing**

- **Issue:** New bulk parser needs thorough testing
- **Test Cases Needed:**
  - Multiple color groups
  - Various name formats
  - Edge cases (special characters, empty lines)

## Refactoring Recommendations

### 1. **Extract Constants**

```typescript
// Add to constants file
const FALLBACK_PRIORITY = {
  LIVE_CUSTOMER_NOTES: 1,
  LIVE_INTERNAL_NOTES: 2,
  DB_CUSTOMER_NOTES: 3,
  DB_INTERNAL_NOTES: 4,
  LEGACY_NOTES: 5,
  CUSTOMER_PROFILE_NOTES: 6,
};

const MAX_PLACEHOLDER_REASON_LENGTH = 1000;
const MAX_ANNOTATION_LENGTH = 1000;
```

### 2. **Split Complex Functions**

**`createOrUpdateTasksInTransaction`** is 400+ lines. Consider splitting:

```typescript
// Suggested structure
async function createOrUpdateTasksInTransaction(...) {
  // Main orchestration
  const preservedTexts = await fetchPreservedTexts(tx, order, options);

  for (const item of order.items) {
    const taskDetails = await processOrderItem(item, order, aiData, options);
    await createTasksForItem(tx, taskDetails, preservedTexts);
  }

  if (options.syncToShipstation) {
    await syncToShipStation(order, itemsToPatch, aiData, options);
  }
}
```

### 3. **Type Safety Improvements**

```typescript
// Create specific types for notes sources
type NotesSource =
  | 'liveCustomerNotes'
  | 'liveInternalNotes'
  | 'dbCustomerNotes'
  | 'dbInternalNotes'
  | 'legacyNotes'
  | 'customerProfileNotes'
  | 'none';

interface NotesSelection {
  notes: string;
  source: NotesSource;
  isLive: boolean;
}
```

### 4. **Error Handling Standardization**

```typescript
class PopulatePrintQueueError extends Error {
  constructor(
    message: string,
    public code: string,
    public orderId?: number,
    public itemId?: number
  ) {
    super(message);
    this.name = 'PopulatePrintQueueError';
  }
}

// Usage
throw new PopulatePrintQueueError(
  'Failed to fetch Amazon URL',
  'AMAZON_URL_FETCH_ERROR',
  order.id,
  item.id
);
```

### 5. **Logging Improvements**

```typescript
// Create structured logging helpers
const logOrderProcessing = {
  start: (order: Order) =>
    logger.info({
      orderId: order.id,
      orderNumber: order.shipstation_order_number,
      marketplace: order.marketplace,
      event: 'ORDER_PROCESSING_START',
    }),

  fallbackUsed: (orderId: number, source: NotesSource) =>
    logger.info({
      orderId,
      notesSource: source,
      event: 'FALLBACK_NOTES_USED',
    }),
};
```

## Testing Checklist

### Essential Tests Before Deployment:

1. **Basic Functionality**

   - [ ] Process order without force-recreate
   - [ ] Process order with force-recreate
   - [ ] Dry run mode works correctly

2. **Amazon Orders**

   - [ ] Amazon order with URL in database
   - [ ] Amazon order without URL (fallback to AI)
   - [ ] Amazon order with fetch error

3. **Bulk Orders**

   - [ ] Bulk order with valid format
   - [ ] Bulk order with multiple color groups
   - [ ] Bulk order with malformed notes

4. **Fallback Scenarios**

   - [ ] AI returns empty response
   - [ ] AI returns single invalid item
   - [ ] No AI response (skip-ai flag)

5. **Live Notes Fetching**

   - [ ] Successful fetch with force-recreate
   - [ ] Failed fetch (API error) with graceful fallback
   - [ ] Orders without ShipStation ID

6. **Edge Cases**
   - [ ] Order with mixed item types
   - [ ] Order with preserve-text flag
   - [ ] Concurrent processing safety

## Conclusion

The local version represents a significant enhancement over the remote version with **no loss of existing functionality**. The main concerns are:

1. **Database dependency** for Amazon URLs (ensure migration completed)
2. **Increased complexity** in fallback logic (needs thorough testing)
3. **Additional API calls** for live notes (monitor rate limits)

The code is production-ready with proper testing. The new features provide valuable flexibility for handling edge cases and bulk orders that the original version couldn't handle effectively.
