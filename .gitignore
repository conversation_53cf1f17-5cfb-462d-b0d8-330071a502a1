# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
jspm_packages/
bower_components/
vendor/

# testing
/coverage
.nyc_output/
# SQLite database files
*.db

# next.js
/.next/
/out/

# production
/build
dist/
.nuxt/
.vuepress/dist
.serverless/
.docz/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*.log # Ignore all log files

# env files (can opt-in for committing if needed)
.env
.env.*
!.env.example
.npmrc

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE and editor folders - Keep .vscode settings for team consistency
.vscode/settings.local.json
.vscode/launch.json
.idea/
*.sublime-*
.qodo/
*.swp
*.swo
.history/
.project
.classpath

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
/path/to/ven/
.pytest_cache/
.ruff_cache/
.mypy_cache/
/myenv/

# Logs and temporary files
/logs/
/tmp/
/temp/
*.tmp
*.temp
logs

# Build and cache
.turbo/
.husky/_/
storybook-static/
.swc/

# Browser automation
.playwright/
playwright-report/
test-results/

# Backup and old files
*.bak
*.old
*.orig
*.rej

# Example and test files
example.*

# Compiled source
*.com
*.class
*.dll
*.exe
*.o
*.so

# Package files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
.qodo
amazon-spapi-credentials.md

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/
/node_modules/.cache/
dev.log

# No longer using Netlify

# Ignore STL files
*.stl

# Aider
.aider.chat.history.md
.aider.input.history
.aider.tags.cache.v4/
.aider*

# Playwright
node_modules/
/blob-report/

#Misc
.venv
fix-quantity-mismatches.sh
mismatched-order-numbers.txt
gkey.json
google-cloud-sdk/
y3dhub-app-573-creds.json
openrouter-keys.json
vertex-ai-keys.json
vertex-ai-formatted-keys.json

# Sensitive service account keys
service-account.json

# IDE/Editor specific
.cursor/

# Temporary diff files
*.diff

# SSH and security files
.ssh/
keys.txt
project_ssh_keys/

# Added by Claude Task Master
# Logs
*.log
dev-debug.log

# Y3DHub debug artifacts
order-*.log
order_*_ai_debug.json
*-lint-warnings.txt
selected-files.txt

# ESLint cache
.eslintcache/
*.eslintcache

# Editor directories and files
.idea
# .vscode - Allow team settings, exclude personal ones
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Task files
tasks.json
tasks/
keys/*
.keys/
.roo/
*.wrapper.sh
scripts-dist/
.envrc

# Claude Code configuration - keep config files but ignore local settings
.claude/settings.local.json
.claude/*.private
