{"folders": [{"name": "Y3DHub_staging", "path": "."}], "settings": {"workbench.editorAssociations": {"*.md": "vscode.markdown.preview.editor"}, "github.copilot.chat.agent.thinkingTool": true, "github.copilot.chat.generateTests.codeLens": true, "github.copilot.chat.notebook.revealCellExecution.enabled": true, "github.copilot.chat.codesearch.enabled": true, "augment.nextEdit.enableGlobalBackgroundSuggestions": true, "github-actions.use-enterprise": false, "github-actions.workflows.pinned.refresh.enabled": true, "github.codespaces.devcontainerChangedNotificationStyle": "modal", "git.timeline.showUncommitted": true, "git.confirmSync": false, "typescript.preferences.includePackageJsonAutoImports": "auto", "files.associations": {"*.css": "tailwindcss", "*.tsx": "typescriptreact", "*.ts": "typescript"}, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "terminal.integrated.defaultProfile.linux": "bash", "problems.showCurrentInStatus": true, "breadcrumbs.enabled": true, "workbench.sideBar.location": "left", "workbench.activityBar.location": "default"}}