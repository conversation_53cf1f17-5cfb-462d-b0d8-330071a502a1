{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "strict": true, "outDir": "scripts-dist", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@lib/*": ["src/lib/*"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true}, "include": ["scripts/**/*.ts", "src/lib/**/*.ts", "src/types/**/*.ts"], "exclude": ["node_modules", "src/app", "src/components"]}