{
  "recommendations": [
    // Essential for this project
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "prisma.prisma",

    // GitHub integration
    "github.copilot",
    "github.copilot-chat",
    "github.vscode-pull-request-github",

    // Next.js/React development
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",

    // Git tools
    "eamodio.gitlens",
    "github.vscode-github-actions",

    // Database tools
    "mtxr.sqltools",
    "mtxr.sqltools-driver-mysql",

    // Development utilities
    "ms-vscode.vscode-dotenv",
    "yzhang.markdown-all-in-one",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-docker",

    // Additional productivity tools
    "usernamehw.errorlens",
    "pmneo.tsimporter",
    "rangav.vscode-thunder-client",

    // AI and productivity enhancements
    "gruntfuggly.todo-tree",
    "aaron-bond.better-comments",
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.vscode-ai"
  ],
  "unwantedRecommendations": ["ms-vscode.vscode-css", "hookyqr.beautify"]
}
