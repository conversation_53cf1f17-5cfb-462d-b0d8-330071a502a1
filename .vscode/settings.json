{
  // AI-powered IDE settings (Cursor/Windsurf/Claude Code)
  "cursor.chat.enabled": true,
  "cursor.chat.enableCodeActions": true,
  "cursor.chat.enableInlineChat": true,
  "cursor.composer.enabled": true,
  "cursor.cpp.disabledLanguages": [],
  "aipopup.enabled": false,
  "workbench.editorAssociations": {
    "*.md": "vscode.markdown.preview.editor"
  },

  // GitHub Copilot settings (fallback)
  "github.copilot.chat.agent.thinkingTool": true,
  "github.copilot.chat.completionContext.typescript.mode": "on",
  "github.copilot.chat.generateTests.codeLens": true,
  "github.copilot.chat.languageContext.typescript.enabled": true,
  "github.copilot.chat.notebook.revealCellExecution.enabled": true,
  "github.copilot.chat.codesearch.enabled": true,
  "augment.nextEdit.enableGlobalBackgroundSuggestions": true,

  // GitHub Actions
  "github-actions.use-enterprise": false,
  "github-actions.workflows.pinned.refresh.enabled": true,
  "github.codespaces.devcontainerChangedNotificationStyle": "modal",

  // Git settings
  "git.verboseCommit": true,
  "git.useCommitInputAsStashMessage": true,
  "git.timeline.showUncommitted": true,
  "git.autofetch": true,
  "git.confirmSync": false,

  // Editor settings for linting/formatting
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
    // "source.organizeImports": "explicit" // Disabled to prevent auto-removal of imports during development
  },
  "editor.rulers": [80, 120],
  "editor.tabSize": 2,
  "editor.insertSpaces": true,

  // TypeScript settings
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "auto",

  // ESLint settings
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,

  // File associations
  "files.associations": {
    "*.css": "tailwindcss",
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },
  "files.autoSave": "onFocusChange",

  // Performance optimizations
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.next": true,
    "**/coverage": true,
    "**/.git": true
  },
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/dist/**": true
  },

  // Tailwind CSS
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["clsx\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ],
  "tailwindCSS.validate": true,
  "tailwindCSS.lint.cssConflict": "warning",

  // Prisma
  "prisma.showPrismaDataPlatformNotification": false,

  // Terminal
  "terminal.integrated.defaultProfile.linux": "bash",

  // Language-specific formatting
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  // Error handling
  "problems.showCurrentInStatus": true,
  "editor.quickSuggestions": {
    "strings": true
  },

  // AI context optimization
  "editor.inlineSuggest.enabled": true,
  "editor.inlineSuggest.showToolbar": "onHover",
  "editor.suggest.preview": true,
  "editor.suggest.showMethods": true,
  "editor.suggest.showFunctions": true,
  "editor.suggest.showConstructors": true,
  "editor.suggest.showFields": true,
  "editor.suggest.showVariables": true,
  "editor.suggest.showClasses": true,
  "editor.suggest.showStructs": true,
  "editor.suggest.showInterfaces": true,
  "editor.suggest.showModules": true,
  "editor.suggest.showProperties": true,
  "editor.suggest.showEvents": true,
  "editor.suggest.showOperators": true,
  "editor.suggest.showUnits": true,
  "editor.suggest.showValues": true,
  "editor.suggest.showConstants": true,
  "editor.suggest.showEnums": true,
  "editor.suggest.showEnumMembers": true,
  "editor.suggest.showKeywords": true,
  "editor.suggest.showWords": true,
  "editor.suggest.showColors": true,
  "editor.suggest.showFiles": true,
  "editor.suggest.showReferences": true,
  "editor.suggest.showCustomcolors": true,
  "editor.suggest.showFolders": true,
  "editor.suggest.showTypeParameters": true,
  "editor.suggest.showSnippets": true,
  "editor.suggest.showUsers": true,
  "editor.suggest.showIssues": true,

  // Better code context for AI
  "breadcrumbs.enabled": true,
  "breadcrumbs.showFiles": true,
  "breadcrumbs.showSymbols": true,
  "breadcrumbs.showClasses": true,
  "breadcrumbs.showMethods": true,
  "breadcrumbs.showProperties": true,
  "breadcrumbs.showVariables": true,
  "breadcrumbs.showFunctions": true,
  "breadcrumbs.showModules": true,
  "breadcrumbs.showPackages": true,
  "breadcrumbs.showNamespaces": true,
  "breadcrumbs.showConstructors": true,
  "breadcrumbs.showEnums": true,
  "breadcrumbs.showInterfaces": true,
  "breadcrumbs.showStructs": true,
  "breadcrumbs.showEvents": true,
  "breadcrumbs.showConstants": true,
  "breadcrumbs.showTypeParameters": true,

  // Activity Bar and Title Bar settings
  "workbench.sideBar.location": "left",
  "workbench.activityBar.location": "default",
  "window.titleBarStyle": "custom"

  // Window settings
}
