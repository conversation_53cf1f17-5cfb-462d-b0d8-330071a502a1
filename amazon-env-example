# Amazon Selling Partner API (SP-API) Credentials
# Required for test-amazon-sp-api.js

# REQUIRED: LWA (Login with Amazon) Credentials
# These are found in the Seller Central Developer Portal under your application
SPAPI_LWA_APP_CLIENT_ID=amzn1.application-oa2-client.xxxxxxxxxxxxxxxxxxxxxxxx
SPAPI_LWA_APP_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SPAPI_LWA_REFRESH_TOKEN=Atzr|xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# OPTIONAL: AWS Credentials (no longer required since October 2023)
# These were previously required for AWS Signature Version 4 signing
SPAPI_AWS_ACCESS_KEY_ID=AKIAXXXXXXXXXXXXXXXX
SPAPI_AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# OPTIONAL: Seller ID (only needed for certain operations)
# Your seller ID in Amazon Seller Central
SPAPI_SELLER_ID=A2XXXXXXXXXXXXX

# OPTIONAL: Marketplace IDs (comma-separated list)
# UK: A1F83G8C2ARO7P
# US: ATVPDKIKX0DER
# DE: A1PA6795UKMFR9
# etc.
SPAPI_MARKETPLACE_IDS=A1F83G8C2ARO7P

# OPTIONAL: Region (default is 'eu' in the script)
# Use 'na' for North America, 'eu' for Europe, 'fe' for Far East
SPAPI_REGION=eu

# OPTIONAL: Custom Endpoint (only needed if using a proxy or custom endpoint)
# If not specified, the script will use the standard Amazon endpoint based on region
# SPAPI_ENDPOINT=https://sellingpartnerapi-eu.amazon.com

# Optional: Marketplace ID
# UK: A1F83G8C2ARO7P
# US: ATVPDKIKX0DER
# DE: A1PA6795UKMFR9
# etc.
SPAPI_MARKETPLACE_ID=A1F83G8C2ARO7P 
