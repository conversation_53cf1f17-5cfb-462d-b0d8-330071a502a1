{"itemPersonalizations": {"custom_listing_bulk_pack": {"personalizations": [{"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ails<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Alexandria", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Alyssia", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "amelie", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name not in Title Case. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Angus", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Antonio", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "aria", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name not in Title Case. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Aria", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "arlo", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name not in Title Case. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Austin", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ava", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ava", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Aven", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Breccan", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Brooklyn", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Carlo", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Cecilia", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Charlotte", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON> (DJ)", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "ella", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name not in Title Case. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "ELLIE", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name in all caps. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Emilia", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Eve", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Filomena", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Gianc<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>iselle", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Haystead", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Iliana", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Iliana", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Iris", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Isabella", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Isabella", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Isabella", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Isabella", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ivy", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ivy", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Juliet", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON> (Kaaz)", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "LEONEL", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Ambiguous input: Name in all caps. Please verify.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Leonidas", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Liv", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "London", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Luca", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Lu<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Luna", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Marina", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Marquesa", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Maya", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Maya", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Maya", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Melania", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Milo", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Min", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Natalie", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Nico", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Nico", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>a", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Petra", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Phoenix", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Ria", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "R<PERSON>n", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "River", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sam", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON>a", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sienna", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sienna", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sofia", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sofia", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Sophia", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Tapiwa", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Tara", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON> (Teddy)", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Victor", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Violet", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Vladimir", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}, {"customText": "Willow", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON><PERSON><PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "Zia-Anna", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "Red", "color2": "White", "quantity": 1, "needsReview": false, "reviewReason": null, "annotation": "Split: Custom listing processed from notes."}, {"customText": "<PERSON>", "color1": "White", "color2": "Red", "quantity": 1, "needsReview": true, "reviewReason": "Duplicate name instance. Please verify color and details.", "annotation": "Split: Custom listing processed from notes."}], "overallNeedsReview": true, "overallReviewReason": "Duplicate name instance. Please verify color and details.; Ambiguous input: Name not in Title Case. Please verify.; Ambiguous input: Name in all caps. Please verify."}}}