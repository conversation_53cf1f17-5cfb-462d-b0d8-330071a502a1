{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/next", "config": {"installCommand": "npm install --no-fund --no-audit --legacy-peer-deps", "buildCommand": "npm run build"}}], "env": {"NEXT_TELEMETRY_DISABLED": "1", "NEXTAUTH_SECRET": "${NEXTAUTH_SECRET}", "DATABASE_URL_VERSION": "direct-value", "SENDGRID_TO_EMAIL": "${SENDGRID_TO_EMAIL}", "SENDGRID_TEST_TO": "${SENDGRID_TEST_TO}", "SENDGRID_FROM_EMAIL": "${SENDGRID_FROM_EMAIL}", "SENDGRID_API_KEY": "${SENDGRID_API_KEY}", "NEW_ORDER_NOTIFICATION_EMAILS": "${NEW_ORDER_NOTIFICATION_EMAILS}", "LITELLM_DATABASE_URL": "${LITELLM_DATABASE_URL}", "LITELLM_SALT_KEY": "${LITELLM_SALT_KEY}", "UI_USERNAME": "${UI_USERNAME}", "UI_PASSWORD": "${UI_PASSWORD}", "LITELLM_MASTER_KEY": "${LITELLM_MASTER_KEY}", "DEFAULT_LLM_KEY": "${DEFAULT_LLM_KEY}"}, "github": {"silent": true, "autoJobCancelation": true, "productionBranch": "main", "enabled": true}}