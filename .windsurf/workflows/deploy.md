---
description: Guide for deploying changes to production using the GitHub Actions workflow.
---

# Deploy to Production (via GitHub Actions)

This workflow outlines the steps to get your local changes from `staging` into `main`, which will then trigger the automated GitHub Actions deployment to the production environment.

## Prerequisites

- Ensure all your changes are committed to your local `staging` branch.
- Ensure your local `staging` branch is up-to-date with any remote changes if applicable (though typically `staging` is your primary development branch before merging to `main`).

## Step 1: Push Staging Changes

0.  **Switch to `staging` branch (if not already there):**
// turbo
    ```bash
    git checkout staging
    ```
1.  **Ensure local `staging` is ready:**
    ```bash
    git status # Verify no uncommitted changes
    git pull origin staging # If others might push to staging
    ```
2.  **Push `staging` to remote:**
    // turbo
    `bash
git push origin staging
`

## Step 2: Merge Staging into Main

1.  **Checkout `main`:**
    // turbo
    `bash
git checkout main
`
2.  **Ensure `main` is up-to-date:**
    // turbo
    `bash
git pull origin main
`
3.  **Merge `staging` into `main`:**

    ```bash
    git merge staging
    ```

    _(You may need to resolve conflicts and/or confirm the merge commit message in your editor if it opens.)_

4.  **Push `main` to remote:**
    // turbo
    `bash
git push origin main
`

## Step 3: Automated Deployment via GitHub Actions

Once `main` is pushed to `origin`, the `deploy-production.yml` GitHub Action will automatically:

- Connect to the production server.
- Pull the latest changes from `main`.
- Install dependencies.
- Run database migrations.
- Build the application.
- Restart the application using PM2.
- Perform a health check.

You can monitor the progress of this action in your repository's "Actions" tab on GitHub.

## Step 4: Verify Production Deployment (Optional Manual Checks)

While the GitHub Action includes a health check, you can also manually verify:

1.  **Check application logs on the server:**
    ```bash
    # On the production server:
    pm2 logs Y3DHub_Production --lines 100
    ```
2.  **Visit your production application URL.**

---

This workflow relies on the `deploy-production.yml` GitHub Action being correctly configured and active.
