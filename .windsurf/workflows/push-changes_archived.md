---
description: Single developer release flow from staging to production.
---

# push-changes

Coordinate a solo-developer release: test and push to **staging**, merge to **main** when ready, then deploy from `Y3DHub_prod` with build + PM2 restart.

## Step 1 – Staging workflow

// turbo

1. terminal: cd /home/<USER>/Y3DHub_staging && npm run build # catch TypeScript errors early
   // turbo
2. terminal: git add . # stage all changes
3. terminal: git commit -m "feat: <message>" # descriptive commit
   // turbo
4. terminal: git push origin staging # remote update

## Step 2 – Promote to main

// turbo

1. terminal: cd /home/<USER>/Y3DHub_staging && git checkout main
   // turbo
2. terminal: git pull origin main # ensure main is up-to-date
   // turbo
3. terminal: git merge staging # merge staging into main
   // turbo
4. terminal: git push origin main # push to remote main

## Step 3 – Deploy production

// turbo

1. terminal: cd /home/<USER>/Y3DHub_prod && git stash push -m "pre-deploy stash" # save any local changes
   // turbo
2. terminal: git pull origin main # get latest changes
   // turbo
3. terminal: npm run build # will catch any TypeScript errors
   // turbo
4. terminal: pm2 list # verify the correct PM2 process name
5. terminal: pm2 reload Y3DHub_Prod || pm2 start npm --name "Y3DHub_Prod" -- start # restart existing or start if not running

## Step 4 – Verify deployment

// turbo

1. terminal: curl -I http://localhost:3000 # verify server is responding
2. terminal: pm2 logs Y3DHub_Prod --lines 10 # check for any startup errors

## Step 5 – Sync any fixes back to staging (if needed)

// turbo

1. terminal: cd /home/<USER>/Y3DHub_staging && git checkout staging
2. terminal: git status # check for any uncommitted changes
3. terminal: git add [fixed files] # if fixes were made in production
4. terminal: git commit -m "fix: sync TypeScript fixes from production" # if needed
   // turbo
5. terminal: git push origin staging # keep staging in sync

## Step 6 – Retrieve stashed changes (if needed)

1. terminal: cd /home/<USER>/Y3DHub_prod && git stash list # check stashed changes
2. terminal: git stash show -p # review stashed changes
3. terminal: git stash pop # apply and remove stash
   or
   terminal: git stash apply # apply but keep stash

## Step 7 – Quick rollback (emergency)

1. terminal: cd /home/<USER>/Y3DHub_prod && git checkout <previous_tag_or_sha>
   // turbo
2. terminal: npm run build
   // turbo
3. terminal: pm2 reload Y3DHub_Prod || pm2 start npm --name "Y3DHub_Prod" -- start
   // turbo
4. terminal: curl -I http://localhost:3000 # verify server is responding after rollback
