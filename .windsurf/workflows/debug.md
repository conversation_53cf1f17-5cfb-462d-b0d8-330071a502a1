---
description: Debug with Aider
---

# debug

Deep-debug with **Aider CLI**.  
Supports **analyse** (suggestions) or **fix** (auto-apply) modes and lets you pick any top-scoring model from Aid<PERSON>’s leaderboard or supply your own.

## description

Runs Aid<PERSON> on a critical error, optionally restricted to specified files.  
Ensure the right API keys are in your shell (see bottom note).

## inputs

| name                               | default | type / enum                                                                                                                    | description                                                                                             |
| ---------------------------------- | ------- | ------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------- |
| **ERROR_MESSAGE**                  | –       | text                                                                                                                           | Full diagnostic text.                                                                                   |
| **FILES_TO_DEBUG**                 | –       | text                                                                                                                           | Comma-sep file list. Blank ⇒ all git-tracked sources.                                                   |
| **USER_PROVIDED_CONTEXT_AND_GOAL** | –       | text                                                                                                                           | Optional: Detailed context about the error, intended functionality, and specific goals for Aider's fix. |
| **MODE**                           | `fix`   | `analyse` &#124; `fix`                                                                                                         | Analyse = dry-run, Fix = `--yes`.                                                                       |
| **MODEL_CHOICE**                   | `o3`    | `o3`, `o3-architect`, `o4-mini`, `gpt-4.1`, `gpt-4o`, `gpt-4-turbo`, `claude-3-opus`, `gemini-2.5-pro`, `qwen3-235b`, `custom` | Select LLM.                                                                                             |
| **CUSTOM_MODEL_ID**                | –       | text                                                                                                                           | Exact ID if `MODEL_CHOICE = custom`.                                                                    |

## steps

// turbo

1. terminal: |

   ```bash
   echo "🔧  Preparing Aider…"

   # Choose files
   if [[ -n "$FILES_TO_DEBUG" ]]; then
     TARGET=$(echo "$FILES_TO_DEBUG" | tr ',' ' ')
     echo "📂  Using user-specified files."
   else
     TARGET=$(git ls-files '*.ts' '*.tsx' '*.js' '*.jsx' '*.json' '*.sh' '*.md')
     echo "📂  Using all git-tracked source files."
   fi

   # Map leaderboard & custom choices
   ARCH_FLAG=""
   case "$MODEL_CHOICE" in
     o3)                MODEL_ID="o3" ;;
     o3-architect)      MODEL_ID="o3"         ; ARCH_FLAG="--architect" ;;   # chains GPT-4.1 diff :contentReference[oaicite:3]{index=3}
     o4-mini)           MODEL_ID="o4-mini" ;;                                 # OpenAI o4-mini ≠ gpt-4o :contentReference[oaicite:4]{index=4}
     gpt-4.1)           MODEL_ID="gpt-4.1" ;;                                 # pure 4.1 API :contentReference[oaicite:5]{index=5}
     gpt-4o)            MODEL_ID="gpt-4o" ;;
     gpt-4-turbo)       MODEL_ID="gpt-4-turbo" ;;
     claude-3-opus)     MODEL_ID="anthropic/claude-3-opus-20240229" ;;
     gemini-2.5-pro)    MODEL_ID="gemini/gemini-2.5-pro-latest" ;;
     qwen3-235b)        MODEL_ID="qwen3-235b" ;;                              # needs DashScope or OpenRouter key :contentReference[oaicite:6]{index=6}
     custom)            MODEL_ID="${CUSTOM_MODEL_ID:-o3}" ;;
     *)                 echo "⚠️ Unknown MODEL_CHOICE – defaulting to o3"; MODEL_ID="o3" ;;
   esac
   echo "🤖  Model: $MODEL_ID $ARCH_FLAG"

   # Determine Aider's execution mode (auto-apply or not) and construct the message
   if [[ "$MODE" == "fix" ]]; then
     AUTO="--yes"
     MSG="🔧 Fix the following TypeScript error:\n${ERROR_MESSAGE}"
   else
     AUTO=""
     MSG="🔍 Analyse the following TypeScript error:\n${ERROR_MESSAGE}"
   fi

   # Append user-provided context and goal if available
   if [[ -n "$USER_PROVIDED_CONTEXT_AND_GOAL" ]]; then
     MSG+="\n\nAdditional Context & Goal:\n${USER_PROVIDED_CONTEXT_AND_GOAL}"
   else
     # Append a default instruction if no specific context/goal is provided
     if [[ "$MODE" == "fix" ]]; then
       MSG+="\n\nPlease identify the root cause and apply the necessary code changes to resolve this error."
     else
       MSG+="\n\nPlease identify the root cause and propose the necessary code changes to resolve this error."
     fi
   fi
   ```

// turbo
2. terminal: |
```bash
echo "🚀 Running Aider…"

     # pass keys (masking in logs left to Cascade)
     OPENAI_API_KEY="$OPENAI_API_KEY" \
     GEMINI_API_KEY="$GEMINI_API_KEY" \
     QWEN_API_KEY="$QWEN_API_KEY" \
     OPENROUTER_API_KEY="$OPENROUTER_API_KEY" \
     aider --model "$MODEL_ID" $ARCH_FLAG $AUTO \
       --message "$MSG" \
       $TARGET
     ```

## api-key note

Set keys _before_ calling the workflow:

- **OpenAI (o3 / o4-mini / GPT-4-family)** → `OPENAI_API_KEY`
- **Anthropic Claude** → `ANTHROPIC_API_KEY`
- **Google Gemini** → `GEMINI_API_KEY` (plus `aider-install`)
- **Qwen via DashScope** →
  ```bash
  export OPENAI_API_BASE=https://dashscope-intl.aliyuncs.com/compatible-mode/v1
  export OPENAI_API_KEY=<your-dashscope-key>
  ```
