/**
 * Direct print utility for packaging slips
 * Place this file in the public folder and include it with a script tag
 * 
 * Usage in HTML:
 * <script src="/print-utility.js"></script>
 * 
 * Then call the function:
 * <button onclick="printPackagingSlip()">Print</button>
 */

function printPackagingSlip() {
  // Get content from hidden template
  const template = document.getElementById('packaging-slip-template');
  
  if (!template) {
    console.error('Template element not found');
    return;
  }
  
  // Get the HTML markup and marketplace class
  const content = template.innerHTML || template.outerHTML || '';
  const marketplaceClass = template.getAttribute('data-marketplace-class') || '';
  
  // Create print window
  const printWin = window.open('', '_blank');
  
  if (!printWin) {
    alert('Please allow pop-ups for printing');
    return;
  }
  
  // Write full document with styling
  const cssLink = '<link rel="stylesheet" href="/packaging-slip.css" />';
  printWin.document.open();
  printWin.document.write(
    `<!doctype html><html><head>${cssLink}</head><body class="packaging-slip-body ${marketplaceClass}">${content}</body></html>`
  );

  // Clone global styles as fallback
  document.querySelectorAll('link[rel="stylesheet"], style[data-n-href]').forEach(el => {
    printWin.document.head.appendChild(el.cloneNode(true));
  });
  printWin.document.close();
  
  // Wait for content to load before printing
  setTimeout(() => {
    printWin.focus();
    printWin.print();
    
    // Close the window after a delay
    setTimeout(() => {
      printWin.close();
    }, 1000);
  }, 500);
} 
