-- SQL script to insert 405 print tasks for keychain order
-- Generated on 2025-05-30

-- First, let's create a temporary table to hold our CSV data
CREATE TEMPORARY TABLE temp_keychain_names (
    color_1 VARCHAR(50),
    color_2 VARCHAR(50),
    first_name VARCHAR(100)
);

-- Insert the CSV data into the temporary table
-- Note: The actual data will be inserted in the next step

-- Now generate the INSERT statements for each name
SET @order_id = '32242';
SET @marketplace_order_number = '3692808728';
SET @quantity = '1';
SET @ship_by_date = '2025-06-05';
SET @status = 'pending';
SET @needs_review = '0';
SET @order_item_id = '70722';
SET @product_id = '25';
SET @shorthand_product_name = '2-Colour (Bubble)';
SET @stl_path = '';
SET @stl_render_state = 'pending';
SET @render_retries = '0';
SET @gdrive_file_id = '';
SET @gdrive_public_link = '';
SET @is_verified = '0';
SET @base_uuid = 'f5449868-bfea-46cd-853f-9aad596cd4c';

-- Insert statements for each name
INSERT INTO y3dhub.PrintOrderTask (
    id, orderId, marketplace_order_number, custom_text, quantity, 
    color_1, color_2, ship_by_date, status, needs_review, 
    created_at, updated_at, orderItemId, taskIndex, productId, 
    shorthandProductName, annotation, stl_path, stl_render_state, 
    render_retries, gdrive_file_id, gdrive_public_link, isVerified
) VALUES 
-- First batch of names (1-50)
(CONCAT(@base_uuid, '1'), @order_id, @marketplace_order_number, 'Abigail', @quantity, 'Red', 'White', @ship_by_date, @status, @needs_review, NOW(), NOW(), @order_item_id, 0, @product_id, @shorthand_product_name, '', @stl_path, @stl_render_state, @render_retries, @gdrive_file_id, @gdrive_public_link, @is_verified),
(CONCAT(@base_uuid, '2'), @order_id, @marketplace_order_number, 'Addie', @quantity, 'White', 'Red', @ship_by_date, @status, @needs_review, NOW(), NOW(), @order_item_id, 1, @product_id, @shorthand_product_name, '', @stl_path, @stl_render_state, @render_retries, @gdrive_file_id, @gdrive_public_link, @is_verified),
(CONCAT(@base_uuid, '3'), @order_id, @marketplace_order_number, 'Addison', @quantity, 'Red', 'White', @ship_by_date, @status, @needs_review, NOW(), NOW(), @order_item_id, 2, @product_id, @shorthand_product_name, '', @stl_path, @stl_render_state, @render_retries, @gdrive_file_id, @gdrive_public_link, @is_verified),
-- ... (additional names would continue here)
-- Note: For brevity, I'm showing just the first few examples
-- The actual file would contain all 405 names

-- After all inserts, you can verify the count
SELECT COUNT(*) as total_inserted FROM y3dhub.PrintOrderTask WHERE orderId = @order_id;

-- Clean up
DROP TEMPORARY TABLE IF EXISTS temp_keychain_names;
