import urllib.request
import ssl

try:
    # Try to bypass SSL verification for testing, not for production
    # For a more robust test, you'd ensure your system's certs are correct
    # ctx = ssl.create_default_context()
    # ctx.check_hostname = False
    # ctx.verify_mode = ssl.CERT_NONE
    # response = urllib.request.urlopen('https://accounts.google.com', context=ctx, timeout=10)
    
    # Simpler request, relies on system certs
    response = urllib.request.urlopen('https://accounts.google.com', timeout=10)
    print(f"Successfully connected to {response.geturl()}, status: {response.getcode()}")
except Exception as e:
    print(f"Failed to connect: {e}")
