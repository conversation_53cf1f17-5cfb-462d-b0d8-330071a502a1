generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client-sqlite"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "linux-musl"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Product {
  id                     Int              @id @default(autoincrement())
  sku                    String?          @unique
  name                   String
  imageUrl               String?
  weight                 Decimal?
  notes                  String?
  createdAt              DateTime         @default(now())
  updatedAt              DateTime         @updatedAt
  fulfillment_sku        String?
  item_weight_units      String?
  item_weight_value      Decimal?
  shipstation_product_id Int?             @unique
  upc                    String?
  warehouse_location     String?
  orderItems             OrderItem[]
  printTasks             PrintOrderTask[]

  @@index([sku])
  @@index([name])
  @@index([shipstation_product_id])
}

model Order {
  id                         Int                 @id @default(autoincrement())
  shipstation_order_id       String?             @unique
  shipstation_order_number   String?
  customerId                 Int?
  customer_name              String?
  order_status               String              @default("awaiting_shipment")
  order_key                  String?
  order_date                 DateTime?
  payment_date               DateTime?
  ship_by_date               DateTime?
  shipping_price             Decimal?
  tax_amount                 Decimal?
  discount_amount            Decimal?
  shipping_amount_paid       Decimal?
  shipping_tax               Decimal?
  total_price                Decimal             @default(0)
  gift                       Boolean             @default(false)
  gift_message               String?
  gift_email                 String?
  requested_shipping_service String?
  carrier_code               String?
  service_code               String?
  package_code               String?
  confirmation               String?
  tracking_number            String?
  shipped_date               DateTime?
  warehouse_id               String?
  customer_notes             String?
  internal_notes             String?
  last_sync_date             DateTime?
  notes                      String?
  created_at                 DateTime            @default(now())
  updated_at                 DateTime?           @updatedAt
  marketplace                String?
  amount_paid                Decimal?
  order_weight_units         String?
  order_weight_value         Decimal?
  payment_method             String?
  shipstation_store_id       Int?
  tag_ids                    Json?
  dimensions_height          Decimal?
  dimensions_length          Decimal?
  dimensions_units           String?
  dimensions_width           Decimal?
  insurance_insure_shipment  Boolean?
  insurance_insured_value    Decimal?
  insurance_provider         String?
  internal_status            InternalOrderStatus @default(new)
  is_voided                  Boolean?            @default(false)
  marketplace_notified       Boolean             @default(false)
  void_date                  DateTime?
  lastPackingSlipAt          DateTime?
  is_merged                  Boolean             @default(false)
  merged_to_order_id         Int?
  merged_from_order_ids      Json?
  customer                   Customer?           @relation(fields: [customerId], references: [id])
  items                      OrderItem[]
  printTasks                 PrintOrderTask[]

  @@index([created_at])
  @@index([updated_at])
  @@index([order_status])
  @@index([customerId])
  @@index([marketplace])
  @@index([shipstation_store_id])
  @@index([is_merged])
  @@index([merged_to_order_id])
}

model Customer {
  id                      Int              @id @default(autoincrement())
  name                    String
  email                   String?          @unique
  phone                   String?
  address                 String?
  shipstation_customer_id String?          @unique
  company                 String?
  street1                 String?
  street2                 String?
  street3                 String?
  city                    String?
  state                   String?
  postal_code             String?
  country                 String?
  country_code            String?
  customer_notes          String?
  created_at              DateTime         @default(now())
  updated_at              DateTime?        @updatedAt
  address_verified_status String?
  is_residential          Boolean?
  orders                  Order[]
  printTasks              PrintOrderTask[]

  @@index([email])
  @@index([shipstation_customer_id])
}

model OrderItem {
  id                       Int                      @id @default(autoincrement())
  orderId                  Int
  quantity                 Int                      @default(1)
  unit_price               Decimal
  print_settings           Json?
  created_at               DateTime                 @default(now())
  updated_at               DateTime?                @updatedAt
  shipstationLineItemKey   String?                  @unique
  productId                Int
  amazonCustomizationFiles AmazonCustomizationFile?
  order                    Order                    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product                  Product                  @relation(fields: [productId], references: [id])
  printTasks               PrintOrderTask[]

  @@index([orderId])
  @@index([productId])
}

model PrintOrderTask {
  id                       String          @id @default(cuid())
  orderId                  Int
  marketplace_order_number String?
  customerId               Int?
  custom_text              String?
  quantity                 Int             @default(1)
  color_1                  String?
  color_2                  String?
  ship_by_date             DateTime?
  status                   PrintTaskStatus @default(pending)
  needs_review             Boolean         @default(false)
  review_reason            String?
  created_at               DateTime        @default(now())
  updated_at               DateTime?       @updatedAt
  orderItemId              Int
  taskIndex                Int
  productId                Int
  shorthandProductName     String?
  annotation               String?
  stl_path                 String?
  stl_render_state         StlRenderStatus @default(pending)
  render_retries           Int             @default(0)
  gdrive_file_id           String?         // Added Google Drive File ID
  gdrive_public_link       String?         // Added Google Drive public link
  isVerified               Boolean         @default(false)
  verifiedAt               DateTime?
  verifiedByUserId         Int?
  customer                 Customer?       @relation(fields: [customerId], references: [id])
  order                    Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderItem                OrderItem       @relation(fields: [orderItemId], references: [id], onDelete: Cascade)
  product                  Product         @relation(fields: [productId], references: [id])
  verifiedByUser           User?           @relation("VerifiedTasks", fields: [verifiedByUserId], references: [id], onDelete: Restrict, onUpdate: Cascade)

  @@unique([orderItemId, taskIndex])
  @@index([orderId])
  @@index([orderItemId])
  @@index([productId])
  @@index([status])
  @@index([ship_by_date])
  @@index([customerId])
  @@index([needs_review])
  @@index([status, updated_at])
  @@index([stl_render_state])
}

model AmazonCustomizationFile {
  id               Int       @id @default(autoincrement())
  orderItemId      Int       @unique
  originalUrl      String
  localFilePath    String?
  downloadStatus   String    @default("pending")
  processingStatus String    @default("pending")
  customText       String?
  color1           String?
  color2           String?
  rawJsonData      Json?
  errorMessage     String?
  retryCount       Int       @default(0)
  lastProcessedAt  DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  orderItem        OrderItem @relation(fields: [orderItemId], references: [id], onDelete: Cascade)

  @@index([orderItemId])
  @@index([downloadStatus])
  @@index([processingStatus])
}

model system_logs {
  id        Int      @id @default(autoincrement())
  message   String?
  level     String?
  timestamp DateTime @default(now())

  @@map("system_logs")
}

model Tag {
  id                 Int      @id @default(autoincrement())
  shipstation_tag_id Int      @unique
  name               String
  color_hex          String?
  last_synced        DateTime @default(now())

  @@index([name])
}

model ScriptRunLog {
  id           Int       @id @default(autoincrement())
  scriptName   String
  runStartedAt DateTime  @default(now())
  runEndedAt   DateTime?
  status       String
  errorMessage String?
  errorStack   String?
  details      Json?
  createdAt    DateTime  @default(now())

  @@index([scriptName])
  @@index([status])
  @@index([runStartedAt])
}

model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  password      String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  emailVerified DateTime?
  image         String?
  name          String?
  accounts      Account[]
  sessions      Session[]
  verifiedPrintTasks PrintOrderTask[] @relation("VerifiedTasks")
}

model Account {
  id                Int     @id @default(autoincrement())
  userId            Int
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           Int      @id @default(autoincrement())
  sessionToken String   @unique
  userId       Int
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model SyncProgress {
  id                     String    @id @default(uuid())
  syncType               String
  startTime              DateTime
  endTime                DateTime?
  status                 String
  totalOrders            Int       @default(0)
  processedOrders        Int       @default(0)
  failedOrders           Int       @default(0)
  lastProcessedOrderId   String?
  lastProcessedTimestamp DateTime?
  error                  String?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  @@index([syncType])
  @@index([status])
  @@index([startTime])
}

model SyncMetrics {
  id                        String    @id @default(uuid())
  syncId                    String
  totalApiCalls             Int       @default(0)
  totalOrdersProcessed      Int       @default(0)
  totalOrdersFailed         Int       @default(0)
  totalItemsProcessed       Int       @default(0)
  totalItemsFailed          Int       @default(0)
  totalCustomersUpserted    Int       @default(0)
  totalProductsUpserted     Int       @default(0)
  avgProcessingTimePerOrder Int       @default(0)
  maxProcessingTimePerOrder Int       @default(0)
  minProcessingTimePerOrder Int       @default(0)
  totalProcessingTime       Int       @default(0)
  startTime                 DateTime
  endTime                   DateTime?
  createdAt                 DateTime  @default(now())
}

model AiCallLog {
  id               String   @id @default(uuid())
  scriptName       String
  orderId          Int
  orderNumber      String?
  marketplace      String?
  aiProvider       String
  modelUsed        String
  promptSent       String
  rawResponse      String
  processingTimeMs Int
  success          Boolean
  errorMessage     String?
  tasksGenerated   Int      @default(0)
  needsReviewCount Int      @default(0)
  createdAt        DateTime @default(now())

  @@index([orderId])
  @@index([scriptName])
  @@index([success])
  @@index([createdAt])
  @@index([aiProvider])
  @@index([modelUsed])
}

model Metric {
  id        Int      @id @default(autoincrement())
  name      String
  value     Float
  tags      Json?
  timestamp DateTime @default(now())

  @@index([name])
  @@index([timestamp])
}

/// AI Reports System
model AiReportDefinition {
  id           String        @id @default(uuid())
  slug         String        @unique
  name         String
  description  String
  systemPrompt String
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  runs         AiReportRun[]
}

model AiReportRun {
  id          String             @id @default(uuid())
  reportId    String
  inputJson   Json
  outputJson  Json?
  rawResponse String?
  status      String             @default("running")
  errorMsg    String?
  createdAt   DateTime           @default(now())
  finishedAt  DateTime?
  report      AiReportDefinition @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@index([reportId])
}

enum InternalOrderStatus {
  new
  processing
  printing
  completed
  cancelled
}

enum PrintTaskStatus {
  pending
  in_progress
  completed
  cancelled
}

enum StlRenderStatus {
  pending
  running
  completed
  failed
}
