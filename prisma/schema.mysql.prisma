generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/.prisma/client"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "linux-musl"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                Int     @id @default(autoincrement())
  userId            Int
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  User              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model AiCallLog {
  id               String   @id
  scriptName       String   @db.VarChar(100)
  orderId          Int
  orderNumber      String?  @db.VarChar(100)
  marketplace      String?  @db.VarChar(50)
  aiProvider       String   @db.VarChar(50)
  modelUsed        String   @db.VarChar(100)
  promptSent       String   @db.LongText
  rawResponse      String   @db.LongText
  processingTimeMs Int
  success          Boolean
  errorMessage     String?  @db.Text
  tasksGenerated   Int      @default(0)
  needsReviewCount Int      @default(0)
  createdAt        DateTime @default(now())

  @@index([aiProvider])
  @@index([createdAt])
  @@index([modelUsed])
  @@index([orderId])
  @@index([scriptName])
  @@index([success])
}

model AiReportDefinition {
  id           String        @id
  slug         String        @unique
  name         String
  description  String
  systemPrompt String        @db.LongText
  createdAt    DateTime      @default(now())
  updatedAt    DateTime
  AiReportRun  AiReportRun[]
}

model AiReportRun {
  id                 String             @id
  reportId           String
  inputJson          Json
  outputJson         Json?
  rawResponse        String?            @db.LongText
  status             String             @default("running")
  errorMsg           String?            @db.Text
  createdAt          DateTime           @default(now())
  finishedAt         DateTime?
  AiReportDefinition AiReportDefinition @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@index([reportId])
}

model AmazonCustomizationFile {
  id               Int       @id @default(autoincrement())
  orderItemId      Int       @unique
  originalUrl      String    @db.VarChar(1024)
  localFilePath    String?   @db.VarChar(512)
  downloadStatus   String    @default("pending") @db.VarChar(50)
  processingStatus String    @default("pending") @db.VarChar(50)
  customText       String?   @db.Text
  color1           String?   @db.VarChar(50)
  color2           String?   @db.VarChar(50)
  rawJsonData      Json?
  errorMessage     String?   @db.Text
  retryCount       Int       @default(0)
  lastProcessedAt  DateTime? @db.Timestamp(0)
  createdAt        DateTime  @default(now()) @db.Timestamp(0)
  updatedAt        DateTime  @db.Timestamp(0)
  OrderItem        OrderItem @relation(fields: [orderItemId], references: [id], onDelete: Cascade)

  @@index([downloadStatus])
  @@index([orderItemId])
  @@index([processingStatus])
}

model Customer {
  id                      Int              @id @default(autoincrement())
  name                    String           @db.VarChar(100)
  email                   String?          @unique @db.VarChar(100)
  phone                   String?          @db.VarChar(20)
  address                 String?          @db.Text
  shipstation_customer_id String?          @unique @db.VarChar(50)
  company                 String?          @db.VarChar(100)
  street1                 String?          @db.VarChar(255)
  street2                 String?          @db.VarChar(255)
  street3                 String?          @db.VarChar(255)
  city                    String?          @db.VarChar(100)
  state                   String?          @db.VarChar(100)
  postal_code             String?          @db.VarChar(20)
  country                 String?          @db.VarChar(2)
  country_code            String?          @db.VarChar(2)
  customer_notes          String?          @db.Text
  created_at              DateTime         @default(now()) @db.Timestamp(0)
  updated_at              DateTime?        @db.Timestamp(0)
  address_verified_status String?          @db.VarChar(50)
  is_residential          Boolean?
  Order                   Order[]
  PrintOrderTask          PrintOrderTask[]

  @@index([email])
  @@index([shipstation_customer_id])
}

model Metric {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(255)
  value     Float
  tags      Json?
  timestamp DateTime @default(now())

  @@index([name])
  @@index([timestamp])
}

model Order {
  id                         Int                   @id @default(autoincrement())
  shipstation_order_id       String?               @unique @db.VarChar(50)
  shipstation_order_number   String?               @db.VarChar(50)
  customerId                 Int?
  customer_name              String?               @db.VarChar(100)
  order_status               String                @default("awaiting_shipment") @db.VarChar(50)
  order_key                  String?               @db.VarChar(50)
  order_date                 DateTime?             @db.DateTime(0)
  payment_date               DateTime?             @db.DateTime(0)
  ship_by_date               DateTime?             @db.DateTime(0)
  shipping_price             Decimal?              @db.Decimal(10, 2)
  tax_amount                 Decimal?              @db.Decimal(10, 2)
  discount_amount            Decimal?              @db.Decimal(10, 2)
  shipping_amount_paid       Decimal?              @db.Decimal(10, 2)
  shipping_tax               Decimal?              @db.Decimal(10, 2)
  total_price                Decimal               @default(0.00) @db.Decimal(10, 2)
  gift                       Boolean               @default(false)
  gift_message               String?               @db.Text
  gift_email                 String?               @db.VarChar(100)
  requested_shipping_service String?               @db.VarChar(100)
  carrier_code               String?               @db.VarChar(50)
  service_code               String?               @db.VarChar(50)
  package_code               String?               @db.VarChar(50)
  confirmation               String?               @db.VarChar(50)
  tracking_number            String?               @db.VarChar(100)
  shipped_date               DateTime?             @db.DateTime(0)
  warehouse_id               String?               @db.VarChar(50)
  customer_notes             String?               @db.Text
  internal_notes             String?               @db.Text
  last_sync_date             DateTime?             @db.DateTime(0)
  notes                      String?               @db.Text
  created_at                 DateTime              @default(now()) @db.Timestamp(0)
  updated_at                 DateTime?             @db.Timestamp(0)
  marketplace                String?               @db.VarChar(50)
  amount_paid                Decimal?              @db.Decimal(10, 2)
  order_weight_units         String?               @db.VarChar(20)
  order_weight_value         Decimal?              @db.Decimal(10, 4)
  payment_method             String?               @db.VarChar(50)
  shipstation_store_id       Int?
  tag_ids                    Json?
  dimensions_height          Decimal?              @db.Decimal(10, 2)
  dimensions_length          Decimal?              @db.Decimal(10, 2)
  dimensions_units           String?               @db.VarChar(20)
  dimensions_width           Decimal?              @db.Decimal(10, 2)
  insurance_insure_shipment  Boolean?
  insurance_insured_value    Decimal?              @db.Decimal(10, 2)
  insurance_provider         String?               @db.VarChar(50)
  internal_status            Order_internal_status @default(new)
  is_voided                  Boolean?              @default(false)
  marketplace_notified       Boolean               @default(false)
  void_date                  DateTime?             @db.DateTime(0)
  lastPackingSlipAt          DateTime?             @db.DateTime(0)
  is_merged                  Boolean               @default(false)
  merged_to_order_id         Int?
  merged_from_order_ids      Json?
  Customer                   Customer?             @relation(fields: [customerId], references: [id])
  OrderItem                  OrderItem[]
  PrintOrderTask             PrintOrderTask[]

  @@index([created_at])
  @@index([customerId])
  @@index([is_merged])
  @@index([marketplace])
  @@index([merged_to_order_id])
  @@index([order_status])
  @@index([shipstation_store_id])
  @@index([updated_at])
}

model OrderItem {
  id                      Int                      @id @default(autoincrement())
  orderId                 Int
  quantity                Int                      @default(1)
  unit_price              Decimal                  @db.Decimal(10, 2)
  print_settings          Json?
  created_at              DateTime                 @default(now()) @db.Timestamp(0)
  updated_at              DateTime?                @db.Timestamp(0)
  shipstationLineItemKey  String?                  @unique @db.VarChar(50)
  productId               Int
  AmazonCustomizationFile AmazonCustomizationFile?
  Order                   Order                    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  Product                 Product                  @relation(fields: [productId], references: [id])
  PrintOrderTask          PrintOrderTask[]

  @@index([orderId])
  @@index([productId])
}

model PrintOrderTask {
  id                       String                          @id
  orderId                  Int
  marketplace_order_number String?                         @db.VarChar(100)
  customerId               Int?
  custom_text              String?                         @db.Text
  quantity                 Int                             @default(1)
  color_1                  String?                         @db.VarChar(50)
  color_2                  String?                         @db.VarChar(50)
  ship_by_date             DateTime?                       @db.Date
  status                   PrintOrderTask_status           @default(pending)
  needs_review             Boolean                         @default(false)
  review_reason            String?                         @db.Text
  created_at               DateTime                        @default(now()) @db.Timestamp(0)
  updated_at               DateTime?                       @db.Timestamp(0)
  orderItemId              Int
  taskIndex                Int
  productId                Int
  shorthandProductName     String?                         @db.VarChar(100)
  annotation               String?                         @db.Text
  stl_path                 String?                         @db.VarChar(512)
  stl_render_state         PrintOrderTask_stl_render_state @default(pending)
  render_retries           Int                             @default(0)
  gdrive_file_id           String?
  gdrive_public_link       String?
  isVerified               Boolean                         @default(false)
  verifiedAt               DateTime?
  verifiedByUserId         Int?
  Customer                 Customer?                       @relation(fields: [customerId], references: [id])
  Order                    Order                           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  OrderItem                OrderItem                       @relation(fields: [orderItemId], references: [id], onDelete: Cascade)
  Product                  Product                         @relation(fields: [productId], references: [id])
  verifiedByUser           User?                           @relation("VerifiedTasks", fields: [verifiedByUserId], references: [id], onDelete: Restrict, onUpdate: Cascade)

  @@unique([orderItemId, taskIndex])
  @@index([customerId])
  @@index([needs_review])
  @@index([orderId])
  @@index([orderItemId])
  @@index([productId])
  @@index([ship_by_date])
  @@index([status])
  @@index([status, updated_at])
  @@index([stl_render_state])
}

model Product {
  id                     Int              @id @default(autoincrement())
  sku                    String?          @unique
  name                   String
  imageUrl               String?          @db.VarChar(512)
  weight                 Decimal?         @db.Decimal(10, 4)
  notes                  String?          @db.Text
  createdAt              DateTime         @default(now())
  updatedAt              DateTime
  fulfillment_sku        String?          @db.VarChar(255)
  item_weight_units      String?          @db.VarChar(20)
  item_weight_value      Decimal?         @db.Decimal(10, 4)
  shipstation_product_id Int?             @unique
  upc                    String?          @db.VarChar(50)
  warehouse_location     String?          @db.VarChar(100)
  OrderItem              OrderItem[]
  PrintOrderTask         PrintOrderTask[]

  @@index([name])
  @@index([shipstation_product_id])
  @@index([sku])
}

model ScriptRunLog {
  id           Int       @id @default(autoincrement())
  scriptName   String    @db.VarChar(100)
  runStartedAt DateTime  @default(now())
  runEndedAt   DateTime?
  status       String    @db.VarChar(50)
  errorMessage String?   @db.Text
  errorStack   String?   @db.Text
  details      Json?
  createdAt    DateTime  @default(now())

  @@index([runStartedAt])
  @@index([scriptName])
  @@index([status])
}

model Session {
  id           Int      @id @default(autoincrement())
  sessionToken String   @unique
  userId       Int
  expires      DateTime
  User         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model SyncMetrics {
  id                        String    @id
  syncId                    String
  totalApiCalls             Int       @default(0)
  totalOrdersProcessed      Int       @default(0)
  totalOrdersFailed         Int       @default(0)
  totalItemsProcessed       Int       @default(0)
  totalItemsFailed          Int       @default(0)
  totalCustomersUpserted    Int       @default(0)
  totalProductsUpserted     Int       @default(0)
  avgProcessingTimePerOrder Int       @default(0)
  maxProcessingTimePerOrder Int       @default(0)
  minProcessingTimePerOrder Int       @default(0)
  totalProcessingTime       Int       @default(0)
  startTime                 DateTime
  endTime                   DateTime?
  createdAt                 DateTime  @default(now())
}

model SyncProgress {
  id                     String    @id
  syncType               String
  startTime              DateTime
  endTime                DateTime?
  status                 String
  totalOrders            Int       @default(0)
  processedOrders        Int       @default(0)
  failedOrders           Int       @default(0)
  lastProcessedOrderId   String?
  lastProcessedTimestamp DateTime?
  error                  String?   @db.Text
  createdAt              DateTime  @default(now())
  updatedAt              DateTime

  @@index([startTime])
  @@index([status])
  @@index([syncType])
}

model Tag {
  id                 Int      @id @default(autoincrement())
  shipstation_tag_id Int      @unique
  name               String   @db.VarChar(100)
  color_hex          String?  @db.VarChar(7)
  last_synced        DateTime @default(now())

  @@index([name])
}

model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  password      String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime
  emailVerified DateTime?
  image         String?
  name          String?
  Account       Account[]
  Session       Session[]
  verifiedPrintTasks PrintOrderTask[] @relation("VerifiedTasks")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model system_logs {
  id        Int      @id @default(autoincrement())
  message   String?  @db.Text
  level     String?  @db.VarChar(50)
  timestamp DateTime @default(now())
}

enum PrintOrderTask_status {
  pending
  in_progress
  completed
  cancelled
}

enum PrintOrderTask_stl_render_state {
  pending
  running
  completed
  failed
}

enum Order_internal_status {
  new
  processing
  printing
  completed
  cancelled
}
