-- AlterTable
ALTER TABLE `PrintOrderTask` ADD COLUMN `isVerified` B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    ADD COLUMN `verifiedAt` DATETIME(3) NULL,
    ADD COLUMN `verifiedByUserId` INTEGER NULL;

-- AddForeignKey
ALTER TABLE `PrintOrderTask` ADD CONSTRAINT `PrintOrderTask_verifiedByUserId_fkey` FOREIGN KEY (`verifiedByUserId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
