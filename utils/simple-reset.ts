import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function simpleReset() {
  try {
    console.log('🔄 Starting simple reset...');
    console.log('Database URL:', process.env.DATABASE_URL?.substring(0, 30) + '...');

    // First, get the product IDs
    const products = await prisma.product.findMany({
      where: { sku: { in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'] } },
      select: { id: true, sku: true },
    });

    console.log('Found products:', products);
    const productIds = products.map(p => p.id);

    if (productIds.length === 0) {
      console.log('❌ No products found!');
      return;
    }

    // Direct update using updateMany for better reliability
    const result = await prisma.printOrderTask.updateMany({
      where: {
        productId: { in: productIds },
        stl_render_state: 'failed',
        status: { in: ['pending', 'in_progress'] },
      },
      data: {
        stl_render_state: 'pending',
        render_retries: 0,
        stl_path: null,
      },
    });

    console.log(`✅ Updated ${result.count} tasks`);

    // Verify the update
    const pendingTasks = await prisma.printOrderTask.count({
      where: {
        Product: {
          sku: { in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'] },
        },
        stl_render_state: 'pending',
        status: { in: ['pending', 'in_progress'] },
      },
    });

    console.log(`🔍 Verification: Found ${pendingTasks} tasks now in pending state`);
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleReset();
