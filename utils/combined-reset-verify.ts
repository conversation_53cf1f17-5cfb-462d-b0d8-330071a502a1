import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function combinedResetAndVerify() {
  try {
    console.log('🔄 Combined reset and verify...');

    // Step 1: Check current state
    console.log('\n📊 BEFORE RESET:');
    const beforeReset = await prisma.$queryRaw`
      SELECT 
        p.sku,
        pot.status,
        pot.stl_render_state,
        pot.render_retries,
        COUNT(*) as count
      FROM PrintOrderTask pot
      JOIN Product p ON pot.productId = p.id
      WHERE p.sku IN ('Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1')
        AND pot.status IN ('pending', 'in_progress')
        AND pot.stl_render_state = 'failed'
      GROUP BY p.sku, pot.status, pot.stl_render_state, pot.render_retries
    `;
    console.table(beforeReset);

    // Step 2: Get specific task IDs to reset
    const tasksToReset = await prisma.printOrderTask.findMany({
      where: {
        Product: {
          sku: { in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'] },
        },
        status: { in: ['pending', 'in_progress'] },
        stl_render_state: 'failed',
      },
      select: {
        id: true,
        status: true,
        stl_render_state: true,
        render_retries: true,
        Product: { select: { sku: true } },
      },
    });

    console.log(`\n🎯 Found ${tasksToReset.length} specific tasks to reset`);

    if (tasksToReset.length === 0) {
      console.log('❌ No tasks found to reset!');
      return;
    }

    // Step 3: Reset the tasks
    console.log('\n🔄 Resetting tasks...');
    const result = await prisma.printOrderTask.updateMany({
      where: {
        id: { in: tasksToReset.map(t => t.id) },
      },
      data: {
        stl_render_state: 'pending',
        render_retries: 0,
        stl_path: null,
      },
    });

    console.log(`✅ Reset ${result.count} tasks`);

    // Step 4: Verify immediately in the same connection
    console.log('\n🔍 AFTER RESET (immediate verification):');
    const afterReset = await prisma.$queryRaw`
      SELECT 
        p.sku,
        pot.status,
        pot.stl_render_state,
        pot.render_retries,
        COUNT(*) as count
      FROM PrintOrderTask pot
      JOIN Product p ON pot.productId = p.id
      WHERE p.sku IN ('Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1')
        AND pot.status IN ('pending', 'in_progress')
      GROUP BY p.sku, pot.status, pot.stl_render_state, pot.render_retries
      ORDER BY p.sku, pot.stl_render_state
    `;
    console.table(afterReset);

    // Step 5: Check processable tasks
    const processableTasks = await prisma.printOrderTask.findMany({
      where: {
        Product: {
          sku: { in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'] },
        },
        status: { in: ['pending', 'in_progress'] },
        stl_render_state: 'pending',
        render_retries: { lt: 3 },
      },
      select: {
        id: true,
        status: true,
        stl_render_state: true,
        render_retries: true,
        custom_text: true,
        Product: { select: { sku: true } },
      },
      take: 5,
    });

    console.log(`\n🎯 Found ${processableTasks.length} processable tasks:`);
    if (processableTasks.length > 0) {
      console.table(processableTasks.map(task => ({
        id: task.id.substring(0, 8) + '...',
        sku: task.Product.sku,
        status: task.status,
        stl_render_state: task.stl_render_state,
        render_retries: task.render_retries,
        custom_text: task.custom_text?.substring(0, 15) + '...',
      })));
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

combinedResetAndVerify();
