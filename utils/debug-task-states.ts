import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugTaskStates() {
  try {
    console.log('=== DEBUGGING TASK STATES ===\n');

    // Get all products with Y3D SKUs
    const products = await prisma.product.findMany({
      where: { 
        sku: { 
          in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'] 
        } 
      },
      select: { id: true, sku: true, name: true },
    });

    console.log('Found products:');
    products.forEach(product => {
      console.log(`  - ${product.sku}: ${product.name} (ID: ${product.id})`);
    });
    console.log();

    if (products.length === 0) {
      console.error('❌ No products found! This might be the issue.');
      return;
    }

    const productIds = products.map(p => p.id);

    // Check all tasks for these products regardless of state
    const allTasks = await prisma.printOrderTask.findMany({
      where: {
        productId: { in: productIds },
      },
      select: {
        id: true,
        status: true,
        stl_render_state: true,
        render_retries: true,
        custom_text: true,
        Product: {
          select: {
            sku: true,
            name: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
      take: 50, // Limit to recent 50 tasks
    });

    console.log(`Found ${allTasks.length} total tasks for these products:`);
    
    if (allTasks.length > 0) {
      // Group by status and render state
      const statusGroups = allTasks.reduce((acc, task) => {
        const key = `${task.status}-${task.stl_render_state}`;
        if (!acc[key]) acc[key] = [];
        acc[key].push(task);
        return acc;
      }, {} as Record<string, typeof allTasks>);

      console.log('\nTasks grouped by status-render_state:');
      Object.entries(statusGroups).forEach(([key, tasks]) => {
        console.log(`  ${key}: ${tasks.length} tasks`);
      });

      // Show detailed breakdown by product
      console.log('\nDetailed breakdown by product:');
      products.forEach(product => {
        const productTasks = allTasks.filter(t => t.Product.sku === product.sku);
        console.log(`\n${product.sku} (${productTasks.length} tasks):`);
        
        const productStatusGroups = productTasks.reduce((acc, task) => {
          const key = `${task.status}-${task.stl_render_state}`;
          if (!acc[key]) acc[key] = 0;
          acc[key]++;
          return acc;
        }, {} as Record<string, number>);

        Object.entries(productStatusGroups).forEach(([key, count]) => {
          console.log(`  ${key}: ${count} tasks`);
        });
      });

      // Show some sample tasks
      console.log('\nSample tasks (first 10):');
      console.table(allTasks.slice(0, 10).map(task => ({
        id: task.id.substring(0, 8) + '...',
        sku: task.Product.sku,
        status: task.status,
        stl_render_state: task.stl_render_state,
        render_retries: task.render_retries,
        custom_text: task.custom_text?.substring(0, 15) + (task.custom_text && task.custom_text.length > 15 ? '...' : ''),
      })));
    }

    // Check specifically for tasks that should be processable
    const processableTasks = await prisma.printOrderTask.findMany({
      where: {
        productId: { in: productIds },
        status: { in: ['pending', 'in_progress'] },
        stl_render_state: 'pending',
        render_retries: { lt: 3 },
      },
      select: {
        id: true,
        status: true,
        stl_render_state: true,
        render_retries: true,
        custom_text: true,
        Product: {
          select: {
            sku: true,
            name: true,
          },
        },
      },
      take: 10,
    });

    console.log(`\n🔍 Found ${processableTasks.length} tasks that should be processable by the worker:`);
    if (processableTasks.length > 0) {
      console.table(processableTasks.map(task => ({
        id: task.id.substring(0, 8) + '...',
        sku: task.Product.sku,
        status: task.status,
        stl_render_state: task.stl_render_state,
        render_retries: task.render_retries,
        custom_text: task.custom_text?.substring(0, 15) + (task.custom_text && task.custom_text.length > 15 ? '...' : ''),
      })));
    } else {
      console.log('❌ No processable tasks found! This explains why the worker is not picking up tasks.');
    }

  } catch (error) {
    console.error('Error debugging task states:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugTaskStates();
