import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function resetBubbleAndSignatureTasks() {
  try {
    console.log('Looking up pending print tasks for bubble and signature style products...\n');

    // Get product IDs for Y3D-NKC-001 (signature), Y3D-NKC-002 (bubble), and Y3D-REGKEY-STL1 (reg keys)
    const products = await prisma.product.findMany({
      where: {
        sku: {
          in: ['Y3D-NKC-001', 'Y3D-NKC-002', 'Y3D-REGKEY-STL1'],
        },
      },
      select: { id: true, sku: true, name: true },
    });

    if (products.length === 0) {
      console.error('No products found with SKUs Y3D-NKC-001, Y3D-NKC-002, or Y3D-REGKEY-STL1');
      return;
    }

    console.log('Found products:');
    products.forEach(product => {
      console.log(`  - ${product.sku}: ${product.name} (ID: ${product.id})`);
    });
    console.log();

    const productIds = products.map(p => p.id);

    // Find all tasks for these products that are still pending but may have failed STL rendering
    const tasksToReset = await prisma.printOrderTask.findMany({
      where: {
        productId: { in: productIds },
        status: { in: ['pending', 'in_progress'] },
        stl_render_state: { in: ['failed', 'running'] }, // Reset failed or stuck running tasks
      },
      select: {
        id: true,
        status: true,
        stl_render_state: true,
        render_retries: true,
        custom_text: true,
        Product: {
          select: {
            sku: true,
            name: true,
          },
        },
      },
    });

    console.log(`Found ${tasksToReset.length} tasks to reset:`);

    if (tasksToReset.length > 0) {
      console.table(
        tasksToReset.map(task => ({
          id: task.id,
          sku: task.Product.sku,
          status: task.status,
          stl_render_state: task.stl_render_state,
          render_retries: task.render_retries,
          custom_text:
            task.custom_text?.substring(0, 30) +
            (task.custom_text && task.custom_text.length > 30 ? '...' : ''),
        }))
      );

      // Reset these tasks back to pending STL render state
      console.log('\n🔄 Attempting to reset tasks...');

      const result = await prisma.printOrderTask.updateMany({
        where: {
          id: { in: tasksToReset.map(t => t.id) },
        },
        data: {
          stl_render_state: 'pending',
          render_retries: 0,
          stl_path: null,
        },
      });

      console.log(`\n✅ Update query executed. Affected rows: ${result.count}`);

      // Verify the reset worked by checking a few tasks
      console.log('\n🔍 Verifying reset...');
      const verifyTasks = await prisma.printOrderTask.findMany({
        where: {
          id: { in: tasksToReset.slice(0, 5).map(t => t.id) },
        },
        select: {
          id: true,
          stl_render_state: true,
          render_retries: true,
        },
      });

      console.log('Sample of reset tasks:');
      console.table(
        verifyTasks.map(task => ({
          id: task.id.substring(0, 8) + '...',
          stl_render_state: task.stl_render_state,
          render_retries: task.render_retries,
        }))
      );

      const stillFailed = verifyTasks.filter(t => t.stl_render_state === 'failed');
      if (stillFailed.length > 0) {
        console.error(`❌ WARNING: ${stillFailed.length} tasks are still in failed state!`);
      } else {
        console.log(`✅ Verification successful: All sampled tasks are now in pending state`);
      }
    } else {
      console.log('No tasks found that need resetting.');
    }

    // Show summary of all tasks for these products
    console.log('\n--- Summary of all tasks for bubble and signature products ---');

    const allTasks = await prisma.printOrderTask.groupBy({
      by: ['stl_render_state'],
      where: {
        productId: { in: productIds },
        status: { in: ['pending', 'in_progress'] },
      },
      _count: {
        id: true,
      },
    });

    console.log('Tasks by STL render state:');
    allTasks.forEach(group => {
      console.log(`  ${group.stl_render_state}: ${group._count.id} tasks`);
    });

    // Show breakdown by product
    console.log('\nTasks by product:');
    for (const product of products) {
      const productTaskCount = await prisma.printOrderTask.count({
        where: {
          productId: product.id,
          status: { in: ['pending', 'in_progress'] },
          stl_render_state: 'pending',
        },
      });
      console.log(`  ${product.sku}: ${productTaskCount} pending STL render tasks`);
    }
  } catch (error) {
    console.error('Error resetting bubble and signature tasks:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetBubbleAndSignatureTasks();
