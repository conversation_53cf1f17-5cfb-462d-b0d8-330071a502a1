// Simple PM2 configuration for both staging and production on same server
module.exports = {
  apps: [
    {
      name: 'Y3DHub_Staging',
      script: 'npm',
      args: 'run start',
      cwd: '/home/<USER>/Y3DHub_staging',
      env: {
        NODE_ENV: 'development',
        PORT: '3001',
        NEXTAUTH_URL: 'http://localhost:3001'
      },
      watch: false,
      max_memory_restart: '1G'
    },
    {
      name: 'Y3DHub_Production',
      script: 'npm',
      args: 'run start',
      cwd: '/home/<USER>/Y3DHub_staging', // Use same directory!
      env: {
        NODE_ENV: 'production',
        PORT: '3000',
        NEXTAUTH_URL: 'http://localhost:3000'
      },
      watch: false,
      max_memory_restart: '2G',
      instances: 1,
      exec_mode: 'cluster'
    }
  ]
};