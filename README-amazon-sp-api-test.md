# Amazon SP-API Test Script

This script allows you to test your Amazon Selling Partner API (SP-API) credentials by retrieving order information.

## Prerequisites

1. You need to have a registered Amazon SP-API application with the "Orders" role enabled
2. You need to have generated a refresh token for your application
3. Node.js and npm installed

## Setup

1. Install the required dependencies:

```bash
npm install amazon-sp-api dotenv
```

2. Create a `.env` file with your SP-API credentials:

```
# Required
SPAPI_LWA_APP_CLIENT_ID=your_client_id
SPAPI_LWA_APP_CLIENT_SECRET=your_client_secret
SPAPI_LWA_REFRESH_TOKEN=your_refresh_token

# Optional
SPAPI_SELLER_ID=your_seller_id
SPAPI_MARKETPLACE_IDS=A1F83G8C2ARO7P,ATVPDKIKX0DER
SPAPI_REGION=eu
SPAPI_ENDPOINT=custom_endpoint_if_needed
SPAPI_AWS_ACCESS_KEY_ID=your_aws_access_key
SPAPI_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

You can use the `amazon-env-example` file as a template.

## Environment Variables Explained

| Variable                      | Required? | Description                                                       |
| ----------------------------- | --------- | ----------------------------------------------------------------- |
| `SPAPI_LWA_APP_CLIENT_ID`     | Yes       | Your Login with Amazon client ID                                  |
| `SPAPI_LWA_APP_CLIENT_SECRET` | Yes       | Your Login with Amazon client secret                              |
| `SPAPI_LWA_REFRESH_TOKEN`     | Yes       | Refresh token generated when authorizing your application         |
| `SPAPI_SELLER_ID`             | No        | Your seller ID (only needed for certain operations)               |
| `SPAPI_MARKETPLACE_IDS`       | No        | Comma-separated list of marketplace IDs (defaults to UK)          |
| `SPAPI_REGION`                | No        | Region code: 'na' (North America), 'eu' (Europe), 'fe' (Far East) |
| `SPAPI_ENDPOINT`              | No        | Custom endpoint URL if needed                                     |
| `SPAPI_AWS_ACCESS_KEY_ID`     | No        | AWS access key (no longer required since October 2023)            |
| `SPAPI_AWS_SECRET_ACCESS_KEY` | No        | AWS secret key (no longer required since October 2023)            |

## Usage

The script supports several commands for different API operations:

```bash
./test-amazon-sp-api.js [orderId] [command] [additionalParam]
```

You can also run commands directly without specifying an order ID:

```bash
./test-amazon-sp-api.js [command] [additionalParam]
```

To see all available commands and examples:

```bash
./test-amazon-sp-api.js help
```

### Commands

1. **Default (order)** - Retrieve a specific order and its items:

   ```bash
   ./test-amazon-sp-api.js 123-4567890-1234567
   ```

2. **Unshipped Orders** - Retrieve all unshipped orders:

   ```bash
   ./test-amazon-sp-api.js unshipped
   ```

3. **Buyer Information** - Retrieve order details including buyer information:

   ```bash
   ./test-amazon-sp-api.js 123-4567890-1234567 buyer
   ```

4. **Create Report** - Request a report for unshipped orders:

   ```bash
   ./test-amazon-sp-api.js report
   ```

   This creates an asynchronous report request. The report will be generated in the background and will not be immediately available.

5. **Check Report Status** - Check the status of a previously created report:

   ```bash
   ./test-amazon-sp-api.js check-report 12345678-1234-1234-1234-123456789012
   ```

   Where the parameter is the `reportId` returned when creating a report.

6. **Download Report** - Download a completed report document:

   ```bash
   ./test-amazon-sp-api.js download-report 12345678-1234-1234-1234-123456789012
   ```

   Where the parameter is the `reportDocumentId` returned when checking a completed report.

7. **Help** - Display usage information and examples:
   ```bash
   ./test-amazon-sp-api.js help
   ```

### Testing with the Sandbox Environment

To test with the Amazon sandbox environment (no real orders needed):

```bash
./test-amazon-sp-api.js TEST_CASE_200
```

This will use Amazon's test case "TEST_CASE_200" which simulates a successful order retrieval.

## Output Files

The script generates several JSON files depending on the command used:

- `amazon-order-{orderId}.json` - Order details
- `amazon-order-items-{orderId}.json` - Order items
- `amazon-orders-list.json` - Recent orders (last 30 days)
- `amazon-unshipped-orders.json` - Unshipped orders (when using "unshipped" command)
- `amazon-order-buyer-{orderId}.json` - Order buyer info (when using "buyer" command)
- `amazon-order-items-buyer-{orderId}.json` - Order items buyer info (when using "buyer" command)
- `amazon-report-request.json` - Report request details (when using "report" command)
- `amazon-report-status-{reportId}.json` - Report status details (when using "check-report" command)
- `amazon-report-document-{reportDocumentId}.json` - Report document metadata (when using "download-report" command)
- `amazon-reports/report-{reportDocumentId}.txt` - The actual downloaded report content

## Marketplace IDs

Common marketplace IDs:

- UK: A1F83G8C2ARO7P
- US: ATVPDKIKX0DER
- Canada: A2EUQ1WTGCTBG2
- Germany: A1PA6795UKMFR9
- France: A13V1IB3VIYZZH
- Italy: APJ6JRA9NG5V4
- Spain: A1RKKUPIHCS9HS
- Netherlands: A1805IZSGTT6HS
- Sweden: A2NODRKZP88ZB9
- Poland: A1C3SOZRARQ6R3
- Turkey: A33AVAJ2PDY3EV
- United Arab Emirates: A2VIGQ35RCS4UG
- India: A21TJRUUN4KGV
- Singapore: A19VAU5U5O7RUS
- Australia: A39IBJ37TRP1C6
- Japan: A1VC38T7YXB528
- Mexico: A1AM78C64UM0Y8
- Brazil: A2Q3Y263D00KWC

## Reports API for Unshipped Orders

For retrieving unshipped orders in bulk, the script includes a complete workflow for the Reports API with the report type `GET_FLAT_FILE_ACTIONABLE_ORDER_DATA_SHIPPING`.

This report provides comprehensive order data but is generated asynchronously, requiring these steps:

1. Create a report request:

   ```bash
   ./test-amazon-sp-api.js report
   ```

   This returns a `reportId` which you'll need for the next step.

2. Check report processing status:

   ```bash
   ./test-amazon-sp-api.js check-report <reportId>
   ```

   This will tell you if the report is still being processed or is ready to download. When ready, it will provide a `reportDocumentId`.

3. Download the completed report:
   ```bash
   ./test-amazon-sp-api.js download-report <reportDocumentId>
   ```
   This will download the report to the `amazon-reports` directory.

The report type `GET_FLAT_FILE_ACTIONABLE_ORDER_DATA_SHIPPING` includes all unshipped orders that need to be fulfilled, with detailed information about each order including customer details, shipping address, and item information.

## Troubleshooting

### 401 Unauthorized Error

If you see a 401 error, check that:

- Your LWA client ID and secret are correct
- Your refresh token is valid and not expired
- Your application has the "Orders" role enabled

### 403 Forbidden Error

If you see a 403 error, check that:

- Your application has the "Orders" role enabled
- The seller account has authorized your application with the correct scopes
- You're trying to access an order that belongs to the seller account that authorized your application

### 404 Not Found Error

If you see a 404 error when testing with a real order ID:

- Verify that the order ID is correct
- Ensure the order belongs to the seller account that authorized your application

### Invalid Marketplace ID

If you see an error about invalid marketplace IDs:

- Check that the marketplace IDs in your `SPAPI_MARKETPLACE_IDS` variable are correct
- Ensure you're using the correct region for your marketplace IDs

### Report Creation or Download Issues

If you have issues with report operations:

- Ensure your application has the necessary roles for report creation and access
- For unshipped orders report, the "Direct to Consumer Shipping" role is required
- The report URL is temporary and will expire quickly, so the script automatically downloads it
- If download fails, the URL may have expired - create a new report request

## Amazon SP-API Documentation

For more information about the Amazon SP-API, refer to the official documentation:

- [SP-API Documentation](https://developer-docs.amazon.com/sp-api/)
- [Orders API Documentation](https://developer-docs.amazon.com/sp-api/docs/orders-api-v0-reference)
- [Reports API Documentation](https://developer-docs.amazon.com/sp-api/docs/reports-api-v2021-06-30-reference)
