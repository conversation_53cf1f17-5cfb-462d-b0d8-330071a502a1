#!/bin/bash
# Simple local deployment script for manual deployment

set -e

echo "🚀 Starting deployment..."

# Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin main

# Install dependencies if package.json changed
if git diff HEAD@{1} --name-only | grep -q "package.json"; then
    echo "📦 Installing dependencies..."
    npm ci
fi

# Run database migrations if needed
if git diff HEAD@{1} --name-only | grep -q "prisma/"; then
    echo "🗄️ Running database migrations..."
    npx prisma migrate deploy
fi

# Build the application
echo "🔨 Building application..."
npm run build

# Reload PM2 processes
echo "🔄 Reloading PM2 processes..."
pm2 reload ecosystem.config.cjs --update-env

echo "✅ Deployment complete!"
echo "📊 PM2 Status:"
pm2 status