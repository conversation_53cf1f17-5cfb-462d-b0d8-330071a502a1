# Database Configuration
# Format: mysql://user:password@host:port/database_name
DATABASE_URL="mysql://user:password@localhost:3306/y3dhub"
# To use local SQLite (offline): comment out or remove DATABASE_URL
# Enable/disable Google Drive integration
GDRIVE_ENABLED=true
# Google Drive folder ID for file storage
GDRIVE_FOLDER_ID="1371X-b1EgnePbf2TBuFq7rJna8XNYfft"
# Path to Google Service Account JSON file
GDRIVE_SERVICE_ACCOUNT_PATH="./service-account.json"
# Dev Container Database Configuration (used by docker-compose.yml)
# These variables configure the MySQL database in the dev container
MYSQL_ROOT_PASSWORD="devpassword"
MYSQL_USER="dbuser"
MYSQL_PASSWORD="dbpassword"
MYSQL_DATABASE="y3dhub"

# API Keys
SHIPSTATION_API_KEY="your_shipstation_api_key"
SHIPSTATION_API_SECRET="your_shipstation_api_secret"
OPENAI_API_KEY="your_openai_api_key"
OPEN_ROUTER_API_KEY="your_open_router_api_key"
SYNC_API_KEY="your_sync_api_key"

# Security
# Minimum 32 characters long
SESSION_PASSWORD="your_session_password_at_least_32_chars_long"
# Minimum 32 characters long, used for NextAuth encryption
NEXTAUTH_SECRET="your_nextauth_secret_at_least_32_chars_long"
# URL where the app will be accessible
NEXTAUTH_URL="http://localhost:3000"

# Application Settings
PORT=8081
NODE_ENV="development|staging|production"

# Amazon Selling Partner API Credentials
SPAPI_LWA_APP_CLIENT_ID="your_lwa_client_id"
SPAPI_LWA_APP_CLIENT_SECRET="your_lwa_client_secret"
SPAPI_LWA_REFRESH_TOKEN="your_lwa_refresh_token"

# AWS Credentials
SPAPI_AWS_ACCESS_KEY_ID="your_aws_access_key_id"
SPAPI_AWS_SECRET_ACCESS_KEY="your_aws_secret_access_key"

# Seller & Endpoint Details
SPAPI_SELLER_ID="your_seller_id"
SPAPI_MARKETPLACE_IDS="your_marketplace_ids"
SPAPI_ENDPOINT="https://sellingpartnerapi-eu.amazon.com"

# Environment-specific settings
# Set to development, staging, or production
NEXT_PUBLIC_ENVIRONMENT="development|staging|production"
# Public URL where the application will be hosted
NEXT_PUBLIC_APP_URL="your_app_url"

# Email Notification Settings
SENDGRID_TO_EMAIL="<EMAIL>"
SENDGRID_TEST_TO="<EMAIL>"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_API_KEY="your_sendgrid_api_key"
NEW_ORDER_NOTIFICATION_EMAILS="<EMAIL>,<EMAIL>"

# LiteLLM Configuration
LITELLM_DATABASE_URL="**************************************************/litellm"
LITELLM_SALT_KEY="random-32-char-salt"
UI_USERNAME="admin"
UI_PASSWORD="changeme"
LITELLM_MASTER_KEY="sk-master-dev"
DEFAULT_LLM_KEY="your_default_llm_key"
# OPENAI_API_BASE_URL="http://localhost:4000"
