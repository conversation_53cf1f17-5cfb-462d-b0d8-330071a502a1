# Claude Code Integration for Y3DHub

This repository is fully integrated with **Claude Code** for AI-powered development assistance, code reviews, and architectural guidance.

## 🚀 Features

### Comprehensive Code Analysis

- **Security**: Vulnerability scanning and best practices
- **Performance**: Optimization for high-volume operations (1000+ orders/day)
- **Architecture**: Design pattern recommendations
- **Business Logic**: 3D printing workflow validation
- **TypeScript**: Strict type safety enforcement
- **Database**: Prisma query optimization

### Y3DHub-Specific Intelligence

- **ShipStation API**: Integration patterns and error handling
- **Amazon SP-API**: Marketplace integration best practices
- **OpenSCAD**: 3D modeling code optimization
- **STL Processing**: File generation and performance
- **Print Queue**: Optimization algorithms and batching
- **Order Processing**: End-to-end workflow validation

## 🎯 Quick Start

**⚠️ FIRST: Ensure you're on staging branch (`git checkout staging`) before any development work!**

### Basic Usage

```
@claude review this PR for performance and security
@claude analyze the print queue optimization logic
@claude help with ShipStation API error handling
```

### Specialized Commands

```
@claude shipstation - Review ShipStation integration
@claude database - Analyze database queries and schema
@claude stl - Review 3D file generation code
@claude performance - High-volume operation analysis
@claude security - Security vulnerability scan
```

## 📁 Configuration

### Core Configuration Files

- **`.claude/config.yaml`** - Main configuration and analysis settings
- **`.claude/styleguide.md`** - Y3DHub coding standards and patterns
- **`.claude/commands.md`** - Available commands and usage examples

### GitHub Integration

- **`.github/workflows/claude-code.yml`** - Automated analysis workflow
- **`.github/PULL_REQUEST_TEMPLATE.md`** - PR template with Claude integration
- **`.github/ISSUE_TEMPLATE/claude-analysis.md`** - Issue template for analysis requests

## 🔧 Advanced Features

### Automated Code Review

Claude automatically reviews:

- All pull requests for code quality, security, and performance
- Database migrations for safety and optimization
- API integrations for best practices
- 3D printing workflows for efficiency

### Context-Aware Analysis

Claude understands:

- **Business Context**: 3D printing operations and order fulfillment
- **Scale Requirements**: 1000+ orders per day processing
- **Tech Stack**: Next.js 14, Prisma, MySQL, TypeScript
- **Integrations**: ShipStation, Amazon, OpenAI APIs
- **Performance Needs**: STL generation and database optimization

### Specialized Workflows

- **Pre-deployment**: Production readiness checks
- **Security Scanning**: Comprehensive vulnerability analysis
- **Performance Tuning**: High-volume operation optimization
- **Architecture Review**: Design pattern validation

## 🎭 Usage Patterns

### Pull Request Reviews

```
@claude review focusing on:
- Database query performance for large order datasets
- ShipStation API error handling and retries
- TypeScript type safety for order processing
- Security implications of the changes
```

### Issue Analysis

```
@claude analyze the performance bottleneck in STL generation
- Current: 45 seconds per file
- Target: <30 seconds per file
- Consider memory usage and OpenSCAD optimization
```

### Architecture Guidance

```
@claude suggest the best approach for:
- Batching 1000+ print tasks efficiently
- Handling ShipStation API rate limits
- Optimizing database queries for order history
```

## 🔍 What Claude Analyzes

### Codebase Coverage

- **Frontend**: React components, pages, and UI logic
- **Backend**: API routes, business logic, and integrations
- **Database**: Schema, queries, and migrations
- **Workers**: Background jobs and STL processing
- **Configuration**: Build, deployment, and environment setup

### Quality Metrics

- **Type Safety**: TypeScript usage and strict typing
- **Performance**: Query optimization and rendering speed
- **Security**: Input validation and API protection
- **Accessibility**: UI compliance and user experience
- **Testing**: Coverage and quality of test suites
- **Documentation**: Code comments and API docs

## 🛡️ Security & Privacy

- **API Keys**: Secure handling and rotation recommendations
- **Data Protection**: Customer data privacy compliance
- **Input Validation**: Comprehensive sanitization checks
- **Environment Variables**: Proper secret management
- **Authentication**: NextAuth.js security patterns

## 📊 Performance Focus

Claude specifically optimizes for:

- **High-Volume Operations**: 1000+ orders/day processing
- **Database Efficiency**: Large dataset query optimization
- **STL Generation**: Memory and CPU optimization
- **API Integrations**: Rate limiting and error recovery
- **Real-time Updates**: WebSocket and state management

## 🎓 Learning Integration

Claude helps with:

- **Best Practices**: Next.js 14 and React patterns
- **Design Patterns**: Scalable architecture decisions
- **Problem Solving**: Complex business logic implementation
- **Code Quality**: Maintainable and testable code
- **Performance**: Optimization strategies and techniques

## 📈 Continuous Improvement

Claude continuously learns from:

- **Project Patterns**: Y3DHub-specific code conventions
- **Business Requirements**: 3D printing workflow needs
- **Performance Data**: Real-world optimization results
- **Team Feedback**: Code review discussions and decisions

## ⚠️ CRITICAL: Branch Workflow Rules

**🚨 NEVER WORK DIRECTLY ON THE MAIN BRANCH 🚨**

### Mandatory Workflow

1. **ALWAYS** work on the `staging` branch for all development
2. **ALWAYS** commit changes to `staging` first
3. **ALWAYS** push to `staging` to trigger staging deployment
4. **ONLY** merge `staging` → `main` for production deployment

### Branch Protection Rules

- ❌ **NEVER** `git checkout main` for development work
- ❌ **NEVER** commit directly to main branch
- ❌ **NEVER** push changes directly to main
- ✅ **ALWAYS** use staging → main flow
- ✅ **ALWAYS** test on staging before production

### Deployment Flow

```bash
# ✅ CORRECT WORKFLOW
git checkout staging           # Work on staging
# ... make changes ...
git add . && git commit -m "..."
git push origin staging        # Triggers staging deployment (port 3001)

# After testing staging:
git checkout main
git merge staging              # Merge tested changes
git push origin main           # Triggers production deployment (port 3000)
```

### Why This Matters

- **Staging deployment**: Tests changes in isolation (port 3001)
- **Production safety**: Prevents untested code reaching production (port 3000)
- **Workflow optimization**: Triggers correct number of workflows (1 for staging, 2 for main)
- **Environment isolation**: Maintains development (3002), staging (3001), production (3000) separation

**⚡ This workflow is MANDATORY for all Y3DHub development ⚡**

## ✅ Code Quality Status

The Y3DHub codebase is currently **100% clean** with:

- ✅ **0 ESLint errors**
- ✅ **0 ESLint warnings**
- ✅ **Full TypeScript 5.8.3 compatibility**
- ✅ **Optimized VS Code configuration**
- ✅ **Prettier formatting setup**
- ✅ **Clean repository ready for production**

All linting issues have been resolved and the development environment is fully optimized.

## 🚀 Getting Started

1. **Install Claude App**: Available at https://github.com/apps/claude
2. **Add API Key**: Set `ANTHROPIC_API_KEY` in repository secrets
3. **Start Using**: Simply mention `@claude` in any PR or issue
4. **Explore Commands**: Try `/claude help` for available features

## 📚 Resources

- **GitHub Actions Workflow**: `.github/workflows/claude-code.yml`
- **Configuration Guide**: `.claude/config.yaml`
- **Style Guide**: `.claude/styleguide.md`
- **Command Reference**: `.claude/commands.md`
- **Official Documentation**: https://docs.anthropic.com/claude-code

---

**Claude Code is now fully integrated and ready to assist with Y3DHub development! 🎉**
