# Y3DHub Cursor/Windsurf AI Rules

## Project Context
You are working on Y3DHub, a comprehensive 3D printing order management platform that processes 1000+ orders per day. The system integrates with ShipStation, Amazon SP-API, and uses OpenSCAD for STL file generation.

## Tech Stack
- **Frontend**: Next.js 14, <PERSON>act, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Prisma ORM, MySQL
- **APIs**: ShipStation, Amazon SP-API, OpenAI
- **3D Processing**: OpenSCAD, STL generation workers
- **Authentication**: NextAuth.js
- **Deployment**: PM2, Docker containers

## Code Patterns

### Always Use
- TypeScript with strict typing
- Prisma for database operations
- Absolute imports with `@/` prefix
- Server actions for form handling
- Error boundaries for React components
- Proper error handling with try/catch
- Input validation using Zod schemas

### File Structure
- `src/app/` - Next.js App Router pages and API routes
- `src/components/` - Reusable React components
- `src/lib/` - Utility functions and configurations
- `src/scripts/` - Background processing scripts
- `src/workers/` - Heavy computation workers
- `prisma/` - Database schema and migrations

### Database Patterns
- Use Prisma client with proper error handling
- Always use transactions for multi-table operations
- Include relevant relations in queries to avoid N+1 problems
- Use database indexes for frequently queried fields
- Follow naming conventions: camelCase for fields

### API Design
- Use Next.js 14 App Router API routes
- Implement proper error responses with HTTP status codes
- Use middleware for authentication and CORS
- Validate inputs with Zod schemas
- Return consistent JSON response formats

### React Patterns
- Use Server Components by default, Client Components when needed
- Implement proper loading states and error boundaries
- Use React Hook Form for complex forms
- Implement optimistic updates for better UX
- Use Suspense boundaries for data fetching

### Performance Requirements
- Optimize for 1000+ orders/day processing
- Use database connection pooling
- Implement proper caching strategies
- Optimize STL generation for memory usage
- Use background workers for heavy operations

## Business Logic

### Order Processing
- Orders come from multiple marketplaces (Amazon, eBay, Etsy)
- Each order item creates print tasks
- STL files are generated using OpenSCAD
- Print tasks track progress through various statuses
- Completed orders sync back to ShipStation

### Print Queue Management
- Group similar print tasks for efficiency
- Optimize print sequences to minimize material waste
- Track print task statuses: pending, printing, completed, shipped
- Handle bulk operations for production efficiency

### Color Processing
- Extract colors from order customizations
- Map colors to available materials
- Handle dual-color and single-color prints
- Validate color combinations

## Error Handling
- Always wrap database operations in try/catch
- Log errors with appropriate context
- Return user-friendly error messages
- Implement retry logic for external API calls
- Use proper HTTP status codes

## Security Practices
- Validate all inputs on both client and server
- Use environment variables for sensitive data
- Implement proper authentication middleware
- Sanitize user inputs to prevent XSS
- Use HTTPS for all external API calls

## Testing Approach
- Write unit tests for utility functions
- Integration tests for API endpoints
- Use Vitest for testing framework
- Mock external API calls in tests
- Test error scenarios thoroughly

## Common Commands
- `npm run dev` - Start development server (requires MySQL)
- `npm run dev:offline` - Start development with SQLite (for offline AI development)
- `npm run build` - Build for production
- `npm run build:offline` - Build with SQLite (for offline environments)
- `npm run test` - Run test suite
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npx prisma generate` - Generate Prisma client
- `npx prisma migrate dev` - Run database migrations
- `npm run db:offline:setup` - Setup SQLite for offline development

## External APIs
- **ShipStation**: Order management and shipping
- **Amazon SP-API**: Marketplace integration
- **OpenAI**: AI-powered features and automation
- **SendGrid**: Email notifications

## Performance Monitoring
- Monitor API response times
- Track database query performance
- Monitor STL generation times
- Alert on error rates
- Track order processing throughput

## When Suggesting Code
1. Always include proper TypeScript types
2. Use existing patterns from the codebase
3. Include error handling
4. Consider performance implications
5. Follow the established file structure
6. Use appropriate React patterns (Server/Client components)
7. Include relevant tests when appropriate
8. Consider the high-volume requirements (1000+ orders/day)

## Files to Reference
- `prisma/schema.prisma` - Database schema
- `src/lib/prisma.ts` - Database client setup
- `src/lib/auth.ts` - Authentication configuration
- `src/lib/shipstation/` - ShipStation integration
- `src/lib/orders/` - Order processing logic
- `src/components/ui/` - Reusable UI components