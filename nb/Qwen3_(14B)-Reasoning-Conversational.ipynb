{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/y3dltd/Y3DCore/blob/main/nb/Qwen3_(14B)-Reasoning-Conversational.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "ACedLIxqfjA-"}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {"id": "l1VO0MszfjBA"}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {"id": "oOKaohsvfjBA"}, "source": ["Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {"id": "p0O86bBxfjBB"}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "gn0Q_-8QfjBB"}, "outputs": [], "source": ["%%capture\n", "import os\n", "if \"COLAB_\" not in \"\".join(os.environ.keys()):\n", "    !pip install unsloth\n", "else:\n", "    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n", "    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl==0.15.2 triton cut_cross_entropy unsloth_zoo\n", "    !pip install sentencepiece protobuf \"datasets>=3.4.1\" huggingface_hub hf_transfer\n", "    !pip install --no-deps unsloth"]}, {"cell_type": "markdown", "metadata": {"id": "iajq1W8ipjyK"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QmUBVEnvCDJv"}, "outputs": [], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "fourbit_models = [\n", "    \"unsloth/Qwen3-1.7B-unsloth-bnb-4bit\", # Qwen 14B 2x faster\n", "    \"unsloth/Qwen3-4B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-8B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-14B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-32B-unsloth-bnb-4bit\",\n", "\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/Phi-4\",\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/orpheus-3b-0.1-ft-unsloth-bnb-4bit\" # [NEW] We support TTS models!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Qwen3-14B\",\n", "    max_seq_length = 2048,   # Context length - can be longer, but uses more memory\n", "    load_in_4bit = True,     # 4bit uses much less memory\n", "    load_in_8bit = False,    # A bit more accurate, uses 2x memory\n", "    full_finetuning = False, # We have full finetuning now!\n", "    # token = \"hf_...\",      # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6bZsfBuZDeCL"}, "outputs": [], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 32,           # Choose any number > 0! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 32,  # Best to choose alpha = rank or rank*2\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,   # We support rank stabilized LoRA\n", "    loftq_config = None,  # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "Qwen3 has both reasoning and a non reasoning mode. So, we should use 2 datasets:\n", "\n", "1. We use the [Open Math Reasoning]() dataset which was used to win the [AIMO](https://www.kaggle.com/competitions/ai-mathematical-olympiad-progress-prize-2/leaderboard) (AI Mathematical Olympiad - Progress Prize 2) challenge! We sample 10% of verifiable reasoning traces that used DeepSeek R1, and whicht got > 95% accuracy.\n", "\n", "2. We also leverage [Maxime Labonne's FineTome-100k](https://huggingface.co/datasets/mlabonne/FineTome-100k) dataset in ShareGPT style. But we need to convert it to HuggingFace's normal multiturn format as well."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5kyTw2n1edte"}, "outputs": [], "source": ["from datasets import load_dataset\n", "reasoning_dataset = load_dataset(\"unsloth/OpenMathReasoning-mini\", split = \"cot\")\n", "non_reasoning_dataset = load_dataset(\"mlabonne/FineTome-100k\", split = \"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "PTZICZtie3lQ"}, "source": ["Let's see the structure of both datasets:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DjgH3lt0e2Sz"}, "outputs": [], "source": ["reasoning_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_zoaygOAe3I2"}, "outputs": [], "source": ["non_reasoning_dataset"]}, {"cell_type": "markdown", "metadata": {"id": "YX8H3urDe00l"}, "source": ["We now convert the reasoning dataset into conversational format:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LjY75GoYUCB8"}, "outputs": [], "source": ["def generate_conversation(examples):\n", "    problems  = examples[\"problem\"]\n", "    solutions = examples[\"generated_solution\"]\n", "    conversations = []\n", "    for problem, solution in zip(problems, solutions):\n", "        conversations.append([\n", "            {\"role\" : \"user\",      \"content\" : problem},\n", "            {\"role\" : \"assistant\", \"content\" : solution},\n", "        ])\n", "    return { \"conversations\": conversations, }"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gbh19fTOfHDB"}, "outputs": [], "source": ["reasoning_conversations = tokenizer.apply_chat_template(\n", "    reasoning_dataset.map(generate_conversation, batched = True)[\"conversations\"],\n", "    tokenize = False,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "XTexROzQfJn5"}, "source": ["Let's see the first transformed row:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mkj4c6NrfIz3"}, "outputs": [], "source": ["reasoning_conversations[0]"]}, {"cell_type": "markdown", "metadata": {"id": "5OMhyEXkfM5e"}, "source": ["Next we take the non reasoning dataset and convert it to conversational format as well.\n", "\n", "We have to use <PERSON><PERSON><PERSON><PERSON>'s `standardize_sharegpt` function to fix up the format of the dataset first."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nXBFaeQHfSxp"}, "outputs": [], "source": ["from unsloth.chat_templates import standardize_sharegpt\n", "dataset = standardize_sharegpt(non_reasoning_dataset)\n", "\n", "non_reasoning_conversations = tokenizer.apply_chat_template(\n", "    dataset[\"conversations\"],\n", "    tokenize = False,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Q9FcosGvfdNr"}, "source": ["Let's see the first row"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pb0hbEekfeqf"}, "outputs": [], "source": ["non_reasoning_conversations[0]"]}, {"cell_type": "markdown", "metadata": {"id": "c_0L18QMfot4"}, "source": ["Now let's see how long both datasets are:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "unDFuUq1foWj"}, "outputs": [], "source": ["print(len(reasoning_conversations))\n", "print(len(non_reasoning_conversations))"]}, {"cell_type": "markdown", "metadata": {"id": "dgknnOf7fn3e"}, "source": ["The non reasoning dataset is much longer. Let's assume we want the model to retain some reasoning capabilities, but we specifically want a chat model.\n", "\n", "Let's define a ratio of chat only data. The goal is to define some mixture of both sets of data.\n", "\n", "Let's select 25% reasoning and 75% chat based:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_szfriCBgCkU"}, "outputs": [], "source": ["chat_percentage = 0.75"]}, {"cell_type": "markdown", "metadata": {"id": "DANuEJA7gL58"}, "source": ["Let's sample the reasoning dataset by 25% (or whatever is 100% - chat_percentage)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7-e0KO9GgFy3"}, "outputs": [], "source": ["import pandas as pd\n", "non_reasoning_subset = pd.Series(non_reasoning_conversations)\n", "non_reasoning_subset = non_reasoning_subset.sample(\n", "    int(len(reasoning_conversations) * (1.0 - chat_percentage)),\n", "    random_state = 2407,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "qR-4prS_gVel"}, "source": ["Finally combine both datasets:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jfV47_SXgXH4"}, "outputs": [], "source": ["data = pd.concat([\n", "    pd.Series(reasoning_conversations),\n", "    pd.Series(non_reasoning_subset)\n", "])\n", "data.name = \"text\"\n", "\n", "from datasets import Dataset\n", "combined_dataset = Dataset.from_pandas(pd.DataFrame(data))\n", "combined_dataset = combined_dataset.shuffle(seed = 3407)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "95_Nn-89DhsL"}, "outputs": [], "source": ["from trl import SFTTrainer, SFTConfig\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = combined_dataset,\n", "    eval_dataset = None, # Can set up evaluation!\n", "    args = SFTConfig(\n", "        dataset_text_field = \"text\",\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4, # Use GA to mimic batch size!\n", "        warmup_steps = 5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps = 30,\n", "        learning_rate = 2e-4, # Reduce to 2e-5 for long training runs\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "2ejIt2xSNKKp"}, "outputs": [], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "markdown", "metadata": {"id": "M9fa371ShyhB"}, "source": ["Let's train the model! To resume a training run, set `trainer.train(resume_from_checkpoint = True)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yqxqAZ7KJ4oL"}, "outputs": [], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "pCqnaKmlO1U9"}, "outputs": [], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model via Unsloth native inference! According to the `Qwen-3` team, the recommended settings for reasoning inference are `temperature = 0.6, top_p = 0.95, top_k = 20`\n", "\n", "For normal chat based inference, `temperature = 0.7, top_p = 0.8, top_k = 20`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kR3gIAX-SM2q"}, "outputs": [], "source": ["messages = [\n", "    {\"role\" : \"user\", \"content\" : \"Solve (x + 2)^2 = 0.\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = False,\n", "    add_generation_prompt = True, # Must add for generation\n", "    enable_thinking = False, # Disable thinking\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 256, # Increase for longer outputs!\n", "    temperature = 0.7, top_p = 0.8, top_k = 20, # For non thinking\n", "    streamer = TextStreamer(tokenizer, skip_prompt = True),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "j873RMcEi9uq"}, "outputs": [], "source": ["messages = [\n", "    {\"role\" : \"user\", \"content\" : \"Solve (x + 2)^2 = 0.\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = False,\n", "    add_generation_prompt = True, # Must add for generation\n", "    enable_thinking = True, # Disable thinking\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 1024, # Increase for longer outputs!\n", "    temperature = 0.6, top_p = 0.95, top_k = 20, # For thinking\n", "    streamer = TextStreamer(tokenizer, skip_prompt = True),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upcOlWe7A1vc"}, "outputs": [], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MKX_XKs_BNZR"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = 2048,\n", "        load_in_4bit = True,\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"lora\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"lora\", token = \"\")"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "[**NEW**] To finetune and auto export to Ollama, try our [Ollama notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FqfebeAdT073"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False:\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "JcfDX13NfjBK"}, "source": ["Now, use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}