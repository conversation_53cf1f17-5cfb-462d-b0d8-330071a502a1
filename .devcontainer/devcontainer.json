{
  "$schema": "https://containers.dev/schemas/devcontainer.json",
  "name": "Y3DHub Dev Environment (Node 22 + MySQL 8)",

  "dockerComposeFile": ["docker-compose.yml"],
  "service": "app",
  "workspaceFolder": "/workspace",

  "build": {
    "dockerfile": "../Dockerfile"
  },

  "mounts": [
    "source=node_modules,target=/workspace/node_modules,type=volume",
    "source=${localWorkspaceFolder}/src,target=/workspace/src,type=bind,consistency=cached",
    "source=${localWorkspaceFolder}/.next,target=/workspace/.next,type=bind,consistency=cached"
  ],

  "customizations": {
    "vscode": {
      "extensions": [
        // Essential for this project
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "prisma.prisma",

        // GitHub integration
        "github.copilot",
        "github.copilot-chat",
        "github.vscode-pull-request-github",

        // Next.js/React development
        "ms-vscode.vscode-json",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",

        // Git tools
        "eamodio.gitlens",
        "github.vscode-github-actions",

        // Database tools
        "mtxr.sqltools",
        "mtxr.sqltools-driver-mysql",

        // Development utilities
        "ms-vscode.vscode-dotenv",
        "yzhang.markdown-all-in-one",
        "redhat.vscode-yaml",
        "ms-vscode.vscode-docker",
        "usernamehw.errorlens",
        "Gruntfuggly.todo-tree"
      ],
      "settings": {
        // Import settings from .vscode/settings.json but simplified for containers
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit",
          "source.organizeImports": "explicit"
        },
        "editor.tabSize": 2,
        "editor.insertSpaces": true,

        // TypeScript settings
        "typescript.preferences.importModuleSpecifier": "non-relative",
        "typescript.suggest.autoImports": true,
        "typescript.updateImportsOnFileMove.enabled": "always",

        // ESLint settings
        "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
        "eslint.format.enable": true,

        // File associations
        "files.associations": {
          "*.css": "tailwindcss",
          "*.tsx": "typescriptreact",
          "*.ts": "typescript"
        },

        // Git
        "git.autofetch": true,
        "git.confirmSync": false,

        // Performance
        "search.exclude": {
          "**/node_modules": true,
          "**/dist": true,
          "**/.next": true
        },

        // Disable spell check in containers
        "cSpell.enabled": false
      }
    },
    "codespaces": {
      "repositories": {
        "y3dltd/Y3DCore": {
          "permissions": { "contents": "write", "pull_requests": "write" }
        }
      },
      "openFiles": ["README.md", "src/app/page.tsx", "prisma/schema.prisma", ".cursorrules"]
    }
  },

  "forwardPorts": [3001, 3000, 3306, 4000],
  "portsAttributes": {
    "3001": { "label": "Next.js Dev Server", "onAutoForward": "notify" },
    "3000": { "label": "Next.js Production", "onAutoForward": "silent" },
    "3306": { "label": "MySQL", "onAutoForward": "silent" },
    "4000": { "label": "LiteLLM", "onAutoForward": "silent" }
  },

  "postCreateCommand": "npm install && npx prisma generate",
  "postStartCommand": "npm run db:migrate || echo 'Migration skipped'",
  "postAttachCommand": "npm run dev",
  "initializeCommand": "if [ ! -f .env ]; then cp .env.example .env 2>/dev/null || echo '.env.example not found'; fi",

  "remoteUser": "node",
  "updateRemoteUserUID": true,
  "remoteEnv": {
    "DATABASE_URL": "${localEnv:DATABASE_URL}",
    "NEXTAUTH_SECRET": "${localEnv:NEXTAUTH_SECRET}",
    "NEXTAUTH_URL": "${localEnv:CODESPACE_NAME:+https://${localEnv:CODESPACE_NAME}-3001.${localEnv:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}}${localEnv:CODESPACE_NAME:-http://localhost:3001}",
    "NODE_ENV": "development",
    "CODESPACES": "${localEnv:CODESPACES}",
    "GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN": "${localEnv:GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}"
  },

  "hostRequirements": {
    "cpus": 4,
    "memory": "8gb",
    "storage": "32gb"
  },

  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "22"
    },
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  }
}
