version: '3.8'

services:
  db:
    image: mysql:8.0
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: secret_password
      MYSQL_USER: node
      MYSQL_PASSWORD: secret_password
      MYSQL_DATABASE: myapp_dev
    volumes:
      - db_data:/var/lib/mysql
    ports: ['3306:3306']
    healthcheck:
      test: [CMD, mysqladmin, ping, -h, localhost, --silent]
      interval: 5s
      timeout: 5s
      retries: 5

  app:
    build:
      context: ..
      dockerfile: Dockerfile
      image: ghcr.io/y3dltd/y3dhub-dev:latest
    command: sleep infinity
    working_dir: /workspace
    volumes:
      - ..:/workspace:cached
      - node_modules:/workspace/node_modules
      - npm_cache:/home/<USER>/.npm
      - /var/run/docker.sock:/var/run/docker.sock # optional: Docker-in-Docker
    env_file:
      - ../.env
    environment:
      - NODE_ENV=development
      - NPM_CONFIG_PREFIX=/home/<USER>/.npm-global
      - GITHUB_TOKEN=${GITHUB_TOKEN:-}
    depends_on:
      db:
        condition: service_healthy
    ports: ['3002:3002']
    user: node
    cap_add: [SYS_PTRACE]
    security_opt: [seccomp:unconfined]

volumes:
  node_modules: {}
  npm_cache: {}
  db_data: {}

networks:
  default:
    name: y3dhub_network
