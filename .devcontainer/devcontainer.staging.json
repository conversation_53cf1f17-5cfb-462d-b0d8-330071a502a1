{
  "$schema": "https://containers.dev/schemas/devcontainer.json",
  "name": "Y3DHub Staging Environment (Codespaces)",

  "image": "mcr.microsoft.com/devcontainers/typescript-node:22-bullseye",
  "workspaceFolder": "/workspace",

  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "version": "22"
    },
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {}
  },

  "customizations": {
    "vscode": {
      "extensions": [
        // Core essentials for quick setup
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "prisma.prisma",
        "github.copilot",
        "github.copilot-chat",
        "eamodio.gitlens",
        "ms-vscode.vscode-json"
      ],
      "settings": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit"
        },
        "editor.tabSize": 2,
        "typescript.suggest.autoImports": true,
        "git.autofetch": true,
        "files.autoSave": "onFocusChange"
      }
    },
    "codespaces": {
      "repositories": {
        "y3dltd/Y3DCore": {
          "permissions": {
            "contents": "write",
            "pull_requests": "write",
            "actions": "read"
          }
        }
      }
    }
  },

  "forwardPorts": [3001, 3000],
  "portsAttributes": {
    "3001": {
      "label": "Next.js Dev Server",
      "onAutoForward": "openBrowser",
      "visibility": "public"
    },
    "3000": {
      "label": "Next.js Production",
      "onAutoForward": "silent"
    }
  },

  "postCreateCommand": "npm install && npx prisma generate",
  "postStartCommand": "echo 'Environment ready for staging development'",
  "postAttachCommand": "npm run dev",

  "remoteUser": "node",
  "remoteEnv": {
    "NODE_ENV": "development",
    "NEXTAUTH_URL": "https://$CODESPACE_NAME-3001.app.github.dev",
    "DATABASE_URL": "file:./dev.db"
  },

  "containerEnv": {
    "GITHUB_CODESPACE": "true"
  },

  "hostRequirements": {
    "cpus": 2,
    "memory": "4gb",
    "storage": "16gb"
  }
}
