#!/usr/bin/env python3

import os
import sys
import google.generativeai as genai

def main():
    # Get API key from environment
    api_key = os.environ.get('GOOGLE_API_KEY')
    if not api_key:
        print("Error: GOOGLE_API_KEY environment variable not set")
        sys.exit(1)
    
    print(f"Using API key: {api_key[:5]}...{api_key[-5:]}")
    
    # Configure the Gemini API
    genai.configure(api_key=api_key)
    
    # Set up the model
    model = genai.GenerativeModel('gemini-1.5-pro')
    
    try:
        # Generate content
        response = model.generate_content("Hello, please introduce yourself in one sentence.")
        
        # Print the response
        print("Response from Gemini API:")
        print(response.text)
        print("\nAPI test successful!")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
